import com.commsource.aigc.AIGCSdkCustomData
import com.commsource.aigc.AIRetouchParamProvider
import com.commsource.duffle.airetouch.AiRetouchMaterial
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.statistics.constant.MTAnalyticsConstant
import com.commsource.studio.function.airetouch.AIRetouchApi
import com.commsource.studio.function.airetouch.AIRetouchApi.UploadMediaInfo
import com.commsource.studio.function.airetouch.AIRetouchApi.UploadMediaProfile
import com.commsource.studio.function.airetouch.AiRetouchProcessor
import com.commsource.studio.function.airetouch.FaceEffect
import com.commsource.studio.function.airetouch.aigc.AIGCEffectGenerator
import com.commsource.studio.function.airetouch.aigc.AIGCException
import com.commsource.studio.function.airetouch.aigc.AIGCResult
import com.commsource.studio.function.airetouch.aigc.SimpleAIGCEffectTaskCallback
import com.commsource.util.GsonUtils
import com.commsource.util.TimeLog
import com.commsource.util.logV
import com.meitu.aigckit.base.AIGCTaskManager
import com.meitu.aigckit.base.bean.AIGCTaskBean
import com.meitu.aigckit.base.bean.AIGCTaskDownloadInfo
import com.meitu.aigckit.base.bean.AIGCTaskMediaInfo
import com.meitu.aigckit.base.callback.AIGCEffectTaskCallback
import com.meitu.aigckit.base.sdk.AIGCSdkResponse
import com.meitu.aigckit.base.task.AIGCEffectTask
import com.meitu.puff.PuffFileType
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONObject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class AIGCEffectGeneratorImpl : AIGCEffectGenerator {

    private companion object {
        const val RETURN_TYPE = "url"
    }

    override suspend fun generateEffect(
        faceEffect: FaceEffect,
        originImagePath: String?,
        effectedUrl: String?,
        uploadFaceEntity: AIRetouchApi.UploadFaceEntity
    ): AIGCResult {

        val isRemoteUrl = effectedUrl != null

        val profileJsonObject = prepareProfile(uploadFaceEntity)

        val path = effectedUrl ?: originImagePath

        if (AiRetouchProcessor.DEBUG_AI_RETOUCH) {
            "$path".logV("lzw-generateEffect")
        }

        if (path == null) {
            throw AIGCException(-1, "参数丢失")
        }

        val effectImagePath = startEffectTask(
            material = faceEffect.material,
            filePath = path,
            isRemoteUrl = isRemoteUrl,
            profileJson = profileJsonObject,
        )

        return AIGCResult(RETURN_TYPE, effectImagePath)
    }

    private fun prepareProfile(
        uploadFaceEntity: AIRetouchApi.UploadFaceEntity,
    ): JSONObject {
        val profile =
            UploadMediaInfo(
                media_extra = uploadFaceEntity,
                media_profiles = UploadMediaProfile("url")
            )

        val profileJsonObject = GsonUtils.getInstance().toJson(profile).let {
            JSONObject(it)
        }
        return profileJsonObject
    }

    private suspend fun startEffectTask(
        material: AiRetouchMaterial,
        filePath: String,
        isRemoteUrl: Boolean,
        profileJson: JSONObject,
    ) = suspendCancellableCoroutine { coroutine ->
        val task = createRemoteTaskBean(
            material = material,
            path = filePath,
            isRemoteUrl = isRemoteUrl,
            profileJson = profileJson
        )

        coroutine.invokeOnCancellation {
            AIGCTaskManager.deleteTask(task.taskId, true)
        }

        val requestTime = TimeLog.createAndStart();

        startRemoteUrlTask(
            task = task,
            callback = object : SimpleAIGCEffectTaskCallback() {
                override fun onFailed(
                    taskId: String,
                    errorCode: String,
                    extCode: String?,
                    errorMsg: String,
                    domain: String,
                    aigcResponse: AIGCSdkResponse?,
                    extraResultData: MutableMap<String, String>?
                ) {
                    logEventOnTimeConsumed(false, requestTime.get(), material.materialId)
                    AIGCTaskManager.deleteTask(task.taskId, false)
                    coroutine.resumeWithException(
                        AIGCException(
                            code = errorCode.toIntOrNull() ?: -1,
                            message = errorMsg
                        )
                    )
                }

                override fun onSuccess(
                    taskId: String,
                    result: AIGCEffectTask.Result,
                    aigcResponse: AIGCSdkResponse?
                ) {
                    //
                }

                override fun onDownloadReady(
                    taskId: String,
                    url: String,
                    urls: List<String>,
                    downloadInfoList: List<AIGCTaskDownloadInfo>?
                ) {
                    super.onDownloadReady(taskId, url, urls, downloadInfoList)
                    logEventOnTimeConsumed(true, requestTime.get(), material.materialId)
                    AIGCTaskManager.deleteTask(task.taskId, false)
                    val path = urls.getOrNull(0)
                    if (path != null) {
                        coroutine.resume(path)
                    } else {
                        coroutine.resumeWithException(
                            AIGCException(
                                code = -1,
                                message = "effect path is null."
                            )
                        )
                    }
                }
            }
        )
    }

    private fun startRemoteUrlTask(
        task: AIGCTaskBean,
        callback: AIGCEffectTaskCallback
    ) {
        AIGCTaskManager.startTask(
            startTaskBean = task,
            insertToTaskQueue = false,
            startDownloadWhenDownloadReady = false,
            autoDownload = false,
            checkCache = false,
            callback = callback
        )
    }

    private fun createRemoteTaskBean(
        material: AiRetouchMaterial,
        path: String,
        isRemoteUrl: Boolean,
        profileJson: JSONObject
    ) = AIGCTaskManager.createTaskBean(
        aigcKey = material.materialId,
        mediaInfoList = listOf(
            AIGCTaskMediaInfo().apply {
                this.path = path
                this.isPathUrl = isRemoteUrl
                this.profileJson = profileJson
                this.puffFileType = PuffFileType("lab-photo", "jpg")
            }
        ),
        videoDuration = null,
        extraParams = null,
        customData = GsonUtils.getInstance().toJson(AIGCSdkCustomData(AIRetouchParamProvider.ROUTE, null)),
        extraEffectTaskParam = hashMapOf(),
        functionName = material.materialName ?: ""
    )

    private fun logEventOnTimeConsumed(
        isSuccess: Boolean,
        requestTime: Long,
        materialId: String
    ) {

        val map: MutableMap<String, String> = HashMap(4)
        map["请求时长"] = requestTime.toString()
        map["是否成功"] = if (isSuccess) "成功" else "失败"
        map["material_id"] = materialId

        MTAnalyticsAgent.logEvent(MTAnalyticsConstant.AIBEAUTY_REQUEST_TIME, map)
    }
}


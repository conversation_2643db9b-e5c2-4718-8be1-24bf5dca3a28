package com.commsource.studio.function.hair.style

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.commsource.ad.DailyMembershipUnlocker
import com.commsource.beautyplus.R
import com.commsource.beautyplus.databinding.FragmentStudioHairSytleBinding
import com.commsource.beautyplus.fragment.BaseFragment
import com.commsource.beautyplus.router.UriConstant
import com.commsource.beautyplus.router.content
import com.commsource.camera.xcamera.cover.tips.TipsViewModel
import com.commsource.config.SubscribeConfig
import com.commsource.repository.child.hair.HairGroup
import com.commsource.repository.child.hair.HairStyle
import com.commsource.studio.ImageStudioViewModel
import com.commsource.studio.function.hair.HairViewModel
import com.commsource.util.BPLocationUtils
import com.commsource.util.ErrorNotifier
import com.commsource.util.LOGV
import com.commsource.util.ResourcesUtils
import com.commsource.util.V
import com.commsource.util.V_CallMethod
import com.commsource.util.dp
import com.commsource.util.set
import com.commsource.util.setRTL
import com.commsource.widget.FastCenterScrollLayoutManager
import com.commsource.widget.recyclerview.AdapterDataBuilder
import com.commsource.widget.recyclerview.BaseRecyclerViewAdapter
import com.meitu.common.AppContext
import com.meitu.library.util.net.NetUtils
import com.pixocial.business.duffle.repo.aihair.AIHairRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 编辑器-美发-发型-子列表
 */
class HairStyleFragment : BaseFragment() {

    val mAdapter by lazy { BaseRecyclerViewAdapter(requireContext()) }

    val mLayoutManager by lazy {
        FastCenterScrollLayoutManager(
            context,
            LinearLayoutManager.HORIZONTAL,
            false
        )
    }

    val hairViewModel by lazy { ViewModelProvider(requireParentFragment())[HairViewModel::class.java] }

    val tipsViewModel by lazy { ViewModelProvider(requireParentFragment())[TipsViewModel::class.java] }

    val studioViewModel by lazy { ViewModelProvider(ownerActivity)[ImageStudioViewModel::class.java] }

    val mViewBinding by lazy {
        FragmentStudioHairSytleBinding.inflate(
            layoutInflater,
            null,
            false
        )
    }

    val none = "none"

    var group: HairGroup? = null

    var currentPosition: Int? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = mViewBinding.root


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        lifecycleScope.launch(Dispatchers.IO) {
            AIHairRepo.fetchAIHairMaterial().collect {
                it?.data?.first()
            }
        }
        mViewBinding.rvChild.layoutManager = mLayoutManager
        mViewBinding.rvChild.adapter = mAdapter
        mViewBinding.rvChild.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                if (parent.getChildAdapterPosition(view) == mAdapter.itemCount - 1) {
                    outRect.setRTL(0, 0, 13.dp(), 0)
                } else {
                    outRect.setRTL(0, 0, 0, 0)
                }
            }
        })

        hairViewModel.styleSelectEvent.observe(viewLifecycleOwner) {
            if (it.first == -1)
                return@observe

//            "HairProcess  item $it".LOGV()
            if (it.first == 0) {
                mAdapter.currentSelectEntity = none
                if (it.second) {
                    mViewBinding.rvChild.smoothScrollToPosition(0)
                }
            } else {
                currentPosition = it.first
                mAdapter.currentSelectEntity = mAdapter.getEntityByPosition(it.first)
                if((mAdapter.currentSelectEntity as HairStyle).isCrimps()){
                    hairViewModel.showManualEvent.postValue(true)
                }
                if (it.second) {
                    mViewBinding.rvChild.smoothScrollToPosition(it.first)
                }
            }
        }

        hairViewModel.hairStyleRetryEvent.observe(viewLifecycleOwner) {
            if (it && currentPosition != null) {
                styleClick(currentPosition!!, mAdapter.currentSelectEntity as HairStyle)
            }
        }
        hairViewModel.showFloatingTips.observe(viewLifecycleOwner) {
            tipsViewModel.showFloatingTips(
                it.second.name,
                isLeftToRight = it.first < currentPosition!!
            )
        }

        mAdapter.setOnEntityClickListener(HairStyle::class.java) { position, entity ->
            if (mAdapter.currentPosition == position) {
                "重复点击。currentPosition $currentPosition  position  $position".LOGV()
            } else {
                styleClick(position, entity)
            }
            false
        }
        //选中none
        mAdapter.setOnEntityClickListener(String::class.java) { position, _ ->
            if (mAdapter.currentSelectEntity != null && mAdapter.currentSelectEntity != none) {
                mAdapter.currentSelectEntity = none
                hairViewModel.showProUnlcokEvent.set(Pair(-1, null))
                mViewBinding.rvChild.smoothScrollToPosition(position)
                hairViewModel.hairProcess.applyStyle(null, 0)
            }
            false
        }

        hairViewModel.hairStyleRollbackEvent.observe(viewLifecycleOwner) {
            if (it) {
                hairViewModel.styleSelectEvent.set(Pair(hairViewModel.hairProcess.styleHairProcess.afterRenderPosition,true))
            }
        }
        initData()
    }

    private fun initData() {
        mAdapter.updateItemEntities(
            AdapterDataBuilder.create()
                .addEntities(arrayListOf(none), HairStyleNoneViewHolder::class.java)
                .addEntities(
                    styles,
                    HairStyleViewHolder::class.java
                )
                .build()
        )

        hairViewModel.protocolJumpEvent.observe(viewLifecycleOwner) {
            if (studioViewModel.routerEntity?.lastPathSegment == UriConstant.PATH_F_HAIR_YO) {
                val content = studioViewModel.routerEntity?.content
                run breakForeach@{
                    styles.forEachIndexed { index, hairStyle ->
                        "protocolJumpEvent=${hairStyle.id},content=${content}".LOGV()
                        if (hairStyle.id == content) {
                            styleClick(index + 1, hairStyle)
                        }
                    }
                }
            }
        }
    }

    private fun styleClick(position: Int, entity: HairStyle) {
        "protocolJumpEvent click $position  face count ${hairViewModel.hairProcess.faceCount}".V_CallMethod()

        if (!NetUtils.canNetworking(AppContext.context)
            && !hairViewModel.hairProcess.styleHairProcess.hasCache(entity)
        ) {
            ErrorNotifier.showNetworkErrorToast()
            return
        }

        if (!hairViewModel.hasFace()) {
            hairViewModel.hairProcess.guideHairProcess.noFaceTip()
            return
        }

        if ((entity.isCrimps() || entity.isStraightSoft()) && hairViewModel.isMulFace()) {
            // 直柔 ai卷发 关闭多人脸，其他显示多人脸
            if (entity.isCrimps()) {
                hairViewModel.supportMultiFaceTipEvent.set(Pair(true,0))
            } else {
                hairViewModel.supportMultiFaceTipEvent.set(Pair(true,1))
            }
            return
        }
        currentPosition = mAdapter.currentPosition
        mAdapter.currentSelectEntity = entity
        mViewBinding.rvChild.smoothScrollToPosition(position)

        if (!DailyMembershipUnlocker.hasUserEarnReward() && entity.isCrimps() && !SubscribeConfig.isSubValid()) {
            // 卷发未解锁
            hairViewModel.hairProcess.cacheStack.cacheCrimpsHairStyle = entity
            hairViewModel.showProUnlcokEvent.postValue(Pair(position, entity))
            return
        }
        // 卷发解锁
        hairViewModel.hairProcess.cacheStack.cacheCrimpsHairStyle = null
        hairViewModel.showProUnlcokEvent.set(Pair(-1, entity))

        if(entity.isCrimps()){
            "ai发型，缓存手动模式原图".V()
            hairViewModel.hairProcess.manualStyleProcess.cacheOriginImage()
        }
        hairViewModel.hairProcess.applyStyle(entity, position)

    }


    companion object {
        fun newInstance(): HairStyleFragment {
            return HairStyleFragment().apply {
            }
        }
    }

    private val styles = mutableListOf<HairStyle>().apply {
        //  直柔
        add(
            HairStyle(
                "BP_HRD_00000007",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_straight_soft
                } else {
                    R.drawable.edit_studio_hair_ea_straight_soft
                },
                ResourcesUtils.getString(R.string.t_hairstyle_straight_soft),
                "",
                straightType = 2
            )
        )
        // 直发
        add(
            HairStyle(
                "BP_HRD_00000005",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_straight
                } else {
                    R.drawable.edit_studio_hair_ea_straight
                },
                ResourcesUtils.getString(R.string.v77170_E_1), "", crimpsType = 3
            )
        )
        // 长发
        add(
            HairStyle(
                "BP_HRD_00000006", if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_long
                } else {
                    R.drawable.edit_studio_hair_ea_long
                }, ResourcesUtils.getString(R.string.t_hairstyle_long), ""
            )
        )
        // 自然大波浪
        add(
            HairStyle(
                "BP_HRD_00000009",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_crimps_large
                } else {
                    R.drawable.edit_studio_hair_ea_crimps_large
                }, ResourcesUtils.getString(R.string.v77170_E_2), "", crimpsType = 0
            )
        )
        //中分
        add(
            HairStyle(
                "BP_HRD_00000008", if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_point
                } else {
                    R.drawable.edit_studio_hair_ea_point
                }, ResourcesUtils.getString(R.string.t_hairstyle_middle_part), "", 2,
                isMember = false
            )
        )
        //薄刘海
        add(
            HairStyle(
                "BP_HRD_00000002", if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_thin_bangs
                } else {
                    R.drawable.edit_studio_hair_ea_thin_bangs
                }, ResourcesUtils.getString(R.string.t_hairstyle_thin_bangs), "", 5
            )
        )
        //斜刘海
        add(
            HairStyle(
                "BP_HRD_00000003", if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_side_fringe_one
                } else {
                    R.drawable.edit_studio_hair_ea_side_fringe_one
                }, ResourcesUtils.getString(R.string.t_hairstyle_side_bangs1), "", 3
            )
        )
        // 中等卷
        add(
            HairStyle(
                "BP_HRD_00000010",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_crimps_medium
                } else {
                    R.drawable.edit_studio_hair_ea_crimps_medium
                }, ResourcesUtils.getString(R.string.v77170_E_3), "", crimpsType = 1
            )
        )
        // 小卷
        add(
            HairStyle(
                "BP_HRD_00000011",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_crimps_small
                } else {
                    R.drawable.edit_studio_hair_ea_crimps_small
                }
                , ResourcesUtils.getString(R.string.v77170_E_4), "", crimpsType = 2
            )
        )
        //斜刘海2
        add(
            HairStyle(
                "BP_HRD_00000004",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_side_fringe_two
                } else {
                    R.drawable.edit_studio_hair_ea_side_fringe_two
                },
                ResourcesUtils.getString(R.string.t_hairstyle_side_bangs2),
                "",
                4,
                isMember = false
            )
        )
        //齐刘海
        add(
            HairStyle(
                "BP_HRD_00000001",
                if (BPLocationUtils.isLocationAsia(AppContext.context)) {
                    R.drawable.edit_studio_hair_neat_bangs
                } else {
                    R.drawable.edit_studio_hair_ea_neat_bangs
                },
                ResourcesUtils.getString(R.string.t_hairstyle_blunt_bangs),
                "",
                0,
                isMember = false
            )
        )
    }
}
package com.commsource.applanuch

import android.app.Application
import com.commsource.aigc.AIGCSdkCustomData
import com.commsource.aigc.AIGCSdkParamRegistry
import com.commsource.beautyplus.BuildConfig
import com.commsource.config.ApplicationConfig
import com.commsource.config.SubscribeConfig
import com.commsource.statistics.MTAnalyticsAgent
import com.commsource.util.AppTools
import com.commsource.util.BPLocationUtils
import com.commsource.util.LanguageUtil
import com.commsource.util.logV
import com.meitu.aigckit.base.IAIGCKitSupport
import com.meitu.aigckit.base.bean.AIGCSdkParam
import com.meitu.aigckit.base.bean.AIGCTaskBean
import com.meitu.aigckit.base.config.AIGCKitConfig
import com.meitu.aigckit.base.constant.AIGCVipConstant
import com.meitu.aigckit.base.delegate.IAIGCSdkParamDelegate
import com.meitu.common.AppContext
import com.meitu.library.mtaigc.MtAigc
import com.meitu.puff.PuffConfig
import org.json.JSONObject

class BeautyAIKitLaunchTask : AbsLaunchTask() {

    override fun onInitial(app: Application) {
        with(AIGCKitConfig) {
            apiEnv =
                if (ApplicationConfig.isTestMaterialEnvironment()) MtAigc.ApiEnv.PIXOCIAL_PRE else MtAigc.ApiEnv.PIXOCIAL_API

            PuffConfig.GLOBAL_API_ENV = PuffConfig.Constant.ApiEnv.PIXOCIAL
            //是否测试环境
            debug = ApplicationConfig.isTestMaterialEnvironment()
            //是否打开日志
            logEnable = AppTools.isDebug()

            sdkParamDelegate = object : IAIGCSdkParamDelegate {
                override suspend fun requestAIGCSdkParam(taskBean: AIGCTaskBean): AIGCSdkParam? {
                    val customData = taskBean.getCustomDataObj<AIGCSdkCustomData>()
                    if (customData == null) {
                        "customData为空，无法尝试获得AIGC参数.".logV("AIKit")
                        return null
                    }
                    val param = AIGCSdkParamRegistry.provideSdkParam(customData.route, taskBean)
                    if (param == null) {
                        "无法获得AIGC参数.".logV("AIKit")
                        return null
                    }

                    return param
                }

                override fun requestAll() {

                }
            }

            //最后调用初始化
            initOnUi(app, AIGCKitSupport())
        }
    }

    private fun getTrackContent(
        materialId: String
    ): String {
        val json = JSONObject().apply {
            put("function", JSONObject().apply {
                put("name", "焕颜")
                put("effect", materialId)
            })

            // 目前只有AI焕颜接入，固定参数
            put("position", JSONObject().apply {
                put("level1", "修图")
                put("level2", "人像")
                put("level3", "焕颜")
            })

            put("media_type", "photo")
            put("res_media_type", "photo")
            put("ext_info", JSONObject().apply {
                put("number", 1)
            })
        }

        return json.toString()
    }
}


class AIGCKitSupport : IAIGCKitSupport {

    override fun getAIGCAppKey(): String = BuildConfig.AIGC_APP_KEY

    override fun getAccessToken(): String = ""

    override fun getCountryCode(): String =
        BPLocationUtils.getCertainCountryCode(AppContext.context)

    override fun getGid(): String = MTAnalyticsAgent.getOverseaGid().orEmpty()

    override fun getLanguage(): String = LanguageUtil.getLanguage(AppContext.context, true)

    override fun getMemberLevel(): Int =
        if (SubscribeConfig.isSubValid()) AIGCVipConstant.MEMBER_VIP else AIGCVipConstant.MEMBER_NORMAL

    override fun getPuffUploadKey(): String = BuildConfig.PUFF_APP_KEY

    override fun getUid(): String = ""

    override fun uploadStatistics(eventName: String, map: MutableMap<String, String>) {

    }
}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="./favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1" />
    <title>BeautyPlus</title>
    <script type="module" crossorigin src="./assets/index.87c19254.js"></script>
    <link rel="modulepreload" href="./assets/vendor.a7738174.js">
    <link rel="stylesheet" href="./assets/index.c9c4ea2e.css">
    <script type="module">!function(){try{new Function("m","return import(m)")}catch(o){console.warn("vite: loading legacy build because dynamic import is unsupported, syntax error above should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}}();</script>
  </head>
  <body>
    <div id="app"></div>


  <script>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script id="vite-legacy-polyfill" src="./assets/polyfills-legacy.998d5cab.js"></script>
    <script id="vite-legacy-entry" src="./assets/index-legacy.f0f4c6b2.js"></script>
</body>
</html>
<!--<script src="https://h5.mr.meitu.com/public/js/vconsole.min.js"></script>-->
<!--<script>-->
<!--  new VConsole();-->
<!--</script>-->

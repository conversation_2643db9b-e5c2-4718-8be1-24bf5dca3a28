import{_ as a}from"./index.87c19254.js";import{o as e,f as s,g as l,h as n,i as t,j as c,k as f,n as i,t as A,p as u,l as p,m as d}from"./vendor.a7738174.js";const o={name:"Leaf"},y={class:"mt-leaf show"},v=[l('<span class="leaf leaf1 leaf-ani-1" style="left:60%;" data-v-ec2e2ca8></span><span class="leaf leaf2 leaf-ani-2" style="left:-5%;" data-v-ec2e2ca8></span><span class="leaf leaf3 leaf-ani-3" style="left:85%;" data-v-ec2e2ca8></span><span class="leaf leaf4 leaf-ani-8" style="left:10%;" data-v-ec2e2ca8></span><span class="leaf leaf4 leaf-ani-7" style="left:80%;" data-v-ec2e2ca8></span><span class="leaf leaf5 leaf-ani-9" style="left:70%;" data-v-ec2e2ca8></span><span class="leaf leaf6 leaf-ani-4" style="left:-40px;top:400px;" data-v-ec2e2ca8></span><span class="leaf leaf4 leaf-ani-5" style="left:30%;" data-v-ec2e2ca8></span><span class="leaf leaf4 leaf-ani-6" style="left:40%;" data-v-ec2e2ca8></span>',9)];var U=a(o,[["render",function(a,l,n,t,c,f){return e(),s("div",y,v)}],["__scopeId","data-v-ec2e2ca8"]]);const B=a=>(u("data-v-68291248"),a=a(),p(),a),O={class:"container"},F=B((()=>f("div",{class:"bg-gift"},null,-1))),g={class:"content"},k=B((()=>f("div",{class:"product"},[f("img",{class:"logo",src:"data:image/png;base64,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",alt:""}),d(" BeautyPlus Premium ")],-1))),r=["innerHTML"],T={class:"tips"};var w=a({setup(a){const l=n("en");return t((()=>{l.value=window.lang||"en"})),(a,n)=>(e(),s("div",O,[c(U),F,f("div",g,[k,f("div",{class:i(["title",l.value]),innerHTML:a.$t("lucky")},null,10,r),f("div",T,A(a.$t("tips")),1)])]))}},[["__scopeId","data-v-68291248"]]);export{w as default};

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ID</key>
	<integer>1001</integer>
	<key>FilterPart</key>
	<array>
		<dict>
			<key>Type</key>
			<string>AnattaBeauty</string>
			<key>Filters</key>
			<array>
				<dict>
					<key>Name</key>
					<string>SourceInput</string>
				</dict>
				<!-- 自动祛斑祛痘-->
				<dict>
					<key>Name</key>
					<string>AcneClean</string>
					<key>IsOnlyPreview</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>SourceInput</string>
					</array>
				</dict>
				<!-- 手动祛斑祛痘 -->
				<dict>
					<key>Name</key>
					<string>AcneCleanManual</string>
					<!-- 是否必须额头点 -->
					<key>IsNeedHeadPoints</key>
					<string>1</string>
					<!-- 是否必须全脸分割 -->
					<key>IsNeedFullSegment</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>AcneClean</string>
					</array>
				</dict>
				<!-- 妆容加深-->
				<dict>
					<key>Name</key>
					<string>FaceSharpen</string>
					<key>Type</key>
					<string>Video</string>
					<key>FromSource</key>
					<array>
						<string>AcneCleanManual</string>
					</array>
				</dict>
				<!-- 去油光 -->
				<dict>
					<key>Name</key>
					<string>CleanShiny</string>
					<key>Type</key>
					<string>Video</string>
					<key>maskOffset</key>
					<string>5.0</string>
					<key>matteOffset</key>
					<string>4.0</string>
					<key>FromSource</key>
					<array>
						<string>FaceSharpen</string>
					</array>
				</dict>
				<!-- 面部丰盈 -->
				<dict>
					<key>Name</key>
					<string>CheekFillers</string>
					<key>Type</key>
					<string>Video</string>
					<key>WithHeadPoint</key>
					<string>1</string>
					<key>WithFaceParsingMask</key>
					<string>1</string>
					<key>SkinToneCorrectionType</key>
					<string>1</string>
					<key>IsMiniFaceLimit</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>CleanShiny</string>
					</array>
				</dict>
				<!-- 面部提亮 -->
				<dict>
					<key>Name</key>
					<string>ShadowSmoothBright</string>
					<key>IsMiniFaceLimit</key>
					<string>0</string>
					<key>IsNeedShadowLightDarkAlpha</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>CheekFillers</string>
					</array>
				</dict>
				<!-- 肤色 -->
				<dict>
					<key>Name</key>
					<string>FaceColor</string>
					<key>Type</key>
					<string>SingleLookup</string>
					<key>LutPath</key>
					<string>Anatta/lookup_table_512_white.png</string>
					<key>Size</key>
					<string>64</string>
					<key>FromSource</key>
					<array>
						<string>ShadowSmoothBright</string>
					</array>
				</dict>
				<!-- 磨皮 -->
				<dict>
					<key>Name</key>
					<string>SkinSmooth</string>
					<key>Type</key>
					<string>Video</string>
					<key>ContourMaskMeshType</key>
					<string>8</string>
					<key>IsNeedFaceAndNeckMask</key>
					<string>1</string>
					<key>FaceEdgeDevelop</key>
					<string>0</string>
					<key>IsNeedCropFace</key>
					<string>1</string>
					<key>WithHeadPoint</key>
					<string>1</string>
					<key>WithFaceParsingMask</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>FaceColor</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>SkinSmoothManual</string>
					<!-- 是否必须额头点 -->
					<key>IsNeedHeadPoints</key>
					<string>1</string>
					<!-- 是否必须全脸分割 -->
					<key>IsNeedFullSegment</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>SkinSmooth</string>
					</array>
				</dict>
				<!-- 清晰 -->
				<dict>
					<key>Name</key>
					<string>Sharpen</string>
					<key>Type</key>
					<string>MySharpenSkinMask</string>
					<key>FaceEdgeDevelop</key>
					<string>0</string>
					<key>ExceptFaceSkin</key>
					<string>0</string>
					<key>FromSource</key>
					<array>
						<string>SkinSmoothManual</string>
						<string>FaceColor</string>
					</array>
				</dict>
				<!-- 亮眼 -->
				<dict>
					<key>Name</key>
					<string>BrightEye</string>
					<key>Type</key>
					<string>VideoPupil</string>
					<key>IsFaceSharpen</key>
					<string>1</string>
					<key>FromSource</key>
					<array>
						<string>Sharpen</string>
					</array>
				</dict>
				<!-- 立体 + 皮肤纹理-->
				<dict>
					<key>Name</key>
					<string>ShadowLight</string>
					<key>Type</key>
					<string>2DSoftLightTexture</string>
					<key>MaskPath</key>
					<string>SoftLight2D/SoftLight.png</string>
					<key>FromSource</key>
					<array>
						<string>BrightEye</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>ResultOutput</string>
					<key>FromSource</key>
					<array>
						<string>ShadowLight</string>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

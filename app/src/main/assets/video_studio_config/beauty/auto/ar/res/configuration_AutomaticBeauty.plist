<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ID</key>
	<integer>1001</integer>
	<key>FilterPart</key>
	<array>
		<dict>
			<key>Type</key>
			<string>AnattaBeauty</string>
			<key>Filters</key>
			<array>
				<dict>
					<key>Name</key>
					<string>SourceInput</string>
				</dict>
				<!-- 匀肤 -->
				<dict>
					<key>Name</key>
					<string>DodgeBurn</string>
					<key>Type</key>
					<string>0</string>
					<key>Switch</key>
					<string>1</string>
					<key>Alpha</key>
					<string>1.0</string>
					<key>DeviceType</key>
					<string>2</string>
					<key>FromSource</key>
					<array>
						<string>SourceInput</string>
					</array>
				</dict>
				<!-- 祛斑祛痘开关 -->
				<dict>
					<key>Name</key>
					<string>FleckFlawClean</string>
					<key>NeedExternSkinMaskData</key>
					<string>0</string>
					<key>FleckFlawSwitch</key>
					<string>0</string>
					<key>FromSource</key>
					<array>
						<string>DodgeBurn</string>
					</array>
				</dict>
				<!-- 光影平整去法令纹&祛泪沟 -->
				<dict>
					<key>Name</key>
					<string>FacialShadowSmooth</string>
					<key>TearTroughSwitch</key>
					<string>0</string>
					<key>TearTroughAlpha</key>
					<real>0.7</real>
					<key>LaughLineSwitch</key>
					<string>1</string>
					<key>LaughLineAlpha</key>
					<real>0.3</real>
					<key>ShadowSmoothAlpha</key>
					<real>0.3</real>
					<key>SkinToneCorrectionType</key>
					<string>0</string>
					<key>IsOnlyRemoveShadow</key>
					<real>1</real>
					<key>FromSource</key>
					<array>
						<string>FleckFlawClean</string>
					</array>
				</dict>
				<!-- 提亮去法令纹&祛泪沟 -->
				<dict>
					<key>Name</key>
					<string>ShadowSmoothBright</string>
					<key>IsNeedFaceDarkAlpha</key>
					<string>1</string>
					<key>RemovePouchSwitch</key>
					<string>1</string>
					<key>RemovePouchAlpha</key>
					<real>0.7</real>
					<key>LaughLineSwitch</key>
					<string>1</string>
					<key>LaughLineAlpha</key>
					<real>0.3</real>
					<key>FromSource</key>
					<array>
						<string>FacialShadowSmooth</string>
					</array>
				</dict>
				<!-- 亮眼&白牙 -->
				<dict>
					<key>Name</key>
					<string>BrightEye</string>
					<key>BrightEyeType</key>
					<string>2</string>
					<key>BrightEyeSwitch</key>
					<string>1</string>
					<key>BrightEyeAlpha</key>
					<string>0.8</string>
					<key>WhiteTeethSwitch</key>
					<string>1</string>
					<key>WhiteTeethAlpha</key>
					<string>0.5</string>
					<key>FromSource</key>
					<array>
						<string>ShadowSmoothBright</string>
					</array>
				</dict>
				<!-- 锐化 -->
				<dict>
					<key>Name</key>
					<string>Sharpen</string>
					<key>Type</key>
					<string>MySharpenSkinMask</string>
					<key>Switch</key>
					<string>1</string>
					<key>Alpha</key>
					<string>0.6</string>
					<key>FromSource</key>
					<array>
						<string>BrightEye</string>
						<string>SourceInput</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>ResultOutput</string>
					<key>FromSource</key>
					<array>
						<string>Sharpen</string>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

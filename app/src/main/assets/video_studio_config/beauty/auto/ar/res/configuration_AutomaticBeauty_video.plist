<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ID</key>
	<integer>1001</integer>
	<key>FilterPart</key>
	<array>
		<dict>
			<key>Type</key>
			<string>AnattaBeauty</string>
			<key>Filters</key>
			<array>
				<dict>
					<key>Name</key>
					<string>SourceInput</string>
				</dict>
				<!-- 匀肤 低端机建议用祛斑祛痘替代 -->
				<dict>
					<key>Name</key>
					<string>DodgeBurnVideo</string>
					<key>Switch</key>
					<string>0</string>
					<key>Alpha</key>
					<string>0.0</string>
					<!-- 匀肤效果类型 0-最佳 1-修图 2-实时 3-fast 4-realtime_wink-->
					<key>Type</key>
					<string>0</string>
					<!-- 匀肤设备类型 -1-none 0-cpu 1-gl 2-coreml -->
					<key>DeviceType</key>
					<string>2</string>
					<key>FromSource</key>
					<array>
						<string>SourceInput</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>AcneClean</string>
					<key>IsOnlyPreview</key>
					<string>1</string>
					<key>Switch</key>
					<string>0</string>
					<key>Alpha</key>
					<string>0.0</string>
					<key>FromSource</key>
					<array>
						<string>DodgeBurnVideo</string>
					</array>
				</dict>
				<!-- 细节(妆容加深) -->
				<dict>
					<key>Name</key>
					<string>FaceSharpen</string>
					<key>Type</key>
					<string>Video</string>
					<key>Switch</key>
					<string>0</string>
					<key>Alpha</key>
					<string>0.0</string>
					<key>FromSource</key>
					<array>
						<string>AcneClean</string>
					</array>
				</dict>
				<!-- 磨皮 -->
                <dict>
                    <key>Name</key>
                    <string>SkinSmooth</string>
                    <key>Type</key>
                    <string>Video</string>
                    <key>Switch</key>
					<string>0</string>
					<key>Alpha</key>
					<string>0.0</string>
                    <key>ContourMaskMeshType</key>
                    <string>7</string>
                    <key>IsNeedFaceAndNeckMask</key>
                    <string>1</string>
                    <key>FaceEdgeDevelop</key>
                    <string>0</string>
                    <key>IsNeedCropFace</key>
                    <string>1</string>
                    <key>WithHeadPoint</key>
                    <string>1</string>
                    <key>WithFaceParsingMask</key>
                    <string>1</string>
                    <key>FromSource</key>
                    <array>
                        <string>FaceSharpen</string>
                    </array>
                </dict>
				<!-- 光影平整去法令纹&祛泪沟 -->
				<dict>
					<key>Name</key>
					<string>FacialShadowSmooth</string>
					<key>TearTroughSwitch</key>
					<string>0</string>
					<key>TearTroughAlpha</key>
					<real>0.0</real>
					<key>LaughLineSwitch</key>
					<string>0</string>
					<key>LaughLineAlpha</key>
					<real>0.0</real>
					<key>ShadowSmoothAlpha</key>
					<real>0.0</real>
					<key>SkinToneCorrectionType</key>
					<string>0</string>
					<key>IsOnlyRemoveShadow</key>
					<real>1</real>
					<key>FromSource</key>
					<array>
						<string>SkinSmooth</string>
					</array>
				</dict>
				<!-- 提亮去法令纹&祛泪沟 -->
				<dict>
					<key>Name</key>
					<string>ShadowSmoothBright</string>
					<key>IsNeedFaceDarkAlpha</key>
					<string>0.0</string>
					<key>RemovePouchSwitch</key>
					<string>0</string>
					<key>RemovePouchAlpha</key>
					<real>0.0</real>
					<key>LaughLineSwitch</key>
					<string>0</string>
					<key>LaughLineAlpha</key>
					<real>0.0</real>
					<key>FromSource</key>
					<array>
						<string>FacialShadowSmooth</string>
					</array>
				</dict>
				<!-- 清晰 -->
				<dict>
					<key>Name</key>
					<string>Sharpen</string>
					<key>Type</key>
					<string>MySharpenSkinMask</string>
					<key>FaceEdgeDevelop</key>
					<string>0</string>
					<key>ExceptFaceSkin</key>
					<string>0</string>
					<key>Switch</key>
					<string>0</string>
					<key>Alpha</key>
					<string>0.0</string>
					<key>FromSource</key>
					<array>
						<string>ShadowSmoothBright</string>
						<string>FacialShadowSmooth</string>
					</array>
				</dict>
				<!-- 亮眼&白牙 -->
				<dict>
					<key>Name</key>
					<string>BrightEye</string>
					<key>BrightEyeType</key>
					<string>2</string>
					<key>BrightEyeSwitch</key>
					<string>0</string>
					<key>BrightEyeAlpha</key>
					<string>0.0</string>
					<key>WhiteTeethSwitch</key>
					<string>0</string>
					<key>WhiteTeethAlpha</key>
					<string>0.0</string>
					<key>FromSource</key>
					<array>
						<string>Sharpen</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>ResultOutput</string>
					<key>FromSource</key>
					<array>
						<string>BrightEye</string>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

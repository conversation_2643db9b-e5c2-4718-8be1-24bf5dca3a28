<?xml version="0.00" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 0.00//EN" "http://www.apple.com/DTDs/PropertyList-0.00.dtd">
<plist version="0.00">
<array>
	<dict>
		<key>FacePart</key>
		<array>
			<!-- order 亮度 对比度 高光 阴影  饱和度 锐化  色温  色调  褪色 暗角 颗粒 光感 HSL-->
			<dict>
				<key>Type</key>
				<string>Tone</string>
				<key>Path</key>
				<string>512x512#Lut.png</string>
				<key>ToneArray</key>
				<array>
					<dict>
						<key>ToneType</key><string>Light</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>light_100.png</string>
								<key>path1</key><string>light_100_rever.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Comparison</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>comparison_100.png</string>
								<key>path1</key><string>comparison_100_rever.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>HighLight</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>highlight_100.jpg</string>
								<key>path1</key><string>highlight_100_rever.jpg</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Shadows</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>shadows_100.jpg</string>
								<key>path1</key><string>shadows_100_rever.jpg</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Saturation</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>saturation_100.png</string>
								<key>path1</key><string>saturation_100_rever.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Temperature</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>temperature_100.png</string>
								<key>path1</key><string>temperature_100_rever.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Tone</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>Tone_magenta.png</string>
								<key>path1</key><string>Tone_blue.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Fade</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>fade_100.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>DarkCorner</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>vignette1.jpg</string>
								<key>path1</key><string>PSMultiple.png</string>
								<key>path2</key><string>vignette2.png</string>
								<key>path3</key><string>PSScreen.png</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>Sharpen</string>
						<key>Alpha</key><string>0</string>
					</dict>
					<dict>
						<key>ToneType</key><string>Particle</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>grainV1.jpg</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key><string>LightSensation</string>
						<key>Alpha</key><string>0</string>
						<key>Path</key>
						<array>
							<dict>
								<key>path0</key><string>lightsensation_100.jpg</string>
								<key>path1</key><string>lightsensation_100_rever.jpg</string>
							</dict>
						</array>
					</dict>
					<dict>
						<key>ToneType</key>
						<string>HSL</string>
						<key>HSLArray</key>
						<array>
							<dict>
								<key>HslType</key>
								<string>Hue</string>
								<key>Red</key>
								<string>0.00</string>
								<key>Orange</key>
								<string>0.00</string>
								<key>Yellow</key>
								<string>0.00</string>
								<key>Green</key>
								<string>0.00</string>
								<key>Cyan</key>
								<string>0.00</string>
								<key>Blue</key>
								<string>0.00</string>
								<key>Purple</key>
								<string>0.00</string>
								<key>Magenta</key>
								<string>0.00</string>
							</dict>
							<dict>
								<key>HslType</key>
								<string>Saturation</string>
								<key>Red</key>
								<string>0.00</string>
								<key>Orange</key>
								<string>0.00</string>
								<key>Yellow</key>
								<string>0.00</string>
								<key>Green</key>
								<string>0.00</string>
								<key>Cyan</key>
								<string>0.00</string>
								<key>Blue</key>
								<string>0.00</string>
								<key>Purple</key>
								<string>0.00</string>
								<key>Magenta</key>
								<string>0.00</string>
							</dict>
							<dict>
								<key>HslType</key>
								<string>Lightness</string>
								<key>Red</key>
								<string>0.00</string> 
								<key>Orange</key>
								<string>0.00</string>
								<key>Yellow</key>
								<string>0.00</string>
								<key>Green</key>
								<string>0.00</string>
								<key>Cyan</key>
								<string>0.00</string>
								<key>Blue</key>
								<string>0.00</string>
								<key>Purple</key>
								<string>0.00</string>
								<key>Magenta</key>
								<string>0.00</string>
							</dict>
						</array>
					</dict>
				</array>
			</dict>
		</array>
	</dict>
</array>
</plist>

{"list": [{"ad_type": 6, "id": 99909, "is_video": 0, "link": "beautyplus://groupselfies", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "auto_camera", "test_b": {"color_value": "#E4DBD5", "content": "Take group selfies with your friends.", "title_1": "", "title_2": "Group Selfie", "typesetting": 9, "icon_imageName": "icon_homeBannerFrame_AIBeauty"}, "topping": 1, "version_control": 0, "video": "", "weight": 9, "weight_traffic_a": 1, "weight_traffic_b": 1}, {"ad_type": 6, "id": 99908, "is_video": 0, "link": "beautyplus://easyeditor", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "easy_editor", "test_b": {"color_value": "#C7C4BF", "content": "Easy editor to perfect your face. Tap to edit!", "title_1": "", "title_2": "Easy Editor", "typesetting": 9}, "topping": 0, "version_control": 0, "video": "", "weight": 8, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99907, "is_video": 0, "link": "beautyplus://aiEditor?mode=imgRestore", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "old_photo", "test_b": {"color_value": "#E9BAB4", "content": "Enhances Image Size and Quality", "title_1": "", "title_2": "Restore Photo", "typesetting": 9, "icon_imageName": "icon_homeBannerFrame_bokehLens"}, "topping": 0, "version_control": 0, "video": "", "weight": 7, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99906, "is_video": 0, "link": "beautyplus://miniapp", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "mini_app", "test_b": {"color_value": "#EDEEF3", "content": "Mini App", "title_1": "", "title_2": "实用小工具", "typesetting": 9}, "topping": 0, "version_control": 0, "video": "", "weight": 6, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99905, "is_video": 0, "link": "beautyplus://zipai?mode=ar&item=501397", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "ip_ar", "test_b": {"color_value": "#F9D2E4", "content": "Snap with your favorite IP characters", "title_1": "<PERSON> Sticker", "title_2": "Sticker of the Week", "typesetting": 10}, "topping": 0, "version_control": 0, "video": "", "weight": 5, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99904, "is_video": 0, "link": "beautyplus://meiyan?mode=headScale", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "head_scale", "test_b": {"color_value": "#BDB495", "content": "Instant head size adjustment", "title_1": "", "title_2": "Head", "typesetting": 9}, "topping": 0, "version_control": 0, "video": "", "weight": 4, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99903, "is_video": 0, "link": "beautyplus://zipai?mode=filter&theme=6015&item=1615", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "Filter", "test_b": {"color_value": "#D5EOD9", "content": "Be a natural beauty", "title_1": "滤镜", "title_2": "Natural Filter", "typesetting": 10}, "topping": 0, "version_control": 0, "video": "", "weight": 3, "weight_traffic_a": 0, "weight_traffic_b": 0}, {"ad_type": 6, "id": 99902, "is_video": 0, "link": "beautyplus://meiyan?mode=boken_lens", "link_target": 1, "max_version": 0, "min_version": 0, "picture_b": "defocus", "test_b": {"color_value": "#38372F", "content": "be the focus", "title_1": "", "title_2": "高级柔焦", "typesetting": 9, "icon_imageName": "icon_homeBannerFrame_bokehLens"}, "topping": 0, "version_control": 0, "video": "", "weight": 2, "weight_traffic_a": 0, "weight_traffic_b": 0}]}
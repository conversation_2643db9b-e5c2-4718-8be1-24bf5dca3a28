<?xml version="1.0" encoding="utf-8"?>
<ShareInfo>

    <!-- ID,Name,AppKey这几个属性是必须的 -->
    <!-- ID暂未使用 -->
    <!-- Name就是你所需要的平台配置类的类名 -->
    <!-- Appkey是每个平台所给予的唯一标识,AppKey使用DES加密。切记 -->

    <!-- 混淆时需添加如下配置
    	# No Proguard Platform Config File
		-keep public class * extends com.meitu.libmtsns.framwork.i.PlatformConfig {*;}
		# No Proguard Platform Constructor
		-keepclasseswithmembers public class * extends com.meitu.libmtsns.framwork.i.Platform {
		    public <init>(android.app.Activity);
		}

		# No Proguard Weixin Class
		-keep class com.tencent.mm.sdk.openapi.WXMediaMessage {*;}
		-keep class com.tencent.mm.sdk.openapi.** implements com.tencent.mm.sdk.openapi.WXMediaMessage$IMediaObject {*;}

     -->

    <!-- Facebook配置使用说明
       <参数说明>
        Scope 这个参数可选，如果需要额外的权限话
       <AndroidManifest.xml需有如下配置 >
        <activity
            android:name="com.facebook.LoginActivity"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
    -->
    <Facebook
        Id="4000"
        Name="PlatformFacebookConfig"
        AppKey="00101000111101010010100100001011010001110100011110111010010111110001101100101010100100000011011111011001001111100110100100101001" />

</ShareInfo>
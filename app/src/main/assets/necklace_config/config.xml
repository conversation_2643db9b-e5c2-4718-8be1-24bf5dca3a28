<?xml version="1.0" encoding="utf-8"?>
<root version="2.5.4" thread_num="4" max_task_num="10">
    <contain id="face_contain_attrib" multi="0">
<!-- 五官分析深度学习框架配置文件
 支持的功能有：  脖子点检测、人体边缘点检测、脸型、太阳穴、眼袋、苹果机、颧骨、下巴....
 版本：         2.5.1
 备注：         update by txr 2019/04/15
 修订记录：    
2019-03-29:    1、增加去皱功能(配置见底部)
               2、<result save_before_mean=“1” save_after_mean=“1”>增加开关，打开开关会保存数据，方便调试。
               3、增加切图填充模式。fill_in  fill_out
               4、增加fullimg模式，当检测不到人脸时，会将整张图片丢入前项。

2019-04-19:   1、调整config.xml配置嵌套结构，将脖子点配置移到face_contain_attrib外面
               2、新增去皱、妆容识别config.xml配置。
               3、切图方式增加fill_in/fill_out两种填充方式。
2019-04-26     1、开放去皱功能，并新增manis版模型（mtnn模型没有提供）
 -->
   
		<detector id="facetype_detector" name="脸型检测" common="0" debug="1" mtnn="0">
			<models name="MTFaceType_model.manis" need_decrypt="0" devtype="0" type="MTFaceType_model.manis"/>
			
			<image resizewidth="224" resizeheight="224" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="1">
				<cut_info fa_point_num="118" face_point="0,1,2,3,4,5,6,7,8,9,15,16,17,23,24,25,26,27,28,29,30,31,31,33,34,35,36,37,42,43,44,45,46" cfg_padding="0.05,0.21,0.05,-0.18"/>
				<cut_info fa_point_num="42" face_point="26,27,28,29,30,31,32,0,1,2,11,10,9" cfg_padding="0.0,0.0,0.0,0.0"/>
			</image>
			<result>
				<result_text key="facetype_long" text="长脸"/>
				<result_text key="facetype_elliptic" text="鹅蛋脸"/>
				<result_text key="facetype_square" text="方脸"/>
				<result_text key="facetype_circular" text="圆脸"/>
				<result_text key="facetype_prism" text="棱形脸"/>
				<result_text key="facetype_triangle" text="倒三角脸"/>
			</result>
		</detector>
		<detector id="temple_detector" name="太阳穴检测" common="0" debug="0" mtnn="0">
			<models name="MTTemple_model.manis" need_decrypt="0" type="MTTemple_model.manis"/>
			
			<image resizewidth="224" resizeheight="224" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="1">
				<cut_info fa_point_num="118" face_point="0,1,2,3,4,5,6,7,88,89,90,25,26,27,28,29,30,31,31,33,34,35,36,37,42,43,44,45,46" cfg_padding="0.0,0.4,0.0,0.0"/>
				<cut_info fa_point_num="42" face_point="26,27,28,29,30,31,32,0,1,2,11,10,9" cfg_padding="0.0,0.0,0.0,0.0"/>
			</image>
			<result>
				<result_text key="temple_seg" text="凹太阳穴"/>
				<result_text key="temple_full" text="饱满太阳穴"/>
			</result>
		</detector>
        <detector id="MTRisorius_detector" name="苹果肌检测" common="0" debug="0" mtnn="0">
            <models name="MTRisorius_model.manis" need_decrypt="0" type="MTRisorius_model.manis"/>
            <image resizewidth="128" resizeheight="128" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="1">
                <cut_info fa_point_num="118" face_point="2,30,72,80" cfg_padding="0.0,-0.3,0.0,0.0"/>
            </image>
            <result>
                <result_text key="risorius_no" text="无苹果肌"/>
                <result_text key="risorius_yes" text="有苹果肌"/>
            </result>
        </detector>
        <detector id="Eyebags_detector" name="眼袋检测" common="0" debug="0" mtnn="0">
            <models name="MTEyebags_model.manis" need_decrypt="0" type="MTEyebags_model.manis"/>
            
            <image resizewidth="105" resizeheight="105" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="3">
                <cut_info fa_point_num="118" face_point="51,55,76" cfg_padding="0.0,-0.1456,-0.1876,0.0"/>
            </image>
            <result>
                <result_text key="eyebags_yes" text="有眼袋"/>
                <result_text key="eyebags_no" text="无眼袋"/>
            </result>
        </detector>
       
       <!--
          <detector id="cheek_detector" name="颧骨" common="0" debug="0" mtnn="0">
              <models name="MTCheek_model.bin" type="MTCheek_model.bin"/>
              
              <image resizewidth="128" resizeheight="128" colorspace="gray[/]255.0" need_transform="1" wrap_mode="1">
                  <cut_info fa_point_num="118" face_point="51,65,93,97" padding="-70,0,-70,0" cfg_padding="0.2142857,0.225,0.2142857,0.625"/>
                  <cut_info fa_point_num="17" face_point="4,7,13,14" cfg_padding="0.2142857,0.225,0.2142857,0.625"/>
              </image>
              
              <result>
                  <result_text key="MTFACE_ATTR_CHEEK_HIGH" text="高颧骨"/>
                  <result_text key="MTFACE_ATTR_CHEEK_FLAT" text="平颧骨"/>
              </result>
          </detector>
          <detector id="jaw_dector" common="0" name="下巴" debug="0" mtnn="0">
              <models name="MTJaw_model.bin" type="MTJaw_model.bin"/>
              
              <image resizewidth="240" resizeheight="240" colorspace="gray[/]255.0" need_transform="1" wrap_mode="1">
                  <cut_info fa_point_num="118" face_point="4,28,15,16,17" padding="-70,20,-70,-50" cfg_padding="0.2142857,0.225,0.2142857,0.625"/>
                  <cut_info fa_point_num="17" face_point="10,12" cfg_padding="0.2142857,0.225,0.2142857,0.625"/>
              </image>
              
              <result>
                  <result_text key="MTFACE_ATTR_JAW_SQUARE" text="方下巴"/>
                  <result_text key="MTFACE_ATTR_JAW_SHARP" text="尖下巴"/>
                  <result_text key="MTFACE_ATTR_JAW_ROUND" text="圆下巴"/>
              </result>
          </detector>
       -->
	</contain>

    <!-- 平滑参数配置说明
    smooth平滑设置.smooth_type:平滑方式。(0帧前平滑,1中间帧平滑)
    frame_catch_size:帧缓冲
    enable:1,点平滑，0关闭
    point_catch_size:点缓冲
    smooth_weight:平滑权重，需要配合point_catch_size来设置
    last_pts_diff:当前帧和上一帧点距。超过该阈值则用当前帧点更新
    total_pts_diff:点距之和
    mean_pts:计算均值点序号 -1：计算所有点
    -->
         
    <contain id="neck_contain" name="脖子点检测" common="1" multi="0">
        <detector id="neck_detector" common="1" debug="0" mtnn="0">
            <smooth enable="1" smooth_type="0" frame_catch_size="5" point_catch_size="3" smooth_weight="0.2,0.3,0.5">
                <adjust last_pts_diff="0.2" total_pts_diff="0.3" mean_pts="0,1,2,3,4,5,11"/>
            </smooth>
            <image resizewidth="128" resizeheight="96" colorspace="rgba[/]255.0,255.0,255.0" need_transform="0" wrap_mode="1">
                <cut_info fa_point_num="118" face_point="0,1,2,3,4,5,6,7,8,9,15,16,17,23,24,25,26,27,28,29,30,31,31" cfg_padding="0.65,-0.48,0.65,0.98"/>
                <cut_info fa_point_num="0" face_point="0,1,2,3" cfg_padding="0,0,0,0"/>
            </image>

            <models name="MTNeck_ptr_model.manis" need_decrypt="0" type="MTNeck_ptr_model.manis" output_name="604"/>
            <result type="1" boxratio="0.3" smootkernel_size="2.5" dim="14">
                <result_text key="box" text="脖子框"/>
                <result_text key="count_ptr" text="脖子点"/>
            </result>
        </detector>
    </contain>
   
    <!--
    功能：皱纹检测（额头+法令纹+鱼尾纹）
    用法：如果检测单个皱纹，请直接detect(“zhouwen_etou”）
         如果检测一个以上皱纹，请detect("clear_wrinkle_contain")
         并且开启线程池thread_num="4" max_task_num="5"
        
    获取结果：MTVector<EdgePoint> etou_pts 
            get_param("wrinkle_etou_result_key", etou_pts);
            .....
    -->
    
    <contain id="clear_wrinkle_contain" name="去除皱纹" common="0" multi="2">
          <detector id="zhouwen_etou" common="0" debug="0" mtnn="0">
              <models name="MTWrinkle_etou.manis" need_decrypt="0" type="MTWrinkle_etou.manis"/>
              <image resizewidth="256" resizeheight="128" trans_org="1" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="4">
                  <cut_info fa_point_num="118" face_point="0,1,2,3,29,30,31,32,33,38,46,50,76,84" cfg_padding="0,1.2,0,-0.9">
                      <fill_in point="33,34,35,36,37,38,39,40,41"/>
                      <fill_in point="42,43,44,45,46,47,48,49,50"/></cut_info>
              </image>
              <mask_result min_mask_color="0x20" max_mask_color="0xff" threshold="0.4">
                  <result_text key="wrinkle_etou_result_key" text="额头去皱"/>
              </mask_result>
          </detector>
          
          <detector id="zhouwen_yuwei_left" common="0" debug="0" mtnn="0">
              <models name="MTWrinkle_yuwei.manis" need_decrypt="0" type="MTWrinkle_yuwei.manis"/>
              <image resizewidth="128" resizeheight="128" trans_org="1" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="4">
                  <cut_info fa_point_num="118" face_point="0,1,2,3,76,72,71,38,39,40,41,33" cfg_padding="0,0,0,0">
                      <fill_in point="51,52,53,54,55,56,57,58"/>
                      <fill_in point="33,34,35,36,37,38,39,40,41"/>
                      <fill_out point="0,1,2,3,76,72,71,38,39,40,41,33"/>
                  </cut_info>
              </image>
              <mask_result min_mask_color="0x20" max_mask_color="0xff" threshold="0.5">
                  <result_text key="wrinkle_yuwei_left_result_key" text="左鱼尾去皱"/>
              </mask_result>
          </detector>
          
          <detector id="zhouwen_yuwei_right" common="0" debug="0" mtnn="0">
              <models name="MTWrinkle_yuwei.manis" need_decrypt="0" type="MTWrinkle_yuwei.manis"/>
              <image resizewidth="128" resizeheight="128" trans_org="1" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="4" flip="1">
                  <cut_info fa_point_num="118" face_point="71,72,84,29,30,31,32,46,47,48,49,50" cfg_padding="0,0,0,0">
                      <fill_in point="61,62,63,64,65,66,67,68"/>
                      <fill_in point="42,43,44,45,46,47,48,49,50"/>
                      <fill_out point="71,72,84,29,30,31,32,46,47,48,49,50"/>
                  </cut_info>
              </image>
              <mask_result min_mask_color="0x20" max_mask_color="0xff" threshold="0.5">
                  <result_text key="wrinkle_yuwei_right_result_key" text="右鱼尾去皱"/>
              </mask_result>
          </detector>
          
          <detector id="zhouwen_faling_left" common="0" debug="0" mtnn="0">
              <models name="MTWrinkle_faling.manis" need_decrypt="0" type="MTWrinkle_faling.manis"/>
              <image resizewidth="128" resizeheight="128" trans_org="1" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="4">
                  <cut_info fa_point_num="118" face_point="3,4,5,6,7,8,9,10,11,12,95,80,76" cfg_padding="0,0,0,0">
                      <fill_in point="86,87,88,89,90,91,92,93,94,95,96,97"/>
                      <fill_in point="76,77,78,80" padding="0,0,-0.03,0,-0.03,0.03,0,0"/>
                      <fill_out point="3,4,5,6,7,8,9,10,11,12,95,80,76"/>
                  </cut_info>
              </image>
              <mask_result min_mask_color="0x20" max_mask_color="0xff" threshold="0.9">
                  <result_text key="wrinkle_faling_left_result_key" text="左法令纹去皱"/>
              </mask_result>
          </detector>
          <detector id="zhouwen_faling_right" common="0" debug="0" mtnn="0">
              <models name="MTWrinkle_faling.manis" need_decrypt="0" type="MTWrinkle_faling.manis"/>
              <image resizewidth="128" resizeheight="128" trans_org="1" colorspace="rgba[/]255.0,255.0,255.0" need_transform="1" wrap_mode="4" flip="1">
                  <cut_info fa_point_num="118" face_point="20,21,22,23,24,25,26,27,28,29,84,80,95" cfg_padding="0,0,0,0">
                      <fill_in point="86,87,88,89,90,91,92,93,94,95,96,97"/>
                      <fill_in point="80,82,83,84" padding="0,0,0.03,0.03,0.03,0,0,0"/>
                      <fill_out point="20,21,22,23,24,25,26,27,28,29,84,80,95"/>
                  </cut_info>
              </image>
              <mask_result min_mask_color="0x20" max_mask_color="0xff" threshold="0.9">
                  <result_text key="wrinkle_faling_right_result_key" text="右法令纹去皱"/>
              </mask_result>
          </detector>
      </contain>
</root>

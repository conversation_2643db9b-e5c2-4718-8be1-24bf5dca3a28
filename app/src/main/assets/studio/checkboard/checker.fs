precision highp float;

uniform float textureWidth;
uniform float textureHeight;
uniform float number;
uniform float offsetX;
uniform float offsetY;

varying vec2 texcoordOut;

vec3 checkerboard(vec2 uv, float rows, float cols)
{
    return vec3(mod(floor(uv.x * cols) + floor( uv.y * rows), 2.0) < 1.0 ? 1.0 : 0.851);
}

void main() {
    vec2 texcoord = vec2((texcoordOut.x - offsetX), (texcoordOut.y - offsetY));
    vec3 color = checkerboard(texcoord, number*(textureHeight/textureWidth), number);
    gl_FragColor = vec4(color, 1.0);
}

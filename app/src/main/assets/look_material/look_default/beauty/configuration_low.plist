<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ID</key>
	<integer>1001</integer>
	<key>FilterPart</key>
	<array>
		<dict>
			<key>Type</key>
			<string>AnattaBeauty</string>
			<key>Filters</key>
			<array>
				<dict>
					<key>Name</key>
					<string>SourceInput</string>
				</dict>
				<!-- 实时祛斑祛痘 -->
				<dict>
					<key>Name</key>
					<string>AcneClean</string>
					<!-- 0:拍后+预览 ；1:仅预览 -->
					<key>IsOnlyPreview</key>
					<string>1</string>
					<!-- 0:关；1:开 -->
					<key>Switch</key>
					<string>1</string>
					<key>Alpha</key>
					<string>1.0</string>
					<key>FromSource</key>
					<array>
						<string>SourceInput</string>
					</array>
				</dict>
				<!-- 磨皮 -->
				<dict>
					<key>Name</key>
					<string>CompactBeauty</string>
					<key>Type</key>
					<string>CPU</string>
					<!-- <磨皮> -->
					<key>BlurSwitch</key>
					<string>1</string>
					<key>BlurAlpha</key>
					<string>1.0</string>
					<!-- <锐化> -->
					<key>SharpenType</key>
					<string>1</string>
					<key>SharpenSwitch</key>
					<string>1</string>
					<key>SharpenAlpha</key>
					<string>1.0</string>
					<!-- <美白牙齿> -->
					<key>WhiteTeethSwitch</key>
					<string>1</string>
					<key>WhiteTeethAlpha</key>
					<string>1.0</string>
					<!-- <黑眼圈> -->
					<key>RemovePouchSwitch</key>
					<string>1</string>
					<key>RemovePouchAlpha</key>
					<string>1.0</string>
					<!-- <法令纹> -->
					<key>LaughLineSwitch</key>
					<string>1</string>
					<key>LaughLineAlpha</key>
					<string>1.0</string>
					<!-- 亮眼 -->
					<key>BrightEyeType</key>
					<string>2</string>
					<key>BrightEyeSwitch</key>
					<string>1</string>
					<key>BrightEyeAlpha</key>
					<string>1.0</string>
					<key>FromSource</key>
					<array>
						<string>AcneClean</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>FaceColor</string>
					<key>Type</key>
					<string>SingleLookupWithBlack</string>
					<key>NeedSkinMaskMix</key>
                    <string>0</string>
                    <key>AutoContrastType</key>
                    <string>0</string>
                    <key>AutoContrastSwitch</key>
                    <string>0</string>
                    <key>AutoContrastAlpha</key>
                    <string>0.0</string>
                    <key>Switch</key>
                    <string>1</string>
                    <key>Alpha</key>
                    <string>1.0</string>
                    <key>FaceColor_lookUpWhitePath</key>
                    <string>lookup_table_white_bp_look.png</string>
                    <key>FaceColor_lookUpBlackPath</key>
                    <string>lookup_table_black_bp_look.png</string>
                    <key>FaceColor_lookUpSizeType</key>
                    <string>64</string>
					<key>FromSource</key>
					<array>
						<string>CompactBeauty</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>ResultOutput</string>
					<key>FromSource</key>
					<array>
						<string>FaceColor</string>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

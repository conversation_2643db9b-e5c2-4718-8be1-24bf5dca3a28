<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
	<array>
		<dict>
			<key>Name</key>
			<string></string>
			<key>AI</key>
			<array>
				<string>ai/configuration.json</string>
			</array>
			<key>CustomParamDict</key>
			<dict>
				<key>EyelidRealtimeModelType</key>
				<integer>-1</integer>
			</dict>
			<key>SpecialFacelift</key>
			<integer>1</integer>
			<key>IsNeedResetSound</key>
			<integer>0</integer>
			<key>EnableReplaceSpecialFacelift</key>
			<integer>0</integer>
			<key>FacePart</key>
			<array>
				<dict>
					<key>Type</key>
					<string>VectorDiagram</string>
					<key>GeneratePlistPath</key>
					<string>operation12327020544.plist</string>
					<key>MaskPath</key>
					<array>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2100_12327022864.png</string>
							<key>Rectangle</key>
							<string>23,101,238,158;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2106_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2108_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2110_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2114_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2119_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2195_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2199_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2200_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2202_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2400_12327022864.png</string>
							<key>Rectangle</key>
							<string>18,116,245,153;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2404_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2405_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12327020544/2406_12327022864.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>0,1</string>
							<key>ValueRange</key>
							<string>0,1</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
					</array>
					<key>ID</key>
					<string>105553140268992</string>
					<key>Name</key>
					<string></string>
				</dict>
				<dict>
					<key>Type</key>
					<string>VectorDiagram</string>
					<key>GeneratePlistPath</key>
					<string>operation12328599040.plist</string>
					<key>MaskPath</key>
					<array>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2100_12328601360.png</string>
							<key>Rectangle</key>
							<string>23,107,237,151;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2106_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2108_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2110_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2114_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2119_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2195_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2199_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2200_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2202_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2400_12328601360.png</string>
							<key>Rectangle</key>
							<string>19,116,244,154;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2404_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2405_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
						<dict>
							<key>Path</key>
							<string>VectorDiagram12328599040/2406_12328601360.png</string>
							<key>Rectangle</key>
							<string>0,0,280,280;</string>
							<key>LiftControlType</key>
							<integer>1</integer>
							<key>ControlRange</key>
							<string>-1,0</string>
							<key>ValueRange</key>
							<string>1,0</string>
							<key>Scale</key>
							<string>0.00</string>
						</dict>
					</array>
					<key>ID</key>
					<string>105553140266688</string>
					<key>Name</key>
					<string></string>
				</dict>
			</array>
		</dict>
	</array>
</plist>

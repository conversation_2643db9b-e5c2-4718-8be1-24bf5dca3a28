<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>ID</key>
		<integer>1001</integer>
		<key>FilterPart</key>
		<array>
			<dict>
				<key>Type</key>
				<string>AnattaBeauty</string>
				<key>Filters</key>
				<array>
					<dict>
						<key>Name</key>
						<string>SourceInput</string>
					</dict>
					<dict>
						<key>Name</key>
						<string>JawlineShadow</string>
						<key>WithFullFaceMask</key>
						<string>0</string>
						<key>WithProfileFade</key>
						<string>0</string>
						<key>FromSource</key>
						<array>
							<string>SourceInput</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>FleckFlawClean</string>
						<key>NeedExternSkinMaskData</key>
						<string>1</string>
						<key>IsNeedOpt</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>JawlineShadow</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>AcneClean</string>
						<key>IsOnlyPreview</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>FleckFlawClean</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>CleanShiny</string>
						<key>Type</key>
						<string>RealTime</string>
						<key>FromSource</key>
						<array>
							<string>AcneClean</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>CheekFillers</string>
						<key>IsNeedFlawSmooth</key>
						<string>1</string>
						<key>FlawSmoothAlpha</key>
						<string>0.3</string>
						<key>IsNeedShadowLightDarkAlpha</key>
						<string>1</string>
						<key>SkinToneCorrectionType</key>
						<string>4</string>
						<key>IsMiniFaceLimit</key>
						<string>1</string>
						<key>HighlighAlpha</key>
						<string>1.0</string>
						<key>IsUseRealtimeMask</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>CleanShiny</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>FacialShadowSmoothBody</string>
						<key>SkinToneCorrectionType</key>
						<string>1</string>
						<key>IsNeedWholeFaceMask</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>CheekFillers</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>DodgeBurn</string>
						<key>FromSource</key>
						<array>
							<string>FacialShadowSmoothBody</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>ShadowSmoothBright</string>
						<key>IsNeedFaceDarkAlpha</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>DodgeBurn</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>SkinSmooth</string>
						<key>Type</key>
						<string>Normal</string>
						<key>IsNeedFaceAndNeckMask</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>ShadowSmoothBright</string>
						</array>
					</dict>
					<!-- 以下BlurFilter02与FacialBeautify一起使用为旧的去法令纹版本，和上面ShadowSmoothBright冲突，为了对标美颜相机进行保留，目前整体效果为去两次法令纹 -->
					<dict>
						<key>Name</key>
						<string>BlurFilter02</string>
						<key>FromSource</key>
						<array>
							<string>SkinSmooth</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>Sharpen</string>
						<key>Type</key>
						<string>USMAndBodyBlur</string>
						<key>ExceptFaceSkin</key>
						<string>0</string>
						<key>IsBeautyThreeSharpen</key>
						<string>4</string>
						<key>ExceptFaceAlpha</key>
						<string>0.5</string>
						<key>Strength</key>
						<string>0.35</string>
						<key>Radius</key>
						<string>0.5</string>
						<key>ColorThredshold</key>
						<string>10</string>
						<key>IsNewSharpen</key>
						<string>1</string>
						<key>IsNeedBodyBlur</key>
						<string>0</string>
						<key>SharpenAdaptType</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>SkinSmooth</string>
							<string>SourceInput</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>BrightEye</string>
						<key>Type</key>
						<string>Pupil</string>
						<key>IsNeedSkinMask</key>
						<string>1</string>
						<key>EnableEyeSeparation</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>Sharpen</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>ShadowLight</string>
						<key>Type</key>
						<string>2.5DLightNew</string>
						<key>IsNeedHighLight</key>
						<string>1</string>
						<key>IsNeedFaceTexture</key>
						<string>1</string>
						<key>IsNeedAttenuation</key>
						<string>1</string>
						<key>FromSource</key>
						<array>
							<string>BrightEye</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>FaceColor</string>
						<key>Type</key>
						<string>SingleLookupWithBlack</string>
						<key>NeedSkinMaskMix</key>
						<string>0</string>
						<key>FaceColor_lookUpWhitePath</key>
						<string>lookup_table_white_bp_look.png</string>
						<key>FaceColor_lookUpBlackPath</key>
						<string>lookup_table_black_bp_look.png</string>
						<key>FaceColor_lookUpSizeType</key>
						<string>64</string>
						<key>FromSource</key>
						<array>
							<string>ShadowLight</string>
						</array>
					</dict>
					<dict>
						<key>Name</key>
						<string>ResultOutput</string>
						<key>FromSource</key>
						<array>
							<string>FaceColor</string>
						</array>
					</dict>
				</array>
			</dict>
		</array>
	</dict>
</plist>

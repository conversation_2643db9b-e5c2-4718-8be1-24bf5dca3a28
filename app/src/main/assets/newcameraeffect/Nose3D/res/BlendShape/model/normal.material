material textured
{
    u_worldViewProjectionMatrix = WORLD_VIEW_PROJECTION_MATRIX
    
    sampler u_diffuseTexture
    {
        mipmap = false
        wrapS = CLAMP
        wrapT = CLAMP
        minFilter = LINEAR
        magFilter = LINEAR
    }

    renderState
    {
        depthTest = true
        blend = true
        blendSrc = SRC_ALPHA
        blendDst = ONE_MINUS_SRC_ALPHA
    }
    
    technique
    {
        pass 
        {
            resourcePath = true
            vertexShader = textured.vert
            fragmentShader = textured.frag
        }
    }
}
material MT_bighead : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_diffuseTexture
    {
        path = normal2.png
    }

}
material MT_niujiao : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_diffuseTexture
    {
        path = niujiao_base_color.png
    }

    technique
    {
        pass 
        {

            defines = SKINNING;SKINNING_JOINT_COUNT 3
        }
    }
}
material MT_bihuan : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_diffuseTexture
    {
        path = refl.png
    }


}
material MT_box : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_diffuseTexture
    {
        path = box.jpg
        wrapS = REPEAT
        wrapT = REPEAT
    }

}

material MT_Light
{
    technique
    {
        pass 
        {
            resourcePath = true
            vertexShader = facelightV2.vert
            fragmentShader = facelightV2.frag
        }
    }
}
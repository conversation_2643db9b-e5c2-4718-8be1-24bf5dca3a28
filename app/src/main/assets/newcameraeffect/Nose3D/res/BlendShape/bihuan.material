material textured
{
    // u_worldViewProjectionMatrix = WORLD_VIEW_PROJECTION_MATRIX
    mvpMatrix = WORLD_VIEW_PROJECTION_MATRIX
    mMatrix = WORLD_MATRIX
    sampler u_texture
    {
        mipmap = true
        wrapS = REPEAT
        wrapT = REPEAT
        minFilter = LINEAR_MIPMAP_LINEAR
        magFilter = LINEAR
    }

    renderState
    {
        depthTest = true
        blend = true
        blendSrc = SRC_ALPHA
        blendDst = ONE_MINUS_SRC_ALPHA
    }
    
    technique
    {
        pass 
        {
            resourcePath = true
            vertexShader = shaders/baseShader.vs
            fragmentShader = shaders/baseShader.fs
        }
    }
}
material MT_bihuan : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_texture
    {
        path = refl.png
    }
}

material MT_Light : textured
{

    u_matrixPalette = MATRIX_PALETTE
    
    sampler u_texture
    {
        path = refl.png
    }
}

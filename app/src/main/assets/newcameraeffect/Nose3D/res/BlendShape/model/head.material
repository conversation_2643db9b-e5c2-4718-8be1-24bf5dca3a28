material textured
{
   // u_worldViewProjectionMatrix = WORLD_VIEW_PROJECTION_MATRIX
    
    sampler u_diffuseTexture
    {
        mipmap = true
        wrapS = CLAMP
        wrapT = CLAMP
        minFilter = LINEAR_MIPMAP_LINEAR
        magFilter = LINEAR
    }

    renderState
    {
        depthTest = true
    }
    
    technique
    {
        pass 
        {
            resourcePath = true
            vertexShader = textured_head.vert
            fragmentShader = textured_head.frag
        }
    }
}
material MT_bighead : textured
{
    
    sampler u_diffuseTexture
    {
        path = normal2.png
    }
}

material MT_Light
{

    u_mvpMatrix  = WORLD_VIEW_PROJECTION_MATRIX
    u_worldMatrix =  WORLD_MATRIX

    technique
    {
        pass 
        {
            resourcePath = true
            vertexShader = facelightV2.vert
            fragmentShader = facelightV2.frag
        }
    }
}
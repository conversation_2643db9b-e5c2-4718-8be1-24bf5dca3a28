config={
	BlendShapeModelClass = {
		"<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON>Bi",
		"<PERSON><PERSON><PERSON>Bi",
		"<PERSON><PERSON><PERSON>i",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"Face",
	},

	HeadSliderType = "HEAD",
	HeadEffectSliderClass = {
		1,
	},

	NoseSliderType = "NOSE",
	NoseEffectSliderClass = {
		4,
	},

	PlumpCheeksSliderType = "PLUMP_CHEEKS",
	PlumpCheeksEffectSliderClass = {
		5,
	},

	ConvexMouthSliderType = "CONVEX_MOUTH",
	ConvexMouthEffectSliderClass = {
		6,
	},

	NoseBridgeSliderType = "NOSE_BRIDGE",
	NoseBridgeEffectSliderClass = {
		7,
	},

	StretchFaceSliderType = "STRETCH_FACE",
	StretchFaceEffectSliderClass = {
		8,
	},
}




return config
<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">

<plist>
  <array>
    <dict>
      <key>CustomParamDict</key>
      <dict>
        <key>EyelidRealtimeModelType</key>
        <integer>-1</integer>
      </dict>
      <key>SpecialFacelift</key>
      <integer>1</integer>
      <key>FacePart</key>
      <array>
        <!-- 渲染插入回调 -->
        <dict>
          <key>Type</key>
          <string>Callback</string>
          <key>CallbackConfigurationPath</key>
          <string></string>
        </dict>
        <!--面部提拉高光Start-->
        <dict>
          <key>Type</key>
          <string>MakeupScript</string>
          <key>EnableFace</key>
          <integer>1</integer>
          <key>EnableFaceHead</key>
          <integer>1</integer>
          <key>ID</key>
          <string>105553125624000</string>
          <key>Name</key>
          <string></string>
          <key>CustomName</key>
          <string>BEFORE_FACELIFT</string>
          <key>MakeupCustomName</key>
          <string>BEFORE_FACELIFT</string>
          <key>PacketPath</key>
          <string>template</string>
          <key>ScriptPath</key>
          <string>template/template.lua</string>
        </dict>
        <!--面部提拉高光End-->
        <!--面部提拉形变Start-->
        <dict>
          <key>Type</key>
          <string>VectorDiagram</string>
          <key>GeneratePlistPath</key>
          <string>operation140462102089216.plist</string>
          <key>MaskPath</key>
          <array>
            <dict>
              <key>Path</key>
              <string>VectorDiagram140462102089216/2108_140462102091384.png</string>
              <key>Rectangle</key>
              <string>94,119,93,22;</string>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>VectorDiagram140462102089216/2195_140462102091384.png</string>
              <key>Rectangle</key>
              <string>31,120,214,134;</string>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>VectorDiagram140462102089216/2199_140462102091384.png</string>
              <key>Rectangle</key>
              <string>30,69,217,59;</string>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0.0</string>
            </dict>
          </array>
        </dict>
        <!--面部提拉形变End-->
        <dict>
          <key>Type</key>
          <string>FaceliftV2</string>
          <key>SwapPoint</key>
          <string>1</string>
          <key>MaskPath</key>
          <array>
            <!--脸型  Start-->
            <!--瘦脸  Start-->
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianzheng1.png</string>
              <key>Rectangle</key>
              <string>103,165,77,46;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/shoulian/shoulianzheng2.png</string>
              <key>Rectangle</key>
              <string>19,26,243,243;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--瘦脸  End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/l.png</string>
              <key>Rectangle</key>
              <string>22,78,120,176;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/r.png</string>
              <key>Rectangle</key>
              <string>142,78,119,176;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/l.png</string>
              <key>Rectangle</key>
              <string>22,81,120,71;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/r.png</string>
              <key>Rectangle</key>
              <string>142,81,119,71;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--脸宽 反向 宽  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/F/l.png</string>
              <key>Rectangle</key>
              <string>22,73,120,181;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/F/r.png</string>
              <key>Rectangle</key>
              <string>142,73,119,181;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 反向 宽  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/l.png</string>
              <key>Rectangle</key>
              <string>48,154,93,63;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/Z/r.png</string>
              <key>Rectangle</key>
              <string>141,154,93,63;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/F/l.png</string>
              <key>Rectangle</key>
              <string>45,144,93,68;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/F/r.png</string>
              <key>Rectangle</key>
              <string>148,144,94,68;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140659887590832.png</string>
              <key>Rectangle</key>
              <string>111,188,64,52;</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--下巴 反向  长 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140274698754504.png</string>
              <key>Rectangle</key>
              <string>74,180,136,78;</string>
            </dict>
            <!--下巴 反向  长 End  -->
            <!--脸型  End-->
            <!--大眼 正向 大  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/dayan/Z/lr.png</string>
              <key>Rectangle</key>
              <string>86,109,110,39;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--大眼 正向 大  End  -->
            <!--大眼 反向 小  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/dayan/F/l.png</string>
              <key>Rectangle</key>
              <string>86,111,55,35;</string>
              <key>ChildSlider</key>
              <string>73</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/dayan/F/r.png</string>
              <key>Rectangle</key>
              <string>141,111,54,35;</string>
              <key>ChildSlider</key>
              <string>79</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--大眼 反向 小  End  -->
            <!--眼睛角度 正向 上扬  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/jiaodu/Z/l.png</string>
              <key>Rectangle</key>
              <string>87,110,54,38;</string>
              <key>ChildSlider</key>
              <string>75</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/jiaodu/Z/r.png</string>
              <key>Rectangle</key>
              <string>141,110,54,38;</string>
              <key>ChildSlider</key>
              <string>81</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--眼睛角度 正向 上扬  End  -->
            <!--眼睛角度 反向 下垂  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/jiaodu/F/l.png</string>
              <key>Rectangle</key>
              <string>87,110,54,38;</string>
              <key>ChildSlider</key>
              <string>75</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/jiaodu/F/r.png</string>
              <key>Rectangle</key>
              <string>141,110,54,38;</string>
              <key>ChildSlider</key>
              <string>81</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--眼睛角度 反向 下垂  End  -->
            <!--内眼角 Start-->
            <dict>
              <key>Path</key>
              <string>eyes/neiyanjiao/l.png</string>
              <key>Rectangle</key>
              <string>97,111,44,31;</string>
              <key>ChildSlider</key>
              <string>120</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/neiyanjiao/r.png</string>
              <key>Rectangle</key>
              <string>141,111,44,31;</string>
              <key>ChildSlider</key>
              <string>121</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--内眼角 End  -->
            <!--眼睑下至  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/yanjianxiazhi/l.png</string>
              <key>Rectangle</key>
              <string>87,118,54,28;</string>
              <key>ChildSlider</key>
              <string>117</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/yanjianxiazhi/r.png</string>
              <key>Rectangle</key>
              <string>141,118,54,28;</string>
              <key>ChildSlider</key>
              <string>118</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--眼睑下至  End  -->
            <!--微笑眼  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/weixiaoyan/l.png</string>
              <key>Rectangle</key>
              <string>83,103,58,44;</string>
              <key>ChildSlider</key>
              <string>154</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/weixiaoyan/r.png</string>
              <key>Rectangle</key>
              <string>141,103,59,44;</string>
              <key>ChildSlider</key>
              <string>155</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--微笑眼  End  -->
            <!--眼睛  End-->
            <!--嘴巴 Start-->
            <!--嘴唇 正向 小  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/daxiao/0_1_140721366093744_2.png</string>
              <key>Rectangle</key>
              <string>103,164,78,49;</string>
            </dict>
            <!--嘴唇 正向 小  End  -->
            <!--嘴唇 反向 大  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/daxiao/0_1_140721366093744.png</string>
              <key>Rectangle</key>
              <string>103,164,77,49;</string>
            </dict>
            <!--嘴唇 反向 大  End  -->
            <!--丰唇 正向 变厚  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/Z/fengshangchunz.png</string>
              <key>Rectangle</key>
              <string>111,168,61,18;</string>
              <key>ChildSlider</key>
              <string>20</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/Z/fengxiachunz.png</string>
              <key>Rectangle</key>
              <string>119,176,46,23;</string>
              <key>ChildSlider</key>
              <string>21</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--丰唇 正向 变厚  End  -->
            <!--丰唇 反向 变薄  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/F/fengshangchunf.png</string>
              <key>Rectangle</key>
              <string>111,168,62,19;</string>
              <key>ChildSlider</key>
              <string>20</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/F/fengxiachunf.png</string>
              <key>Rectangle</key>
              <string>118,175,47,24;</string>
              <key>ChildSlider</key>
              <string>21</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--丰唇 正向 变厚  Start-->
            <!--嘴巴 End-->
            <!--鼻子 Start  -->
            <!--鼻翼 正向 小  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biyi/Z/lr.png</string>
              <key>Rectangle</key>
              <string>107,108,69,68;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--鼻翼 正向 小  End  -->
            <!--鼻翼 反向 大  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biyi/F/l.png</string>
              <key>Rectangle</key>
              <string>105,137,36,39;</string>
              <key>ChildSlider</key>
              <string>95</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>nose/biyi/F/r.png</string>
              <key>Rectangle</key>
              <string>141,137,36,39;</string>
              <key>ChildSlider</key>
              <string>96</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--鼻翼 反向 大  End  -->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biliang/0_1_140260981872552.png</string>
              <key>Rectangle</key>
              <string>117,106,49,70;</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻梁 反向 粗  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biliang/0_1_140460538949552.png</string>
              <key>Rectangle</key>
              <string>120,106,43,70;</string>
            </dict>
            <!--鼻梁 反向 粗  End  -->
            <!--鼻尖 正向 小  Start-->
            <dict>
              <key>Path</key>
              <string>nose/bijian/0_1_140460544365488.png</string>
              <key>Rectangle</key>
              <string>126,143,31,32;</string>
            </dict>
            <!--鼻尖 正向 小  End  -->
            <!--鼻尖 反向 大  Start-->
            <dict>
              <key>Path</key>
              <string>nose/bijian/0_1_140460538949552.png</string>
              <key>Rectangle</key>
              <string>125,143,33,33;</string>
            </dict>
            <!--鼻尖 反向 大  End  -->
            <!--鼻子 End  -->
            <!--缩人中 正向 缩  Start  -->
            <dict>
              <key>Path</key>
              <string>face/suorenzhong/renzhongzheng.png</string>
              <key>Rectangle</key>
              <string>19,148,247,109;</string>
            </dict>
            <!--缩人中 正向 缩  End  -->
            <!--缩人中 反向 伸  Start  -->
            <dict>
              <key>Path</key>
              <string>face/suorenzhong/renzhongfu.png</string>
              <key>Rectangle</key>
              <string>19,149,246,110;</string>
            </dict>
            <!--缩人中 反向 伸  End  -->
            <!--中庭 正向 Start  -->
            <dict>
              <key>Path</key>
              <string>face/zhongting/z.png</string>
              <key>Rectangle</key>
              <string>58,128,163,102;</string>
            </dict>
            <!--中庭 正向  End  -->
            <!--中庭 反向  Start  -->
            <dict>
              <key>Path</key>
              <string>face/zhongting/f.png</string>
              <key>Rectangle</key>
              <string>58,129,163,102;</string>
            </dict>
            <!--中庭 反向  End  -->
            <!--下庭 正向 小 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiating/0_1_140388718478768.png</string>
              <key>Rectangle</key>
              <string>22,130,238,127;</string>
            </dict>
            <!--下庭 正向 小 End  -->
            <!--下庭 反向 大 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiating/0_1_140388668404656.png</string>
              <key>Rectangle</key>
              <string>23,130,238,129;</string>
            </dict>
            <!--下庭 反向 大 End  -->
          </array>
          <key>Configure</key>
          <array>
            <!--瘦脸 正向  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>0</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>1</integer>
              <key>LiftControlType</key>
              <integer>1</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--瘦脸 正向  End  -->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>2</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>3</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>4</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>5</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--脸宽 反向 宽  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>6</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1,0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>7</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1,0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 反向 宽  End  -->
            <!--颌骨 正向 小 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>8</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>9</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 正向 小 End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>10</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>11</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>12</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--下巴 反向  长 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>13</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 反向  长 End  -->
            <!--大眼 正向 大  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>14</integer>
              <key>LiftControlType</key>
              <integer>0</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--大眼 正向 大  End  -->
            <!--大眼 反向 小  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>15</integer>
              <key>LiftControlType</key>
              <integer>0</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>16</integer>
              <key>LiftControlType</key>
              <integer>0</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--大眼 反向 小  End  -->
            <!--眼睛角度 正向 上扬  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>17</integer>
              <key>LiftControlType</key>
              <integer>26</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>18</integer>
              <key>LiftControlType</key>
              <integer>26</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--眼睛角度 正向 上扬  End  -->
            <!--眼睛角度 反向 下垂  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>19</integer>
              <key>LiftControlType</key>
              <integer>26</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>20</integer>
              <key>LiftControlType</key>
              <integer>26</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--眼睛角度 反向 下垂  End  -->
            <!--内眼角  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>21</integer>
              <key>LiftControlType</key>
              <integer>57</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>22</integer>
              <key>LiftControlType</key>
              <integer>57</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--内眼角  End  -->
            <!--眼睑下至  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>23</integer>
              <key>LiftControlType</key>
              <integer>60</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>24</integer>
              <key>LiftControlType</key>
              <integer>60</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--眼睑下至  End  -->
            <!--微笑眼  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>25</integer>
              <key>LiftControlType</key>
              <integer>56</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>26</integer>
              <key>LiftControlType</key>
              <integer>56</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--微笑眼  End  -->
            <!--眼睛  End  -->
            <!--嘴巴 Start-->
            <!--嘴唇 正向 小  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>27</integer>
              <key>LiftControlType</key>
              <integer>4</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--嘴唇 正向 小  End  -->
            <!--嘴唇 反向 大  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>28</integer>
              <key>LiftControlType</key>
              <integer>4</integer>
              <key>ControlRange</key>
              <string>-1.0,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--嘴唇 反向 大  End  -->
            <!--丰唇 正向 变厚  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>29</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>30</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--丰唇 正向 变厚  End  -->
            <!--丰唇 反向 变薄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>31</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>32</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--丰唇 反向 变薄  End  -->
            <!--嘴巴 End-->
            <!--鼻翼 正向 小  Start-->
            <dict>
              <key>MaskIndex</key>
              <string>33</string>
              <key>LiftControlType</key>
              <string>22</string>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>1.00</string>
            </dict>
            <!--鼻翼 正向 小  End  -->
            <!--鼻翼 反向 大  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>34</integer>
              <key>LiftControlType</key>
              <integer>22</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>35</integer>
              <key>LiftControlType</key>
              <integer>22</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻翼 反向 大  End  -->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>36</integer>
              <key>LiftControlType</key>
              <integer>51</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻梁 反向 粗  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>37</integer>
              <key>LiftControlType</key>
              <integer>51</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻梁 反向 粗  End  -->
            <!--鼻尖 正向 小  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>38</integer>
              <key>LiftControlType</key>
              <integer>52</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻尖 正向 小  End  -->
            <!--鼻尖 反向 大  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>39</integer>
              <key>LiftControlType</key>
              <integer>52</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻尖 反向 大  End  -->
            <!--鼻子 End  -->
            <!--缩人中 正向 缩  Start  -->
            <dict>
              <key>MaskIndex</key>
              <integer>40</integer>
              <key>LiftControlType</key>
              <integer>59</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.00</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--缩人中 正向 缩  End  -->
            <!--缩人中 反向 伸  Start  -->
            <dict>
              <key>MaskIndex</key>
              <integer>41</integer>
              <key>LiftControlType</key>
              <integer>59</integer>
              <key>ControlRange</key>
              <string>-1.00,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--缩人中 反向 伸  End  -->
            <!--中庭 正向  Start  -->
            <dict>
              <key>MaskIndex</key>
              <integer>42</integer>
              <key>LiftControlType</key>
              <integer>72</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.00</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--中庭 正向  End  -->
            <!--中庭 反向  Start  -->
            <dict>
              <key>MaskIndex</key>
              <integer>43</integer>
              <key>LiftControlType</key>
              <integer>72</integer>
              <key>ControlRange</key>
              <string>-1.00,0.00</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--中庭 反向  End  -->
            <!--下庭 正向 小 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>44</integer>
              <key>LiftControlType</key>
              <integer>28</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
              <key>Coefficient</key>
              <string>1.2</string>
            </dict>
            <!--下庭 正向 小 End  -->
            <!--下庭 反向 大 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>45</integer>
              <key>LiftControlType</key>
              <integer>28</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
              <key>Coefficient</key>
              <string>1.2</string>
            </dict>
            <!--下庭 反向 大 End  -->
          </array>
        </dict>
        <dict>
          <key>Type</key>
          <string>HeadFacelift</string>
          <key>Degree</key>
          <string>0,0.0,0.0,0,0,0</string>
          <key>Debug</key>
          <string>0</string>
          <key>DebugTriangle</key>
          <string>0</string>
          <key>Face5_9</key>
          <string>1</string>
        </dict>
        <dict>
          <key>Type</key>
          <string>Facelift</string>
          <key>FacemeshType</key>
          <string>2198</string>
          <key>FacemeshParameters</key>
          <string>0.0,0.0,0.0</string>
          <!--美型滑杆配置-->
          <!-- 控制左右大眼滑杆 -->
          <key>LiftControlType</key>
          <string>73,79</string>
          <!-- 控制FaceMeshParameters具体位置 -->
          <key>ControlPosition</key>
          <string>1;2;</string>
          <!-- 输入滑杆范围 -->
          <key>ControlRange</key>
          <string>-1.0,1.0;-1.0,1.0;</string>
          <!-- 转化给底层数据范围 -->
          <key>ValueRange</key>
          <string>2.0,4.0;2.0,4.0;</string>
          <!-- <string>1.3018,2.9053;1.3018,2.9053</string> -->
          <!-- <string>-0.6982,0.9053;-0.6982,0.9053</string> -->
          <key>Debug</key>
          <string>0</string>
        </dict>
      </array>
    </dict>
  </array>
</plist>

<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">

<plist>
  <array>
    <dict>
      <key>CustomParamDict</key>
      <dict>
        <key>EyelidRealtimeModelType</key>
        <integer>-1</integer>
      </dict>
      <key>SpecialFacelift</key>
      <integer>1</integer>
      <key>FacePart</key>
      <array>
        <dict>
          <key>Type</key>
          <string>FaceliftV2</string>
          <key>SwapPoint</key>
          <string>1</string>
          <key>MaskPath</key>
          <array>
            <!--脸型  Start-->
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/l.png</string>
              <key>Rectangle</key>
              <string>22,78,120,176;</string>
              <key>Child<PERSON>lider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z1/r.png</string>
              <key>Rectangle</key>
              <string>142,78,119,176;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/l.png</string>
              <key>Rectangle</key>
              <string>22,81,120,71;</string>
              <key>ChildSlider</key>
              <string>99</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/liankuan/Z2/r.png</string>
              <key>Rectangle</key>
              <string>142,81,119,71;</string>
              <key>ChildSlider</key>
              <string>108</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>Path</key>
              <string>face/hegu/F/l.png</string>
              <key>Rectangle</key>
              <string>45,144,93,68;</string>
              <key>ChildSlider</key>
              <string>113</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>face/hegu/F/r.png</string>
              <key>Rectangle</key>
              <string>148,144,94,68;</string>
              <key>ChildSlider</key>
              <string>114</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>Path</key>
              <string>face/xiaba/0_1_140659887590832.png</string>
              <key>Rectangle</key>
              <string>111,188,64,52;</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--脸型  End-->
            <!--大眼 正向 大  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/dayan/Z/lr.png</string>
              <key>Rectangle</key>
              <string>86,109,110,39;</string>
              <key>GenerateType</key>
              <string>0</string>
            </dict>
            <!--大眼 正向 大  End  -->
            <!--微笑眼  Start-->
            <dict>
              <key>Path</key>
              <string>eyes/weixiaoyan/l.png</string>
              <key>Rectangle</key>
              <string>83,103,58,44;</string>
              <key>ChildSlider</key>
              <string>154</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>eyes/weixiaoyan/r.png</string>
              <key>Rectangle</key>
              <string>141,103,59,44;</string>
              <key>ChildSlider</key>
              <string>155</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--微笑眼  End  -->
            <!--眼睛  End-->
            <!--嘴巴 Start-->
            <!--丰唇 正向 变厚  Start-->
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/Z/fengshangchunz.png</string>
              <key>Rectangle</key>
              <string>111,168,61,18;</string>
              <key>ChildSlider</key>
              <string>20</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <dict>
              <key>Path</key>
              <string>mouth/fengchun/Z/fengxiachunz.png</string>
              <key>Rectangle</key>
              <string>119,176,46,23;</string>
              <key>ChildSlider</key>
              <string>21</string>
              <key>Scale</key>
              <string>0.0</string>
              <key>Coefficient</key>
              <string>1.0</string>
            </dict>
            <!--丰唇 正向 变厚  End  -->
            <!--嘴巴 End-->
            <!--鼻子 Start  -->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>Path</key>
              <string>nose/biliang/0_1_140260981872552.png</string>
              <key>Rectangle</key>
              <string>117,106,49,70;</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻子 End  -->
          </array>
          <key>Configure</key>
          <array>
            <!--脸宽 正向 窄  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>0</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>1</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>2</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0,1</string>
              <key>ValueRange</key>
              <string>0,1</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>3</integer>
              <key>LiftControlType</key>
              <integer>18</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--脸宽 正向 窄  End  -->
            <!--颌骨 反向 大 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>4</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>5</integer>
              <key>LiftControlType</key>
              <integer>62</integer>
              <key>ControlRange</key>
              <string>-1.0,0.0</string>
              <key>ValueRange</key>
              <string>1.0,0.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--颌骨 反向 大 End  -->
            <!--下巴 正向  短 Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>6</integer>
              <key>LiftControlType</key>
              <integer>2</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--下巴 正向  短 End  -->
            <!--大眼 正向 大  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>7</integer>
              <key>LiftControlType</key>
              <integer>0</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--大眼 正向 大  End  -->
            <!--微笑眼  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>8</integer>
              <key>LiftControlType</key>
              <integer>56</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>9</integer>
              <key>LiftControlType</key>
              <integer>56</integer>
              <key>ControlRange</key>
              <string>0.0,1.0</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--微笑眼  End  -->
            <!--眼睛  End  -->
            <!--嘴巴 Start-->
            <!--丰唇 正向 变厚  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>10</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <dict>
              <key>MaskIndex</key>
              <integer>11</integer>
              <key>LiftControlType</key>
              <integer>30</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--丰唇 正向 变厚  End  -->
            <!--嘴巴 End-->
            <!--鼻梁 正向 细  Start-->
            <dict>
              <key>MaskIndex</key>
              <integer>12</integer>
              <key>LiftControlType</key>
              <integer>51</integer>
              <key>ControlRange</key>
              <string>0.0,1.00</string>
              <key>ValueRange</key>
              <string>0.0,1.0</string>
              <key>Scale</key>
              <string>0</string>
            </dict>
            <!--鼻梁 正向 细  End  -->
            <!--鼻子 End  -->
          </array>
        </dict>
        <dict>
          <key>Type</key>
          <string>HeadFacelift</string>
          <key>Degree</key>
          <string>0,0.0,0.0,0,0,0</string>
          <key>Debug</key>
          <string>0</string>
          <key>DebugTriangle</key>
          <string>0</string>
          <key>Face5_9</key>
          <string>1</string>
        </dict>
      </array>
    </dict>
  </array>
</plist>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>增强</key>
	<array>
		<dict>
			<key>EffectClass</key>
			<string>EffectTextureTuneGroup</string>
			<key>EffectName</key>
			<string>智能补光</string>
			<key>EffectGroup</key>
			<array>
				<dict>
					<key>shader_v</key>
					<string>texture_v</string>
					<key>shader_f</key>
					<string>mapy_f</string>
					<key>material</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/optimization_1.png</string>
						</dict>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/optimization_2.png</string>
						</dict>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/optimization_3.png</string>
						</dict>
					</array>
					<key>float</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>alpha</string>
							<key>floatValue</key>
							<integer>1</integer>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>EffectClass</key>
			<string>EffectTextureTuneGroup</string>
			<key>EffectName</key>
			<string>锐化</string>
			<key>EffectGroup</key>
			<array>
				<dict>
					<key>shader_v</key>
					<string>sharpen_v</string>
					<key>shader_f</key>
					<string>sharpen_f</string>
					<key>float</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>alpha</string>
							<key>floatValue</key>
							<integer>1</integer>
						</dict>
					</array>
					<key>fraction</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>imageWidthFactor</string>
							<key>molecule</key>
							<dict>
								<key>DataType</key>
								<string>number</string>
								<key>value</key>
								<real>1</real>
							</dict>
							<key>denominator</key>
							<dict>
								<key>DataType</key>
								<string>string</string>
								<key>value</key>
								<string>imageWidth</string>
							</dict>
						</dict>
						<dict>
							<key>uniformName</key>
							<string>imageHeightFactor</string>
							<key>molecule</key>
							<dict>
								<key>DataType</key>
								<string>number</string>
								<key>value</key>
								<real>1</real>
							</dict>
							<key>denominator</key>
							<dict>
								<key>DataType</key>
								<string>string</string>
								<key>value</key>
								<string>imageHeight</string>
							</dict>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>EffectClass</key>
			<string>EffectTextureTuneGroup</string>
			<key>EffectName</key>
			<string>色温</string>
			<key>EffectGroup</key>
			<array>
				<dict>
					<key>shader_v</key>
					<string>texture_v</string>
					<key>shader_f</key>
					<string>bothway_mapy_f</string>
					<key>material</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/colourtemperature100.png</string>
						</dict>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData2</string>
							<key>materialPath</key>
							<string>style/colourtemperature_100.png</string>
						</dict>
					</array>
					<key>float</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>alpha</string>
							<key>floatValue</key>
							<integer>0</integer>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>EffectClass</key>
			<string>EffectTextureTuneGroup</string>
			<key>EffectName</key>
			<string>暗部改善</string>
			<key>EffectGroup</key>
			<array>
				<dict>
					<key>shader_v</key>
					<string>texture_v</string>
					<key>shader_f</key>
					<string>bothway_shadow_highlight_f</string>
					<key>material</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/shadows100.png</string>
						</dict>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData2</string>
							<key>materialPath</key>
							<string>style/shadows_100.png</string>
						</dict>
					</array>
					<key>float</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>alpha0</string>
							<key>floatValue</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>integer</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>type</string>
							<key>integerValue</key>
							<integer>1</integer>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<dict>
			<key>EffectClass</key>
			<string>EffectTextureTuneGroup</string>
			<key>EffectName</key>
			<string>褪色</string>
			<key>EffectGroup</key>
			<array>
				<dict>
					<key>shader_v</key>
					<string>texture_v</string>
					<key>shader_f</key>
					<string>mapy_f</string>
					<key>material</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>mt_tempData1</string>
							<key>materialPath</key>
							<string>style/fade100.png</string>
						</dict>
					</array>
					<key>float</key>
					<array>
						<dict>
							<key>uniformName</key>
							<string>alpha</string>
							<key>floatValue</key>
							<integer>1</integer>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

var Zi=Object.defineProperty;var Xi=(A,a,e)=>a in A?Zi(A,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):A[a]=e;var Ye=(A,a,e)=>(Xi(A,typeof a!="symbol"?a+"":a,e),e);import{C as ea,P as Ga,c as FA,p as Ta,a as xa,b as De,A as $a,d as IA,e as $i,u as nA,r as o,N as ut,U as _t,s as mt,f as aa,g as Fa,h as ke,j as t,I as An,i as en,k as qt,l as Qt,S as ZA,T as JA,F as je,m as GA,n as an,o as N,q as Ua,t as tn,v as nn,w as Ha,x as At,y as on,z as jt,B as sn,D as rn,E as cn,G as ln,H as Ca,J as Pa,K as dn,L as ie,M as EA,O as Ke,Q as un,R as _n,V as Aa,W as gt,X as mn,Y as gn,Z as Vt,_ as Jt,$ as pn,a0 as hn,a1 as fn,a2 as bn,a3 as yn,a4 as kn,a5 as vn,a6 as wn,a7 as Sn,a8 as Tn}from"./vendor-DNrNpj3i.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))i(n);new MutationObserver(n=>{for(const s of n)if(s.type==="childList")for(const r of s.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function e(n){const s={};return n.integrity&&(s.integrity=n.integrity),n.referrerPolicy&&(s.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?s.credentials="include":n.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function i(n){if(n.ep)return;n.ep=!0;const s=e(n);fetch(n.href,s)}})();var xn={VITE_APP_BASE_API:"https://openapi.mtlab.meitu.com/",VITE_APP_API_KEY:"2edbcf550aa54575906eebbad6f98f9d",VITE_APP_API_SECRET:"4d9abcdf56d64bb7897ff84e04b50a02",BASE_URL:"/undefinedminiapp.snapid.ai/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};(()=>{var a;const A=()=>{var e,i;(i=(e=window.Sentry)==null?void 0:e.forceLoad)==null||i.call(e)};(a=window==null?void 0:window.Sentry)==null||a.onLoad(()=>{var e,i;(i=(e=window.Sentry)==null?void 0:e.init)==null||i.call(e,{environment:"prod",maxBreadcrumbs:10,release:xn.VITE_APP_VERSION||"1.0.000",sampleRate:.5,tracesSampleRate:0})}),document.readyState==="complete"?A():window.addEventListener("load",A)})();const Cn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAV1BMVEUAAADX19fY2NjX19fa2tre3t7Y2NjZ2dnY2NjY2NjX19fZ2dnY2NjZ2dnZ2dnY2NjX19fw8PAAAAB4eHjr6+vY2Njl5eXb29vi4uKWlpbf3993d3eVlZUwsvPkAAAAEXRSTlMAIGCA7xDff8+/kO+vn1Dv399rOeAAAAGASURBVEjHlZbrkoIwDEZDC5WLqIGkVNz3f87VrrOBSYFyfsH4nV6kJICiMu310TGza1pTwAHVpeHgifANkZ+nWpxkvAsxK1DgflO5lB415PmSjBf1RJiEgktMYmR4jS9vajkyfAqaLmfy2rjpvDbMYr+lzmujk507jxn4+n8DAbOY7HdBTHkClVUUeo+ZeKsneD7lWt/HKUxY/j4M4zIxDsPSCJ8paloJ0ZD8WqAGoJhwySiG3AhcgZlRG3L5whXBQOtxwxBV8C00hCkjnUe6gouCNvR6ouCAEZNGHF9zF0EZqTzyeeG+vyQtPHY2PaY2ffWp/Gvrb212Htwr+eB2j4Y2ZgMVnzl8UwHQnDnenzpgAwo/By+QeQtVibmvKHEsTTa7CIT+r410dJSUCSJ2zpzAwpc6r1S6k8W4XLQhk1PuV03IHjcUC5BvSF647TdFA4rCha22O9fp3m7Zp5RQWtig6Fl/OnS2koRWTD3N8nESuNFx7bSNY+a7u7ZGp38BldJ6jml0zrsAAAAASUVORK5CYII=",za=()=>ea.isNativePlatform()?{getItem(A){return Ga.get({key:A}).then(a=>a.value)},setItem(A,a){return Ga.set({key:A,value:a})},removeItem(A){return Ga.remove({key:A})}}:localStorage,Pn=FA(Ta((A,a)=>({agreed:!1,setAgreed(e){A({agreed:e})}}),{name:"snapid.agreement",storage:xa(za)})),zn=["en","ja","ko","th","zh-Hant","tr","id","vi","es","pt","fr","de","ru"],jn=["KH","CN","ID","JP","LA","MY","MM","NP","PH","SG","KR","TW","TH","VN"],ma=["CA","US","GB","IE","AT","BE","FR","DE","LU","NL","CH","DK","FI","IS","NO","SE"],ga=["BN","KH","ID","LA","MM","PH","VN","BY","BG","CZ","HU","MD","PL","RO","RU","SK","UA","AR","BO","BR","CL","CO","EC","GY","PY","PE","SR","UY","VE"],Wt="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/blue-HJAwJrQK.jpg",Bn=Object.freeze(Object.defineProperty({__proto__:null,default:Wt},Symbol.toStringTag,{value:"Module"})),Yt="data:image/jpeg;base64,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",Mn=Object.freeze(Object.defineProperty({__proto__:null,default:Yt},Symbol.toStringTag,{value:"Module"})),Zt="data:image/jpeg;base64,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",In=Object.freeze(Object.defineProperty({__proto__:null,default:Zt},Symbol.toStringTag,{value:"Module"})),Xt="data:image/jpeg;base64,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",En=Object.freeze(Object.defineProperty({__proto__:null,default:Xt},Symbol.toStringTag,{value:"Module"})),$t="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/deep_blue-DdEmWn3z.jpg",Nn=Object.freeze(Object.defineProperty({__proto__:null,default:$t},Symbol.toStringTag,{value:"Module"})),Ai="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/deongaree-86ysCwfF.jpg",Rn=Object.freeze(Object.defineProperty({__proto__:null,default:Ai},Symbol.toStringTag,{value:"Module"})),ei="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/golden-DV61qc1S.jpg",Dn=Object.freeze(Object.defineProperty({__proto__:null,default:ei},Symbol.toStringTag,{value:"Module"})),ai="data:image/jpeg;base64,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",Ln=Object.freeze(Object.defineProperty({__proto__:null,default:ai},Symbol.toStringTag,{value:"Module"})),ti="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/grey-Uvo290Tq.jpg",On=Object.freeze(Object.defineProperty({__proto__:null,default:ti},Symbol.toStringTag,{value:"Module"})),pt="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/us_deep_blue-CkkD-xIA.jpg",Gn=Object.freeze(Object.defineProperty({__proto__:null,default:pt},Symbol.toStringTag,{value:"Module"})),ht="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/us_gray_blue-BZkvoDLl.jpg",Fn=Object.freeze(Object.defineProperty({__proto__:null,default:ht},Symbol.toStringTag,{value:"Module"})),ft="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/us_gray_white-DWjyksK7.jpg",Un=Object.freeze(Object.defineProperty({__proto__:null,default:ft},Symbol.toStringTag,{value:"Module"})),bt="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/us_light_blue-BzHS_Gvm.jpg",Hn=Object.freeze(Object.defineProperty({__proto__:null,default:bt},Symbol.toStringTag,{value:"Module"})),ii="data:image/jpeg;base64,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",Kn=Object.freeze(Object.defineProperty({__proto__:null,default:ii},Symbol.toStringTag,{value:"Module"})),ni="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/lotus_color-BtL8mA0X.jpg",qn=Object.freeze(Object.defineProperty({__proto__:null,default:ni},Symbol.toStringTag,{value:"Module"})),oi="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/pale_blue-CSQgwbfS.jpg",Qn=Object.freeze(Object.defineProperty({__proto__:null,default:oi},Symbol.toStringTag,{value:"Module"})),si="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/pale_white-JbIHVupO.jpg",Vn=Object.freeze(Object.defineProperty({__proto__:null,default:si},Symbol.toStringTag,{value:"Module"})),ri="data:image/jpeg;base64,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",Jn=Object.freeze(Object.defineProperty({__proto__:null,default:ri},Symbol.toStringTag,{value:"Module"})),ci="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/premium_blue-By0r8jr6.jpg",Wn=Object.freeze(Object.defineProperty({__proto__:null,default:ci},Symbol.toStringTag,{value:"Module"})),li="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/purple-DnAnd5oF.jpg",Yn=Object.freeze(Object.defineProperty({__proto__:null,default:li},Symbol.toStringTag,{value:"Module"})),di="data:image/jpeg;base64,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",Zn=Object.freeze(Object.defineProperty({__proto__:null,default:di},Symbol.toStringTag,{value:"Module"})),ui="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/white-C6fwjTE0.jpg",Xn=Object.freeze(Object.defineProperty({__proto__:null,default:ui},Symbol.toStringTag,{value:"Module"})),_i="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/yellow-BxfXRKW8.jpg",$n=Object.freeze(Object.defineProperty({__proto__:null,default:_i},Symbol.toStringTag,{value:"Module"})),mi="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAoHBwkHBgoJCAkLCwoMDxkQDw4ODx4WFxIZJCAmJSMgIyIoLTkwKCo2KyIjMkQyNjs9QEBAJjBGS0U+Sjk/QD3/2wBDAQsLCw8NDx0QEB09KSMpPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT3/wgARCAMgAlgDAREAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAf/xAAWAQEBAQAAAAAAAAAAAAAAAAAAAwT/2gAMAwEAAhADEAAAAKvfIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP/xAAUEAEAAAAAAAAAAAAAAAAAAADA/9oACAEBAAE/AGpn/8QAFBEBAAAAAAAAAAAAAAAAAAAAwP/aAAgBAgEBPwBqZ//EABQRAQAAAAAAAAAAAAAAAAAAAMD/2gAIAQMBAT8Aamf/2Q==",Ao=Object.freeze(Object.defineProperty({__proto__:null,default:mi},Symbol.toStringTag,{value:"Module"})),gi="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAoHBwkHBgoJCAkLCwoMDxkQDw4ODx4WFxIZJCAmJSMgIyIoLTkwKCo2KyIjMkQyNjs9QEBAJjBGS0U+Sjk/QD3/2wBDAQsLCw8NDx0QEB09KSMpPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT3/wgARCAMgAlgDAREAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAf/xAAWAQEBAQAAAAAAAAAAAAAAAAAAAQL/2gAMAwEAAhADEAAAAKrNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD//xAAUEAEAAAAAAAAAAAAAAAAAAADA/9oACAEBAAE/AGpn/8QAFBEBAAAAAAAAAAAAAAAAAAAAwP/aAAgBAgEBPwBqZ//EABQRAQAAAAAAAAAAAAAAAAAAAMD/2gAIAQMBAT8Aamf/2Q==",eo=Object.freeze(Object.defineProperty({__proto__:null,default:gi},Symbol.toStringTag,{value:"Module"})),pi="data:image/jpeg;base64,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",ao=Object.freeze(Object.defineProperty({__proto__:null,default:pi},Symbol.toStringTag,{value:"Module"})),hi="data:image/jpeg;base64,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",to=Object.freeze(Object.defineProperty({__proto__:null,default:hi},Symbol.toStringTag,{value:"Module"})),fi="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAoHBwkHBgoJCAkLCwoMDxkQDw4ODx4WFxIZJCAmJSMgIyIoLTkwKCo2KyIjMkQyNjs9QEBAJjBGS0U+Sjk/QD3/2wBDAQsLCw8NDx0QEB09KSMpPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT3/wgARCAMgAlgDAREAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAf/xAAWAQEBAQAAAAAAAAAAAAAAAAAABAX/2gAMAwEAAhADEAAAAKjn2gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/xAAUEAEAAAAAAAAAAAAAAAAAAADA/9oACAEBAAE/AGpn/8QAFBEBAAAAAAAAAAAAAAAAAAAAwP/aAAgBAgEBPwBqZ//EABQRAQAAAAAAAAAAAAAAAAAAAMD/2gAIAQMBAT8Aamf/2Q==",io=Object.freeze(Object.defineProperty({__proto__:null,default:fi},Symbol.toStringTag,{value:"Module"})),pA=[{bg:ui,name:"white",label:"白色"},{bg:si,name:"pale_white",label:"暗角白"},{bg:ti,name:"grey",label:"灰色"},{bg:Zt,name:"dark_gray",label:"深灰色",isMain:!0},{bg:ii,name:"light_blue",label:"淡蓝",isMain:!0},{bg:Yt,name:"cyan",label:"青色",isMain:!0},{bg:mi,name:"sky_blue",label:"天蓝色"},{bg:oi,name:"pale_blue",label:"浅蓝"},{bg:Wt,name:"blue",label:"中白边蓝"},{bg:Ai,name:"deongaree",label:"深蓝"},{bg:ci,name:"premium_blue",label:"亮蓝"},{bg:$t,name:"deep_blue",label:"暗蓝"},{bg:hi,name:"real_yellow",label:"黄色"},{bg:ai,name:"goose_yellow",label:"鹅黄色",isMain:!0},{bg:_i,name:"yellow",label:"暗黄色"},{bg:di,name:"turf_green",label:"草色绿"},{bg:ei,name:"golden",label:"亮黄色"},{bg:fi,name:"light_orange",label:"浅橙色"},{bg:ri,name:"pink",label:"粉色"},{bg:gi,name:"sand",label:"沙色"},{bg:Xt,name:"dark_pink",label:"暗粉色"},{bg:pi,name:"rose_red",label:"玫红色"},{bg:ni,name:"lotus_color",label:"藕粉色"},{bg:li,name:"purple",label:"紫色"},{bg:bt,name:"us_light_blue",label:"美式浅蓝"},{bg:ht,name:"us_gray_blue",label:"美式灰蓝"},{bg:pt,name:"us_deep_blue",label:"美式深蓝"},{bg:ft,name:"us_gray_white",label:"美式灰白"}],no=Object.freeze(Object.defineProperty({__proto__:null,IDPHOTO_BACKGROUND_GROUP:pA,US_DEEP_BLUE:pt,US_GRAY_BLUE:ht,US_GRAY_WHITE:ft,US_LIGHT_BLUE:bt},Symbol.toStringTag,{value:"Module"})),oo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAAAM1BMVEUAAAD///////////////////////////////////////////////////////////////+3leKCAAAAEHRSTlMAzxDfIGDvgEC/r6Bwf1+P3d0GHgAAAKtJREFUSMft0ssOwjAQQ9HmVZo0gP//a0GAsKK0Qk4luhmv56zuTDab7fTVktMg9Q7PuSEbA16rByyKbpcZn2XZ3vBdUu2V1h2wqxcTrbQXOe+4jYE2jdt5EZ+WeRHitn0/rd/K+9MmgHov716izAM9bwG1nLeCWs/rGi3m9a3u8wq6y6voLq+i721eTXd5Zc1EuqbVNfPqmnl1zUS6ptU18+pLudTJZrP9fQ+j0hnapwgdNAAAAABJRU5ErkJggg==",so=Object.freeze(Object.defineProperty({__proto__:null,default:oo},Symbol.toStringTag,{value:"Module"})),pa="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIwAAAC8CAMAAACdZLahAAAAS1BMVEUAAAD29vb19fX39/f19fX29vb19fX19fX39/f09PTz8/P19fXMzMzb29vw8PDPz8/S0tLs7Ozf39/k5OTZ2dnW1tbh4eHm5ubo6OgGgvMQAAAAC3RSTlMAv3Ag73/fnxDvgC5SaCsAAANzSURBVHja7d3rbqMwEIZhCDm1A7axDeT+r3S3TehETb82I42FFc37c7cRT4ydcFLb3Dof2iNt0lu765r7upY27XTH2b3Rxu13q+VAFXS4jQtV0efYdJvvo2v7j3lzokpqm6ajajrXMmOuc7ilamqbPVXTsaGKMoxhDGOYbxkGZRiUYVCGQRkGZRiUYVCGQRkGZRiUYVCGQRkGZRiUYVCGQRkGZRiUYVCGQdWGcSnmEPr/hTDFNJIgXUwafP8tnxMJ0sKM8Sp5bBCMjwpmnPpfmiCnAIYpqOwIpI2JGMFFAqliXOg5/7GE3Oc/j5c43WuCI5AeZvEsyQ9z4zLcQROBtDAzb2umn3JLALtKHTNjCjcHoNHFzM+tFjcAjSZmfno2JA80api0rpOR/iz3txbiFDHOP79meVF5VwQTJBYulMDMMgsX9TEOjTq25Nt+HXUxPCPT85ZhnfGTNma8bUBg4XcwKmMmNGGwhRfgpIjhgYkiC8/6UQ3D2wgyCw9NVMPwUlpkFh4ar4hZl4UTWnhokiIm8zZEFn6pIub69i5SC099r4ThtSS38H5yKhieMpPcwp9QSQ0TeX0KLfxiNczEU0Zm4WHNapjAH6MyC0+4oIxxEgvnlDFgMSELfnV5DFu2x7BlewxbSmHwBJZbXJHVNAosBZd25k90sYUu/F1S5OsAW/CLS31RYgv+LllUMDwHvdzCR6yj9sHViC24i+rBFW84iy38UvUDci+28HmFBgac/gALPq8gLQyvTy+2UOCfK3B6K7AUOb3lTwvvZBYX9E/8eWiyyEJDgUsiPDR9klgSvwNFDA+Nz8iCL486KnWBUWAJxS4wkgtSS8FLr+S8zFL0ojQtqyXTn41XCx+SvfSNDNb0g6PfyiVu8WBNiASLHljK3RYMyUFK+duCPBvWhsvDvM38334hkO6tZG6Kl/VWcoqTL3grGe+qv5sJpIwhl3tO9DTEqz+Y8ckZgMTHgo+s4JbsHyRD8Yd5cOMSp/UxpxyToyd7/QfADPNjhkEZBmUYlGFQhkEZBmUYlGFQhkEZBmUYlGFQhkEZBmUYlGFQhkEZBmUYlGFQhkEZBvXimCNV076uX4r+TtW0a85UTV1Tz346NU3TVfLb6/ddbX+WoqljDh+aW7vN99R+13zVnWjT2m9/c2bXbjQ8x/b9fEP8Azlcc2GsPTyXAAAAAElFTkSuQmCC",ro=Object.freeze(Object.defineProperty({__proto__:null,default:pa},Symbol.toStringTag,{value:"Module"})),bi="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/model1-Cf5UcON6.jpg",co=Object.freeze(Object.defineProperty({__proto__:null,default:bi},Symbol.toStringTag,{value:"Module"})),yi="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/model2-CJd0tRFP.jpg",lo=Object.freeze(Object.defineProperty({__proto__:null,default:yi},Symbol.toStringTag,{value:"Module"})),ki="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/model3-B5W9l3xL.jpg",uo=Object.freeze(Object.defineProperty({__proto__:null,default:ki},Symbol.toStringTag,{value:"Module"})),vi="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/model4-B7qoYIPW.jpg",_o=Object.freeze(Object.defineProperty({__proto__:null,default:vi},Symbol.toStringTag,{value:"Module"})),$A=[{model:pa},{model:bi,style_id:2,label:"刘海1",id:"1HIB0000001"},{model:yi,style_id:0,label:"刘海2",id:"1HIB0000002"},{model:ki,style_id:4,label:"刘海3",id:"1HIB0000003"},{model:vi,style_id:3,label:"刘海4",id:"1HIB0000004"}],mo=Object.freeze(Object.defineProperty({__proto__:null,BANGS_ASSETS:$A},Symbol.toStringTag,{value:"Module"})),go="data:image/svg+xml,%3csvg%20width='34'%20height='34'%20viewBox='0%200%2034%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='17'%20cy='17'%20r='15.5'%20stroke='currentColor'%20stroke-width='3'/%3e%3cpath%20d='M7%2027L27%207'%20stroke='currentColor'%20stroke-width='3'/%3e%3c/svg%3e",po=Object.freeze(Object.defineProperty({__proto__:null,default:go},Symbol.toStringTag,{value:"Module"}));var kA=(A=>(A.mm="mm",A.inch="inch",A.px="px",A))(kA||{});const ho={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. Das Generieren von KI-Outfits kann aufgrund der großen Anzahl von Anfragen „eine Weile dauern“.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. Die Verwendung von KI-generierten Outfits für offizielle Dokumente kann das „Risiko einer Einreichungsablehnung“ erhöhen. Wir empfehlen, auf der offiziellen Website nach detaillierten Dokumentenanforderungen zu schauen, bevor Sie fortfahren.",IDPhoto:"Ausweisfoto",JPPosterSize:"89x127mm","Keep waiting":"Weiter warten","Server is busy.":"Server ist beschäftigt.","Stop generating":"Generieren stoppen",THPosterSize:"10,16 x 15,24 cm",adjustTips:"Bitte bewegen Sie das Gesicht an den Rand",adjust_tips:"Bei der Beantragung von amtlichen Dokumenten ist darauf zu achten, dass das Lichtbild nicht seitenverkehrt oder gespiegelt ist.",adjust_title:"Position anpassen",afghanistan:"🇦🇫 Afghanistan",africa:"Afrika",agree:"Zustimmen",agreeTOS:'Indem Sie fortfahren, stimmen Sie unseren <a href="https://snapid.ai/terms-of-service">Nutzungsbedingungen</a> und unserer <a href="https://snapid.ai/privacy-policy">Datenschutzrichtlinie</a> zu.',albania:"🇦🇱 Albanien",albumPermissionContent:"Vergewissern Sie sich, dass Sie der App erlaubt haben, auf die Kamera und Alle Fotos zuzugreifen.",albumPermissionTitle:"Erlaubnis erforderlich.",algeria:"🇩🇿 Algerien",almost_there_just_1_second_to_go:"Fast da! Nur noch 1 Sekunde",american_samoa:"🇦🇸 Amerikanisch-Samoa",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua und Barbuda",argentina:"🇦🇷 Argentinien",armenia:"🇦🇲 Armenien",asia:"Asien",australia:"🇦🇺 Australien",austria:"🇦🇹 Österreich",azerbaijan:"🇦🇿 Aserbaidschan",background:"Hintergrund",background_updated:"Hintergrund wurde aktualisiert",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Bahrain",bangladesh:"🇧🇩 Bangladesch",bangs:"Pony",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Belarus",belgium:"🇧🇪 Belgien",belize:"🇧🇿 Belize",benin:"🇧🇯 Benin",bermuda:"🇧🇲 Bermuda",bhutan:"🇧🇹 Bhutan",bolivia:"🇧🇴 Bolivien",bosnia_herzegovina:"🇧🇦 Bosnien und Herzegowina",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brasilien",british_virgin_islands:"🇻🇬 Britische Jungferninseln",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgarien",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Kap Verde",cambodia:"🇰🇭 Kambodscha",cameroon:"🇨🇲 Kamerun",canada:"🇨🇦 Kanada",cancel:"Abbrechen",cancelGeneratorTips:"Sind Sie sicher, dass Sie die Generierung stoppen wollen?",central_african_republic:"🇨🇫 Zentralafrikanische Republik",chad:"🇹🇩 Tschad",chile:"🇨🇱 Chile",china:"🇨🇳 China",christmas_island:"🇨🇽 Weihnachtsinsel",close:"Schließen",closer:"Näher",cocos_keeling_islands:"🇨🇨 Kokosinseln",colombia:"🇨🇴 Kolumbien",colorChange:"Farbe wechseln",comoros:"🇰🇲 Komoren",confirm:"Bestätigen",congo_brazzaville:"🇨🇬 Kongo-Brazzaville",congo_kinshasa:"🇨🇩 Kongo-Kinshasa",cook_islands:"🇨🇰 Cookinseln",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Kroatien",cte_divoire:"🇨🇮 Elfenbeinküste",cuba:"🇨🇺 Kuba",custom:"Benutzerdefiniert",custom_size:"Benutzerdefinierte Größe",cyprus:"🇨🇾 Zypern",czech_republic:"🇨🇿 Tschechien",delete:"Löschen",denmark:"🇩🇰 Dänemark",digitalPhoto:"Digitalbild",digitalPhotoTips:"Fotocollage zum Ausdrucken",djibouti:"🇩🇯 Dschibuti",dominica:"🇩🇲 Dominica",dominican_republic:"🇩🇴 Dominikanische Republik",ecuador:"🇪🇨 Ecuador",edit_title:"Bearbeiten",egypt:"🇪🇬 Ägypten",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Äquatorialguinea",eritrea:"🇪🇷 Eritrea",error1:"Bildverarbeitung fehlgeschlagen.",error2:"Gesicht konnte nicht erkannt werden.",error3:"Fotos mit mehreren Personen werden nicht akzeptiert.",estonia:"🇪🇪 Estland",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Äthiopien",europe:"Europa",exitFirstMenuTips:"Sind Sie sicher, dass Sie ohne zu speichern beenden wollen?",failed:"Fehlgeschlagen",failed_to_generate:"Generieren fehlgeschlagen.",falkland_islands:"🇫🇰 Falklandinseln",faroe_islands:"🇫🇴 Färöer-Inseln",farther:"Weiter",female:"Weiblich",fiji:"🇫🇯 Fidschi",finland:"🇫🇮 Finnland",france:"🇫🇷 Frankreich",french_polynesia:"🇵🇫 Französisch-Polynesien",gabon:"🇬🇦 Gabun",gambia:"🇬🇲 Gambia",generateFailed:"Erstellung fehlgeschlagen",generate_id_photo_desc:`1. Fotografiert bei gleichmäßigem, weichem Licht<br/>
2. Keine übermäßige Retusche oder Ausradierung von Gesichtszügen.`,georgia:"🇬🇪 Georgien",germany:"🇩🇪 Deutschland",getStarted:"Auf geht's",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Verstanden",greece:"🇬🇷 Griechenland",greenland:"🇬🇱 Grönland",grenada:"🇬🇩 Grenada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haiti",head_position:"Kopfposition",height:"Höhe",height_range_should_be_within_10120:"Die Höhe sollte zwischen {min} und {max} liegen.",holy_see_vatican_city:"🇻🇦 Vatikanstadt",homeTitle:"Passfoto",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hongkong SAR China",hungary:"🇭🇺 Ungarn",iceland:"🇮🇸 Island",idPhotoTip1:"1. Lassen Sie sich aus 1-2 m Entfernung von einer anderen Person fotografieren.",idPhotoTip2:"2. Suchen Sie sich einen einfachen weißen oder hellgrauen Hintergrund.",idPhotoTip3:"3. Entfernen Sie alle Accessoires. Achten Sie darauf, dass die Haare nach hinten gesteckt sind.",idPhotoTip4:"4. Das Gesicht darf nicht durch Schatten, Stirnhaare oder Kleidung verdeckt werden.",idPhotoTip5:"5. Neutraler Ausdruck mit offenen Augen und geschlossenem Mund.",idPhotoTip6:"6. Keine Flecken, Spiegelungen von Brillengläsern oder „rote Augen“.",idTaskEmpty:"Hier gibt es nichts zu sehen! <br />Erstellen Sie Ausweisfotos.",inch:"Zoll",india:"🇮🇳 Indien",indonesia:"🇮🇩 Indonesien",iran:"🇮🇷 Iran",iraq:"🇮🇶 Irak",ireland:"🇮🇪 Irland",isle_of_man:"🇮🇲 Isle of Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Italien",jamaica:"🇯🇲 Jamaika",japan:"🇯🇵 Japan",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordanien",kazakhstan:"🇰🇿 Kasachstan",kenya:"🇰🇪 Kenia",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kirgisistan",laos:"🇱🇦 Laos",later:"Später",latvia:"🇱🇻 Lettland",lebanon:"🇱🇧 Libanon",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libyen",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Litauen",loading:"Laden",loadingText1:"Ihre Anfrage ist unterwegs",loadingText2:"Bitte halten Sie die App für eine rasche Fertigstellung geöffnet",loadingText3:"Fast da! Nur noch{time} Sekunden",loadingText4:"Ihr KI-Outfit wird zusammengenäht",loadingText5:"Schneller, als ein Gang zum Fotostudio!",loadingText6:"Nur noch ein bisschen länger, Ihre Geduld ist sehr geschätzt",loadingText7:"Großartige Ergebnisse sind das Warten wert!",loadingText8:"Ihr KI-generiertes Outfit wird aufpoliert, fast fertig!",loadingText9:"Vielen Dank für Ihre Geduld, während wir Ihre Anfrage fertigstellen",loginSaveTip1:"Ihr Erstellungsdatensatz wird <span>7 Tage</span> lang gespeichert; bitte notieren Sie sich das Erstellungsdatum und speichern Sie den Datensatz rechtzeitig.",luxembourg:"🇱🇺 Luxemburg",macau_sar_china:"🇲🇴 Macau SAR China",madagascar:"🇲🇬 Madagaskar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaysia",maldives:"🇲🇻 Malediven",male:"Männlich",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Marshallinseln",mauritania:"🇲🇷 Mauretanien",mauritius:"🇲🇺 Mauritius",mayotte:"🇾🇹 Mayotte",medium:"Mittel",mexico:"🇲🇽 Mexiko",micronesia:"🇫🇲 Mikronesien",mm:"mm",moldova:"🇲🇩 Moldau",monaco:"🇲🇨 Monaco",mongolia:"🇲🇳 Mongolei",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Mehr Farben",mozambique:"🇲🇿 Mosambik",myanmar_burma:"🇲🇲 Myanmar",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Niederlande",network_error:"Netzwerk getrennt",network_error_info:"Netzwerkfehler.",network_error_message:"Bitte überprüfen Sie Ihre Netzwerkeinstellungen.",network_retry:"Erneut versuchen",new_caledonia:"🇳🇨 Neukaledonien",new_zealand:"🇳🇿 Neuseeland",next:"Weiter",nicaragua:"🇳🇮 Nicaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"NEIN",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Keine Ergebnisse gefunden. Bitte versuchen Sie es mit einem anderen Stichwort, oder passen Sie Ihre eigene Größe an.",no_search_results_found_please_try_a_different_keyword:"Keine Suchergebnisse gefunden. Bitte versuchen Sie ein anderes Stichwort.",norfolk_island:"🇳🇫 Norfolkinsel",north_america:"Nordamerika",north_korea:"🇰🇵 Nordkorea",north_macedonia:"🇲🇰 Nordmazedonien",northern_mariana_islands:"🇲🇵 Nördliche Marianen",norway:"🇳🇴 Norwegen",notice:"Mitteilung",oceania:"Ozeanien",ok:"VERSTANDEN",oman:"🇴🇲 Oman",outfit:"Outfit",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Palästinensische Gebiete",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papua-Neuguinea",paraguay:"🇵🇾 Paraguay",payment:"Zahlung",paymentFailure:"Zahlung fehlgeschlagen!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Philippinen",photoResize:"Größe ändern",photo_size:"Bildgröße",pitcairn_islands:"🇵🇳 Pitcairninseln",poland:"🇵🇱 Polen",portugal:"🇵🇹 Portugal",posterSize:"12,7 x 17,78 cm",preview_link:"Besuchen Sie die offizielle Webseite",printSize:"Druckgröße",privacy_policy:"Datenschutzrichtlinie",puerto_rico:"🇵🇷 Puerto Rico",purchase:"Kaufen",px:"px",qatar:"🇶🇦 Katar",reset:"Zurücksetzen",results:"Ergebnisse",revert:"Horizontal spiegeln",romania:"🇷🇴 Rumänien",runion:"🇷🇪 Réunion",russia:"🇷🇺 Russland",rwanda:"🇷🇼 Ruanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Saudi-Arabien",save:"Speichern",save_failed:"Speichern fehlgeschlagen",save_success:"Erfolgreich gespeichert",search:"Suchen",search_for_a_country_or_region:"Suchen Sie nach einem Land oder Region",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbien",settings:"Einstellungen",seychelles:"🇸🇨 Seychellen",share:"Teilen",shootingTips:"Tipps zum Fotografieren",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapur",size:"Größe",skin_retouch:"Haut",slovakia:"🇸🇰 Slowakei",slovenia:"🇸🇮 Slowenien",so_tom_prncipe:"🇸🇹 São Tomé und Príncipe",solomon_islands:"🇸🇧 Salomonen",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 Südafrika",south_america:"Südamerika",south_korea:"🇰🇷 Südkorea",south_sudan:"🇸🇸 Südsudan",spain:"🇪🇸 Spanien",specialNotes:"Besondere Hinweise",specifications:"Spezifikationen",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 St. Kitts und Nevis",st_lucia:"🇱🇨 St. Lucia",st_pierre_miquelon:"🇵🇲 St. Pierre und Miquelon",st_vincent_grenadines:"🇻🇨 St. Vincent und die Grenadinen",sudan:"🇸🇩 Sudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Spitzbergen und Jan Mayen",sweden:"🇸🇪 Schweden",switzerland:"🇨🇭 Schweiz",syria:"🇸🇾 Syrien",taiwan:"🇹🇼 Taiwan",tajikistan:"🇹🇯 Tadschikistan",tanzania:"🇹🇿 Tansania",taskExplore:"Erkunden",thailand:"🇹🇭 Thailand",timorleste:"🇹🇱 Osttimor",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Gesamt",trinidad_tobago:"🇹🇹 Trinidad und Tobago",try_again:"Erneut versuchen",tunisia:"🇹🇳 Tunesien",turkey:"🇹🇷 Türkei",turkmenistan:"🇹🇲 Turkmenistan",turks_caicos_islands:"🇹🇨 Turks- und Caicosinseln",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ukraine",unit:"Fotokopien",united_arab_emirates:"🇦🇪 Vereinigte Arabische Emirate",united_kingdom:"🇬🇧 Vereinigtes Königreich",united_states:"🇺🇸 Vereinigte Staaten",update:"Aktualisieren",updateCancelButton:"Später vielleicht",updateComfirmButton:"Jetzt aktualisieren",updateMessage:"Neue Inhalte sind da! Tippen Sie auf „Jetzt aktualisieren“, um die neuesten Ressourcen zu laden.",uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 Amerikanische Jungferninseln",uzbekistan:"🇺🇿 Usbekistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnam",wallis_futuna:"🇼🇫 Wallis und Futuna",whiteEdgeTips:"Bitte stellen Sie ein Porträtfoto mit einem breiteren Bereich bereit",width:"Breite",width_range_should_be_within_10120:"Die Breite sollte zwischen {min} und {max} liegen.",yemen:"🇾🇪 Jemen",yes:"Ja",zambia:"🇿🇲 Sambia",zimbabwe:"🇿🇼 Simbabwe",agreement:"Die Nutzung dieser Funktion setzt voraus, dass Sie die {termsOfService} und {privacyPolicy} kennen und ihnen zustimmen.",more:"Mehr",homeCaption:"Powered by",termsOfService:"SnapID Nutzungsbedingungen",history:"Verlauf"},fo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. Generating AI Outfits may <span>take a while</span> due to the large volume of requests.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. Using AI-Generated outfits for official documents may increase the <span>risk of submission rejection</span>. We recommend checking the official website for detailed document requirements before proceeding.",IDPhoto:"ID Photo",JPPosterSize:"89x127mm","Keep waiting":"Keep waiting","Server is busy.":"Server is busy.","Stop generating":"Stop generating",THPosterSize:"4x6in",adjustTips:"Please adjust the face along the border",adjust_tips:"When applying for official documents, please make sure that the photo is not left-right reversed or a mirrored image.",adjust_title:"Adjust Position",afghanistan:"🇦🇫 Afghanistan",africa:"Africa",agree:"Agree",agreeTOS:'By continuing, you agree to our <a href="https://snapid.ai/terms-of-service">Terms of Service</a> and <a href="https://snapid.ai/privacy-policy">Privacy Policy</a>.',albania:"🇦🇱 Albania",albumPermissionContent:"Please make sure you have given the app access to Camera and All Photos.",albumPermissionTitle:"Permission needed.",algeria:"🇩🇿 Algeria",almost_there_just_1_second_to_go:"Almost there! Just 1 second to go",american_samoa:"🇦🇸 American Samoa",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua & Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armenia",asia:"Asia",australia:"🇦🇺 Australia",austria:"🇦🇹 Austria",azerbaijan:"🇦🇿 Azerbaijan",background:"Background",background_updated:"Background Updated",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Bahrain",bangladesh:"🇧🇩 Bangladesh",bangs:"Bangs",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Belarus",belgium:"🇧🇪 Belgium",belize:"🇧🇿 Belize",benin:"🇧🇯 Benin",bermuda:"🇧🇲 Bermuda",bhutan:"🇧🇹 Bhutan",bolivia:"🇧🇴 Bolivia",bosnia_herzegovina:"🇧🇦 Bosnia & Herzegovina",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brazil",british_virgin_islands:"🇻🇬 British Virgin Islands",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgaria",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Cambodia",cameroon:"🇨🇲 Cameroon",canada:"🇨🇦 Canada",cancel:"Cancel",cancelGeneratorTips:"Are you sure you want to stop the generation?",central_african_republic:"🇨🇫 Central African Republic",chad:"🇹🇩 Chad",chile:"🇨🇱 Chile",china:"🇨🇳 China",christmas_island:"🇨🇽 Christmas Island",close:"Close",closer:"Closer",cocos_keeling_islands:"🇨🇨 Cocos (Keeling) Islands",colombia:"🇨🇴 Colombia",colorChange:"Color Change",comoros:"🇰🇲 Comoros",confirm:"Confirm",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Cook Islands",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Croatia",cte_divoire:"🇨🇮 Côte d’Ivoire",cuba:"🇨🇺 Cuba",custom:"Custom",custom_size:"Custom Size",cyprus:"🇨🇾 Cyprus",czech_republic:"🇨🇿 Czech Republic",delete:"Delete",denmark:"🇩🇰 Denmark",digitalPhoto:"Digital Photo",digitalPhotoTips:"Collage photo for printing",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominica",dominican_republic:"🇩🇴 Dominican Republic",ecuador:"🇪🇨 Ecuador",edit_title:"Edit",egypt:"🇪🇬 Egypt",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Equatorial Guinea",eritrea:"🇪🇷 Eritrea",error1:"Image processing failed.",error2:"Face could not be detected.",error3:"Photos with multiple people will not be accepted.",estonia:"🇪🇪 Estonia",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Ethiopia",europe:"Europe",exitFirstMenuTips:"Are you sure you want to exit without saving?",failed:"Failed",failed_to_generate:"Failed to generate.",falkland_islands:"🇫🇰 Falkland Islands",faroe_islands:"🇫🇴 Faroe Islands",farther:"Farther",female:"Female",fiji:"🇫🇯 Fiji",finland:"🇫🇮 Finland",france:"🇫🇷 France",french_polynesia:"🇵🇫 French Polynesia",gabon:"🇬🇦 Gabon",gambia:"🇬🇲 Gambia",generateFailed:"Generation Failed",generate_id_photo_desc:`1. Photographed under even, soft lighting<br/>
2. No over-retouching or erasing facial features.`,georgia:"🇬🇪 Georgia",germany:"🇩🇪 Germany",getStarted:"Get Started",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Got it",greece:"🇬🇷 Greece",greenland:"🇬🇱 Greenland",grenada:"🇬🇩 Grenada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haiti",head_position:"Head Position",height:"Height",height_range_should_be_within_10120:"Height range should be within {min}~{max}.",holy_see_vatican_city:"🇻🇦 Holy See (Vatican City)",homeTitle:"Passport Photo",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong SAR China",hungary:"🇭🇺 Hungary",iceland:"🇮🇸 Iceland",idPhotoTip1:"1. Have someone else take your photo from 1-2 m away. ",idPhotoTip2:"2. Find a plain white or light grey background. ",idPhotoTip3:"3. Remove all accessories. Make sure hair is pulled back. ",idPhotoTip4:"4. No shadows, front hair, or clothing obscure the face.",idPhotoTip5:"5. Neutral expression with eyes open and mouth closed.",idPhotoTip6:"6. No marks, reflection from glasses,  or 'red eye'.",idTaskEmpty:"Nothing to see here! <br/>Go to create ID photos.",inch:"inch",india:"🇮🇳 India",indonesia:"🇮🇩 Indonesia",iran:"🇮🇷 Iran",iraq:"🇮🇶 Iraq",ireland:"🇮🇪 Ireland",isle_of_man:"🇮🇲 Isle of Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Italy",jamaica:"🇯🇲 Jamaica",japan:"🇯🇵 Japan",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordan",kazakhstan:"🇰🇿 Kazakhstan",kenya:"🇰🇪 Kenya",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kyrgyzstan",laos:"🇱🇦 Laos",later:"Later",latvia:"🇱🇻 Latvia",lebanon:"🇱🇧 Lebanon",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libya",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lithuania",loading:"Loading",loadingText1:"Your request is on the way",loadingText2:"Please keep the app open for a swift completion",loadingText3:"Almost there! Just {time} seconds to go",loadingText4:"Stitching up your AI outfit",loadingText5:"Quicker than a trip to the photo studio!",loadingText6:"Just a bit longer, your patience is much appreciated",loadingText7:"Great outcomes are worth the wait!",loadingText8:"Polishing your AI-generated outfit, almost done!",loadingText9:"Thanks for your patience as we complete your request",loginSaveTip1:"Your generation record will be saved for <span>7 days</span>; please take note of the creation date and save it promptly.",luxembourg:"🇱🇺 Luxembourg",macau_sar_china:"🇲🇴 Macau SAR China",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaysia",maldives:"🇲🇻 Maldives",male:"Male",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Marshall Islands",mauritania:"🇲🇷 Mauritania",mauritius:"🇲🇺 Mauritius",mayotte:"🇾🇹 Mayotte",medium:"Medium",mexico:"🇲🇽 Mexico",micronesia:"🇫🇲 Micronesia",mm:"mm",moldova:"🇲🇩 Moldova",monaco:"🇲🇨 Monaco",mongolia:"🇲🇳 Mongolia",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"More Colors",mozambique:"🇲🇿 Mozambique",myanmar_burma:"🇲🇲 Myanmar (Burma)",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Netherlands",network_error:"Network Disconnected",network_error_info:"Network error.",network_error_message:"Please check your network settings.",network_retry:"Retry",new_caledonia:"🇳🇨 New Caledonia",new_zealand:"🇳🇿 New Zealand",next:"Next",nicaragua:"🇳🇮 Nicaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"No",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"No results found. Please try a different keyword, or customize your own size.",no_search_results_found_please_try_a_different_keyword:"No search results found. Please try a different keyword.",norfolk_island:"🇳🇫 Norfolk Island",north_america:"North America",north_korea:"🇰🇵 North Korea",north_macedonia:"🇲🇰 North Macedonia",northern_mariana_islands:"🇲🇵 Northern Mariana Islands",norway:"🇳🇴 Norway",notice:"Notice",oceania:"Oceania",ok:"OK",oman:"🇴🇲 Oman",outfit:"Outfit",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Palestinian Territories",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papua New Guinea",paraguay:"🇵🇾 Paraguay",payment:"Payment",paymentFailure:"Payment failure!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Philippines",photoResize:"Photo Resize",photo_size:"Photo Size",pitcairn_islands:"🇵🇳 Pitcairn Islands",poland:"🇵🇱 Poland",portugal:"🇵🇹 Portugal",posterSize:"5x7in",preview_link:"Visit the official website",printSize:"Print Size",privacy_policy:"Privacy Policy",puerto_rico:"🇵🇷 Puerto Rico",purchase:"Purchase",px:"px",qatar:"🇶🇦 Qatar",reset:"Reset",results:"Results",revert:"Flip Horizontally",romania:"🇷🇴 Romania",runion:"🇷🇪 Réunion",russia:"🇷🇺 Russia",rwanda:"🇷🇼 Rwanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Saudi Arabia",save:"Save",save_failed:"Failed to save",save_success:"Saved successfully",search:"Search",search_for_a_country_or_region:"Search for a country or region",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbia",settings:"Settings",seychelles:"🇸🇨 Seychelles",share:"Share",shootingTips:"Shooting Tips",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapore",size:"Size",skin_retouch:"Skin",slovakia:"🇸🇰 Slovakia",slovenia:"🇸🇮 Slovenia",so_tom_prncipe:"🇸🇹 São Tomé & Príncipe",solomon_islands:"🇸🇧 Solomon Islands",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 South Africa",south_america:"South America",south_korea:"🇰🇷 South Korea",south_sudan:"🇸🇸 South Sudan",spain:"🇪🇸 Spain",specialNotes:"Special Notes",specifications:"Specifications",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 St. Kitts & Nevis",st_lucia:"🇱🇨 St. Lucia",st_pierre_miquelon:"🇵🇲 St. Pierre & Miquelon",st_vincent_grenadines:"🇻🇨 St. Vincent & Grenadines",sudan:"🇸🇩 Sudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard & Jan Mayen",sweden:"🇸🇪 Sweden",switzerland:"🇨🇭 Switzerland",syria:"🇸🇾 Syria",taiwan:"🇹🇼 Taiwan",tajikistan:"🇹🇯 Tajikistan",tanzania:"🇹🇿 Tanzania",taskExplore:"Explore",thailand:"🇹🇭 Thailand",timorleste:"🇹🇱 Timor-Leste",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Total",trinidad_tobago:"🇹🇹 Trinidad & Tobago",try_again:"Try Again",tunisia:"🇹🇳 Tunisia",turkey:"🇹🇷 Turkey",turkmenistan:"🇹🇲 Turkmenistan",turks_caicos_islands:"🇹🇨 Turks & Caicos Islands",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ukraine",unit:"Photocopies",united_arab_emirates:"🇦🇪 United Arab Emirates",united_kingdom:"🇬🇧 United Kingdom",united_states:"🇺🇸 United States",update:"Update",updateCancelButton:"Maybe Later",updateComfirmButton:"Update Now",updateMessage:'New content is ready! Click "Update Now" to load the latest resources.',uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 U.S. Virgin Islands",uzbekistan:"🇺🇿 Uzbekistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnam",wallis_futuna:"🇼🇫 Wallis & Futuna",whiteEdgeTips:"Please provide a portrait photo with a wider range",width:"Width",width_range_should_be_within_10120:"Width range should be within {min}~{max}.",yemen:"🇾🇪 Yemen",yes:"Yes",zambia:"🇿🇲 Zambia",zimbabwe:"🇿🇼 Zimbabwe",agreement:"Using this function requires you to know and agree to the {termsOfService} and {privacyPolicy}.",more:"More",homeCaption:"Powered by",termsOfService:"SnapID Terms of Service",history:"History"},bo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. La generación de atuendos por IA puede <span>tardar un poco</span> debido al gran volumen de solicitudes.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. El uso de atuendos generados por IA para los documentos oficiales puede aumentar el <span>riesgo de rechazo en el momento de presentar documentos oficiales</span>. Recomendamos consultar la página web oficial para conocer los requisitos detallados de los documentos antes de continuar.",IDPhoto:"Foto de identificación",JPPosterSize:"89x127mm","Keep waiting":"Seguir esperando","Server is busy.":"El servidor está ocupado.","Stop generating":"Detener la generación",THPosterSize:"4x6 pulgadas",adjustTips:"Ajusta la cara a lo largo del borde",adjust_tips:"Cuando solicites documentos oficiales, asegúrate de que la foto no esté invertida de izquierda a derecha ni sea una imagen reflejada.",adjust_title:"Ajustar posición",afghanistan:"🇦🇫 Afganistán",africa:"África",agree:"Acepto",agreeTOS:'Al continuar, aceptas nuestras <a href="https://snapid.ai/terms-of-service">Condiciones de servicio</a> y <a href="https://snapid.ai/privacy-policy">Política de privacidad</a>.',albania:"🇦🇱 Albania",albumPermissionContent:"Asegúrate de haber dado a la aplicación acceso a Cámara y Todas las fotos.",albumPermissionTitle:"Permisos necesarios.",algeria:"🇩🇿 Argelia",almost_there_just_1_second_to_go:"¡Ya casi está! Sólo faltan 1 segundo para terminar",american_samoa:"🇦🇸 Samoa Americana",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguila",antigua_barbuda:"🇦🇬 Antigua y Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armenia",asia:"Asia",australia:"🇦🇺 Australia",austria:"🇦🇹 Austria",azerbaijan:"🇦🇿 Azerbaiyán",background:"Fondo",background_updated:"Fondo actualizado",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Baréin",bangladesh:"🇧🇩 Bangladés",bangs:"Fleco",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Bielorrusia",belgium:"🇧🇪 Bélgica",belize:"🇧🇿 Belice",benin:"🇧🇯 Benín",bermuda:"🇧🇲 Bermudas",bhutan:"🇧🇹 Bután",bolivia:"🇧🇴 Bolivia",bosnia_herzegovina:"🇧🇦 Bosnia y Herzegovina",botswana:"🇧🇼 Botsuana",brazil:"🇧🇷 Brasil",british_virgin_islands:"🇻🇬 Islas Vírgenes Británicas",brunei:"🇧🇳 Brunéi",bulgaria:"🇧🇬 Bulgaria",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Camboya",cameroon:"🇨🇲 Camerún",canada:"🇨🇦 Canadá",cancel:"Cancelar",cancelGeneratorTips:"¿Seguro que quieres detener la generación?",central_african_republic:"🇨🇫 República Centroafricana",chad:"🇹🇩 Chad",chile:"🇨🇱 Chile",china:"🇨🇳 China",christmas_island:"🇨🇽 Isla de Navidad",close:"Cerrar",closer:"Más cerca",cocos_keeling_islands:"🇨🇨 Islas Cocos (Keeling)",colombia:"🇨🇴 Colombia",colorChange:"Cambio de color",comoros:"🇰🇲 Comoras",confirm:"Confirmar",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Islas Cook",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Croacia",cte_divoire:"🇨🇮 Costa de Marfil",cuba:"🇨🇺 Cuba",custom:"Personalizar",custom_size:"Tamaño personalizado",cyprus:"🇨🇾 Chipre",czech_republic:"🇨🇿 República Checa",delete:"Eliminar",denmark:"🇩🇰 Dinamarca",digitalPhoto:"Foto digital",digitalPhotoTips:"Foto collage para imprimir",djibouti:"🇩🇯 Yibuti",dominica:"🇩🇲 Dominica",dominican_republic:"🇩🇴 República Dominicana",ecuador:"🇪🇨 Ecuador",edit_title:"Editar",egypt:"🇪🇬 Egipto",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Guinea Ecuatorial",eritrea:"🇪🇷 Eritrea",error1:"Error al procesar la imagen.",error2:"No se ha podido detectar la cara.",error3:"No se aceptan fotos en las que aparezcan varias personas.",estonia:"🇪🇪 Estonia",eswatini:"🇸🇿 Esuatini",ethiopia:"🇪🇹 Etiopía",europe:"Europa",exitFirstMenuTips:"¿Seguro que quieres salir sin guardar?",failed:"Error",failed_to_generate:"Error al generar.",falkland_islands:"🇫🇰 Islas Malvinas",faroe_islands:"🇫🇴 Islas Feroe",farther:"Más lejos",female:"Femenino",fiji:"🇫🇯 Fiyi",finland:"🇫🇮 Finlandia",france:"🇫🇷 Francia",french_polynesia:"🇵🇫 Polinesia Francesa",gabon:"🇬🇦 Gabón",gambia:"🇬🇲 Gambia",generateFailed:"Error de generación",generate_id_photo_desc:`1. Fotografiar bajo una iluminación uniforme y suave<br/>
2. Sin retoques ni borrado de rasgos faciales.`,georgia:"🇬🇪 Georgia",germany:"🇩🇪 Alemania",getStarted:"Empieza a usarlo",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Entendido",greece:"🇬🇷 Grecia",greenland:"🇬🇱 Groenlandia",grenada:"🇬🇩 Granada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bisáu",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haití",head_position:"Posición de la cabeza",height:"Altura",height_range_should_be_within_10120:"El alto debe estar entre {min}~{max}.",holy_see_vatican_city:"🇻🇦 Santa Sede (Ciudad del Vaticano)",homeTitle:"Foto de pasaporte",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong SAR China",hungary:"🇭🇺 Hungría",iceland:"🇮🇸 Islandia",idPhotoTip1:"1. Pídele a otra persona que te tome la foto a 1-2 m de distancia.",idPhotoTip2:"2. Busca un fondo liso blanco o gris claro.",idPhotoTip3:"3. Quítate todos los complementos o accesorios que traigas. Asegúrate de tener el pelo recogido.",idPhotoTip4:"4. No debe haber sombras, pelo que te tape la cara o ropa que oscurezca el rostro.",idPhotoTip5:"5. Debes tener una expresión neutra con los ojos abiertos y la boca cerrada.",idPhotoTip6:"6. Sin marcas, reflejos de gafas ni <span>ojos rojos</span>.",idTaskEmpty:"No hay nada aquí. <br />Crear fotos de identidad.",inch:"pulgada",india:"🇮🇳 India",indonesia:"🇮🇩 Indonesia",iran:"🇮🇷 Irán",iraq:"🇮🇶 Irak",ireland:"🇮🇪 Irlanda",isle_of_man:"🇮🇲 Isla de Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Italia",jamaica:"🇯🇲 Jamaica",japan:"🇯🇵 Japón",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordania",kazakhstan:"🇰🇿 Kazajistán",kenya:"🇰🇪 Kenia",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kirguistán",laos:"🇱🇦 Laos",later:"Más tarde",latvia:"🇱🇻 Letonia",lebanon:"🇱🇧 Líbano",lesotho:"🇱🇸 Lesoto",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libia",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lituania",loading:"Cargando",loadingText1:"Tu solicitud está en camino",loadingText2:"Mantén la aplicación abierta para garantizar una finalización completa",loadingText3:"¡Ya casi está! Sólo faltan{time} segundos para terminar",loadingText4:"Haz medida tu atuendo por IA",loadingText5:"¡Más rápido que ir al estudio fotográfico!",loadingText6:"Sólo un poco más, muchas gracias por tu paciencia",loadingText7:"¡Merece la pena la espera para conseguir los mejores resultados!",loadingText8:"Estamos terminado tu atuendo generado por IA, ¡ya casi está listo!",loadingText9:"Te agradecemos tu paciencia mientras esperas a que terminemos tu solicitud",loginSaveTip1:"Tu historial de generación se guardará durante <span>7 días</span>; toma nota de la fecha de creación y guárdalo cuanto antes.",luxembourg:"🇱🇺 Luxemburgo",macau_sar_china:"🇲🇴 Macao SAR China",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malaui",malaysia:"🇲🇾 Malasia",maldives:"🇲🇻 Maldivas",male:"Masculino",mali:"🇲🇱 Malí",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Islas Marshall",mauritania:"🇲🇷 Mauritania",mauritius:"🇲🇺 Mauricio",mayotte:"🇾🇹 Mayotte",medium:"Medio",mexico:"🇲🇽 México",micronesia:"🇫🇲 Micronesia",mm:"mm",moldova:"🇲🇩 Moldavia",monaco:"🇲🇨 Mónaco",mongolia:"🇲🇳 Mongolia",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Más colores",mozambique:"🇲🇿 Mozambique",myanmar_burma:"🇲🇲 Myanmar (Birmania)",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Países Bajos",network_error:"Red desconectada",network_error_info:"Error de red.",network_error_message:"Comprueba la configuración de la red.",network_retry:"Intentar de nuevo",new_caledonia:"🇳🇨 Nueva Caledonia",new_zealand:"🇳🇿 Nueva Zelanda",next:"Siguiente",nicaragua:"🇳🇮 Nicaragua",niger:"🇳🇪 Níger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"NO",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"No se han encontrado resultados. Inténtalo con otra palabra clave o personaliza las dimensiones a tu gusto.",no_search_results_found_please_try_a_different_keyword:"No se han encontrado resultados. Prueba con otra palabra clave.",norfolk_island:"🇳🇫 Isla Norfolk",north_america:"América del Norte",north_korea:"🇰🇵 Corea del Norte",north_macedonia:"🇲🇰 Macedonia del Norte",northern_mariana_islands:"🇲🇵 Islas Marianas del Norte",norway:"🇳🇴 Noruega",notice:"Aviso",oceania:"Oceanía",ok:"OK",oman:"🇴🇲 Omán",outfit:"Outfit",pakistan:"🇵🇰 Pakistán",palau:"🇵🇼 Palaos",palestinian_territories:"🇵🇸 Territorios Palestinos",panama:"🇵🇦 Panamá",papua_new_guinea:"🇵🇬 Papúa Nueva Guinea",paraguay:"🇵🇾 Paraguay",payment:"Pago",paymentFailure:"¡Error de pago!",peru:"🇵🇪 Perú",philippines:"🇵🇭 Filipinas",photoResize:"Redim. fotos",photo_size:"Tamaño de la foto",pitcairn_islands:"🇵🇳 Islas Pitcairn",poland:"🇵🇱 Polonia",portugal:"🇵🇹 Portugal",posterSize:"5x7 pulgadas",preview_link:"Visitar el sitio oficial",printSize:"Tamaño de impresión",privacy_policy:"Política de privacidad",puerto_rico:"🇵🇷 Puerto Rico",purchase:"Comprar",px:"px",qatar:"🇶🇦 Catar",reset:"Restablecer",results:"Resultados",revert:"Voltear horizontalmente",romania:"🇷🇴 Rumanía",runion:"🇷🇪 Reunión",russia:"🇷🇺 Rusia",rwanda:"🇷🇼 Ruanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Arabia Saudí",save:"Guardar",save_failed:"Error al guardar",save_success:"Guardado correctamente",search:"Buscar",search_for_a_country_or_region:"Buscar por país o región",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbia",settings:"Ajustes",seychelles:"🇸🇨 Seychelles",share:"Compartir",shootingTips:"Consejos para fotografiar",sierra_leone:"🇸🇱 Sierra Leona",singapore:"🇸🇬 Singapur",size:"Tamaño",skin_retouch:"Piel",slovakia:"🇸🇰 Eslovaquia",slovenia:"🇸🇮 Eslovenia",so_tom_prncipe:"🇸🇹 Santo Tomé y Príncipe",solomon_islands:"🇸🇧 Islas Salomón",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 Sudáfrica",south_america:"América del Sur",south_korea:"🇰🇷 Corea del Sur",south_sudan:"🇸🇸 Sudán del Sur",spain:"🇪🇸 España",specialNotes:"Notas especiales",specifications:"Especificaciones",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 San Cristóbal y Nieves",st_lucia:"🇱🇨 Santa Lucía",st_pierre_miquelon:"🇵🇲 San Pedro y Miquelón",st_vincent_grenadines:"🇻🇨 San Vicente y las Granadinas",sudan:"🇸🇩 Sudán",suriname:"🇸🇷 Surinam",svalbard_jan_mayen:"🇸🇯 Svalbard y Jan Mayen",sweden:"🇸🇪 Suecia",switzerland:"🇨🇭 Suiza",syria:"🇸🇾 Siria",taiwan:"🇹🇼 Taiwán",tajikistan:"🇹🇯 Tayikistán",tanzania:"🇹🇿 Tanzania",taskExplore:"Explorar",thailand:"🇹🇭 Tailandia",timorleste:"🇹🇱 Timor-Leste",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Total",trinidad_tobago:"🇹🇹 Trinidad y Tobago",try_again:"Intentar de nuevo",tunisia:"🇹🇳 Túnez",turkey:"🇹🇷 Turquía",turkmenistan:"🇹🇲 Turkmenistán",turks_caicos_islands:"🇹🇨 Islas Turcas y Caicos",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ucrania",unit:"Fotocopias",united_arab_emirates:"🇦🇪 Emiratos Árabes Unidos",united_kingdom:"🇬🇧 Reino Unido",united_states:"🇺🇸 Estados Unidos",update:"Actualizar",updateCancelButton:"Tal vez más tarde",updateComfirmButton:"Actualizar ahora",updateMessage:'¡Los nuevos contenidos están listos! Toca "Actualizar ahora" para cargar los recursos más recientes.',uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 Islas Vírgenes de los Estados Unidos",uzbekistan:"🇺🇿 Uzbekistán",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnam",wallis_futuna:"🇼🇫 Wallis y Futuna",whiteEdgeTips:"Proporcione una foto de retrato con un rango más amplio",width:"Ancho",width_range_should_be_within_10120:"El ancho debe estar entre {min}~{max}.",yemen:"🇾🇪 Yemen",yes:"Sí",zambia:"🇿🇲 Zambia",zimbabwe:"🇿🇼 Zimbabue",agreement:"El uso de esta función requiere que conozca y acepte las {termsOfService} y la {privacyPolicy}.",more:"Más",homeCaption:"Powered by",termsOfService:"Términos de servicio de SnapID",history:"Historial"},yo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. La création de tenues IA peut « prendre un certain temps » en raison d'un grand nombre de demandes.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. L'utilisation de tenues générées par l'IA pour les documents officiels peut augmenter le « risque de rejet de la demande ». Nous recommandons de consulter le site officiel pour connaître les exigences détaillées en matière de documents avant de poursuivre.",IDPhoto:"Photo d'identité",JPPosterSize:"89x127mm","Keep waiting":"Continuer à attendre","Server is busy.":"Le serveur est occupé.","Stop generating":"Arrêter de générer",THPosterSize:"4x6in",adjustTips:"Veuillez ajuster le visage le long de la bordure",adjust_tips:"Lorsque vous effectuez une demande de documents officiels, assurez-vous que la photo n'est pas inversée gauche-droite ou qu'elle n'est pas une image inversée.",adjust_title:"Ajuster la position",afghanistan:"🇦🇫 Afghanistan",africa:"Afrique",agree:"Accepter",agreeTOS:'En continuant, vous acceptez nos <a href="https://snapid.ai/terms-of-service">conditions de service</a> et notre <a href="https://snapid.ai/privacy-policy">politique de confidentialité</a>.',albania:"🇦🇱 Albanie",albumPermissionContent:"Assurez-vous d'avoir donné à l'application l'accès à Appareil photo et à Toutes les photos.",albumPermissionTitle:"Permission nécessaire.",algeria:"🇩🇿 Algérie",almost_there_just_1_second_to_go:"Vous y êtes presque ! 1 seconde restantes ",american_samoa:"🇦🇸 Samoa américaines",andorra:"🇦🇩 Andorre",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua-et-Barbuda",argentina:"🇦🇷 Argentine",armenia:"🇦🇲 Arménie",asia:"Asie",australia:"🇦🇺 Australie",austria:"🇦🇹 Autriche",azerbaijan:"🇦🇿 Azerbaïdjan",background:"Arrière-plan",background_updated:"Arrière-plan mis à jour",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Bahreïn",bangladesh:"🇧🇩 Bangladesh",bangs:"Frange",barbados:"🇧🇧 Barbade",belarus:"🇧🇾 Biélorussie",belgium:"🇧🇪 Belgique",belize:"🇧🇿 Belize",benin:"🇧🇯 Bénin",bermuda:"🇧🇲 Bermudes",bhutan:"🇧🇹 Bhoutan",bolivia:"🇧🇴 Bolivie",bosnia_herzegovina:"🇧🇦 Bosnie-Herzégovine",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brésil",british_virgin_islands:"🇻🇬 Îles Vierges britanniques",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgarie",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cap-Vert",cambodia:"🇰🇭 Cambodge",cameroon:"🇨🇲 Cameroun",canada:"🇨🇦 Canada",cancel:"Annuler",cancelGeneratorTips:"Êtes-vous sûr de vouloir arrêter la génération ?",central_african_republic:"🇨🇫 République centrafricaine",chad:"🇹🇩 Tchad",chile:"🇨🇱 Chili",china:"🇨🇳 Chine",christmas_island:"🇨🇽 Île Christmas",close:"Fermer",closer:"Plus près",cocos_keeling_islands:"🇨🇨 Îles Cocos (Keeling)",colombia:"🇨🇴 Colombie",colorChange:"Changement de couleur",comoros:"🇰🇲 Comores",confirm:"Confirmer",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Îles Cook",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Croatie",cte_divoire:"🇨🇮 Côte d’Ivoire",cuba:"🇨🇺 Cuba",custom:"Personnalisé",custom_size:"Format personnalisé",cyprus:"🇨🇾 Chypre",czech_republic:"🇨🇿 République tchèque",delete:"Supprimer",denmark:"🇩🇰 Danemark",digitalPhoto:"Photo numérique",digitalPhotoTips:"Photo collage pour impression",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominique",dominican_republic:"🇩🇴 République Dominicaine",ecuador:"🇪🇨 Équateur",edit_title:"Éditer",egypt:"🇪🇬 Égypte",el_salvador:"🇸🇻 Salvador",equatorial_guinea:"🇬🇶 Guinée équatoriale",eritrea:"🇪🇷 Érythrée",error1:"Le traitement de l'image a échoué.",error2:"Le visage n'a pas pu être détecté.",error3:"Les photos avec plusieurs personnes ne seront pas acceptées.",estonia:"🇪🇪 Estonie",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Éthiopie",europe:"Europe",exitFirstMenuTips:"Êtes-vous sûr de vouloir quitter sans sauvegarder ?",failed:"Échec",failed_to_generate:"Échec de la génération.",falkland_islands:"🇫🇰 Îles Malouines",faroe_islands:"🇫🇴 Îles Féroé",farther:"Plus loin",female:"Femme",fiji:"🇫🇯 Fidji",finland:"🇫🇮 Finlande",france:"🇫🇷 France",french_polynesia:"🇵🇫 Polynésie française",gabon:"🇬🇦 Gabon",gambia:"🇬🇲 Gambie",generateFailed:"Génération échouée",generate_id_photo_desc:`1. Prendre la photo sous un éclairage doux et uniforme<br/>
2. Ne pas trop retoucher ou effacer les traits du visage.`,georgia:"🇬🇪 Géorgie",germany:"🇩🇪 Allemagne",getStarted:"Commencer",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"J'ai compris",greece:"🇬🇷 Grèce",greenland:"🇬🇱 Groenland",grenada:"🇬🇩 Grenade",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernesey",guinea:"🇬🇳 Guinée",guineabissau:"🇬🇼 Guinée-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haïti",head_position:"Position de la tête",height:"Hauteur",height_range_should_be_within_10120:"La hauteur doit être comprise entre {min}~{max}.",holy_see_vatican_city:"🇻🇦 Saint-Siège (Vatican)",homeTitle:"Photo d'identité",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong SAR Chine",hungary:"🇭🇺 Hongrie",iceland:"🇮🇸 Islande",idPhotoTip1:"1. Demandez à quelqu'un de prendre votre photo à une distance de 1 à 2 m.",idPhotoTip2:"2. Choisissez un fond blanc ou gris clair.",idPhotoTip3:"3. Retirez tous vos accessoires. Vos cheveux doivent être tirés vers l'arrière.",idPhotoTip4:"4. Votre visage ne doit pas être dissimulé par des ombres, des mèches de cheveux ou des vêtements.",idPhotoTip5:"5. Prenez une expression neutre avec les yeux ouverts et la bouche fermée.",idPhotoTip6:"6. Vérifiez que la photo ne comporte pas de marques, de reflets de lunettes ou d'yeux rouges.",idTaskEmpty:"Il n'y a rien à voir ici ! <br />Aller dans la rubrique Créer des photos d'identité.",inch:"pouce",india:"🇮🇳 Inde",indonesia:"🇮🇩 Indonésie",iran:"🇮🇷 Iran",iraq:"🇮🇶 Irak",ireland:"🇮🇪 Irlande",isle_of_man:"🇮🇲 Île de Man",israel:"🇮🇱 Israël",italy:"🇮🇹 Italie",jamaica:"🇯🇲 Jamaïque",japan:"🇯🇵 Japon",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordanie",kazakhstan:"🇰🇿 Kazakhstan",kenya:"🇰🇪 Kenya",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Koweït",kyrgyzstan:"🇰🇬 Kirghizistan",laos:"🇱🇦 Laos",later:"Plus tard",latvia:"🇱🇻 Lettonie",lebanon:"🇱🇧 Liban",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Libéria",libya:"🇱🇾 Libye",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lituanie",loading:"Chargement",loadingText1:"Votre demande est en cours de traitement",loadingText2:"Veuillez garder l'application ouverte pour une exécution rapide.",loadingText3:"Vous y êtes presque ! {time} secondes restantes ",loadingText4:"Tenue IA en cours de confection",loadingText5:"Plus rapide qu'une visite au studio photo !",loadingText6:"Encore un peu de patience s'il vous plaît.",loadingText7:"Les bons résultats valent la peine d'être attendus !",loadingText8:"Finition de la tenue générée par l'IA presque terminée !",loadingText9:"Nous vous remercions de votre patience pendant que nous traitons votre demande",loginSaveTip1:"Votre production sera sauvegardée pendant <span>7 jours</span> ; prenez note de la date de création et sauvegardez-le rapidement.",luxembourg:"🇱🇺 Luxembourg",macau_sar_china:"🇲🇴 Macao SAR Chine",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaisie",maldives:"🇲🇻 Maldives",male:"Homme",mali:"🇲🇱 Mali",malta:"🇲🇹 Malte",marshall_islands:"🇲🇭 Îles Marshall",mauritania:"🇲🇷 Mauritanie",mauritius:"🇲🇺 Maurice",mayotte:"🇾🇹 Mayotte",medium:"Moyen",mexico:"🇲🇽 Mexique",micronesia:"🇫🇲 Micronésie",mm:"mm",moldova:"🇲🇩 Moldavie",monaco:"🇲🇨 Monaco",mongolia:"🇲🇳 Mongolie",montenegro:"🇲🇪 Monténégro",montserrat:"🇲🇸 Montserrat",moreColors:"Plus de couleurs",mozambique:"🇲🇿 Mozambique",myanmar_burma:"🇲🇲 Myanmar (Birmanie)",namibia:"🇳🇦 Namibie",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Népal",netherlands:"🇳🇱 Pays-Bas",network_error:"Réseau déconnecté",network_error_info:"Erreur de réseau.",network_error_message:"Veuillez vérifier les paramètres de votre réseau.",network_retry:"Réessayer",new_caledonia:"🇳🇨 Nouvelle-Calédonie",new_zealand:"🇳🇿 Nouvelle-Zélande",next:"Suivant",nicaragua:"🇳🇮 Nicaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigéria",niue:"🇳🇺 Niue",no:"NON",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Aucun résultat trouvé. Veuillez essayer un autre mot-clé ou personnaliser votre format.",no_search_results_found_please_try_a_different_keyword:"Aucun résultat de recherche n'a été trouvé. Veuillez essayer un autre mot-clé.",norfolk_island:"🇳🇫 Île Norfolk",north_america:"Amérique du Nord",north_korea:"🇰🇵 Corée du Nord",north_macedonia:"🇲🇰 Macédoine du Nord",northern_mariana_islands:"🇲🇵 Îles Mariannes du Nord",norway:"🇳🇴 Norvège",notice:"Avertissement",oceania:"Océanie",ok:"OK",oman:"🇴🇲 Oman",outfit:"Tenue",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palaos",palestinian_territories:"🇵🇸 Territoires palestiniens",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papouasie-Nouvelle-Guinée",paraguay:"🇵🇾 Paraguay",payment:"Paiement",paymentFailure:"Le paiement a échoué !",peru:"🇵🇪 Pérou",philippines:"🇵🇭 Philippines",photoResize:"Taille photo",photo_size:"Taille de la photo",pitcairn_islands:"🇵🇳 Îles Pitcairn",poland:"🇵🇱 Pologne",portugal:"🇵🇹 Portugal",posterSize:"5x7in",preview_link:"Visiter le site officiel",printSize:"Taille d'impression",privacy_policy:"Politique de confidentialité",puerto_rico:"🇵🇷 Porto Rico",purchase:"Acheter",px:"pixel",qatar:"🇶🇦 Qatar",reset:"Réinitialiser",results:"Résultats",revert:"Retournement horizontal",romania:"🇷🇴 Roumanie",runion:"🇷🇪 Réunion",russia:"🇷🇺 Russie",rwanda:"🇷🇼 Rwanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 Saint-Marin",saudi_arabia:"🇸🇦 Arabie saoudite",save:"Économiser",save_failed:"Échec de la sauvegarde",save_success:"Sauvegardé avec succès",search:"Rechercher",search_for_a_country_or_region:"Recherche d'un pays ou d'une région",senegal:"🇸🇳 Sénégal",serbia:"🇷🇸 Serbie",settings:"Paramètres",seychelles:"🇸🇨 Seychelles",share:"Partage",shootingTips:"Conseils pour la prise de vue",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapour",size:"Taille ",skin_retouch:"Peau",slovakia:"🇸🇰 Slovaquie",slovenia:"🇸🇮 Slovénie",so_tom_prncipe:"🇸🇹 Sao Tomé-et-Principe",solomon_islands:"🇸🇧 Îles Salomon",somalia:"🇸🇴 Somalie",south_africa:"🇿🇦 Afrique du Sud",south_america:"Amérique du Sud",south_korea:"🇰🇷 Corée du Sud",south_sudan:"🇸🇸 Soudan du Sud",spain:"🇪🇸 Espagne",specialNotes:"Remarques particulières",specifications:"Spécifications",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 Saint-Christophe-et-Niévès",st_lucia:"🇱🇨 Sainte-Lucie",st_pierre_miquelon:"🇵🇲 Saint-Pierre-et-Miquelon",st_vincent_grenadines:"🇻🇨 Saint-Vincent-et-les Grenadines",sudan:"🇸🇩 Soudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard et Jan Mayen",sweden:"🇸🇪 Suède",switzerland:"🇨🇭 Suisse",syria:"🇸🇾 Syrie",taiwan:"🇹🇼 Taïwan",tajikistan:"🇹🇯 Tadjikistan",tanzania:"🇹🇿 Tanzanie",taskExplore:"Explorer",thailand:"🇹🇭 Thaïlande",timorleste:"🇹🇱 Timor oriental",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Total ",trinidad_tobago:"🇹🇹 Trinité-et-Tobago",try_again:"Réessayer",tunisia:"🇹🇳 Tunisie",turkey:"🇹🇷 Turquie",turkmenistan:"🇹🇲 Turkménistan",turks_caicos_islands:"🇹🇨 Îles Turques-et-Caïques",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Ouganda",ukraine:"🇺🇦 Ukraine",unit:"Photocopies",united_arab_emirates:"🇦🇪 Émirats arabes unis",united_kingdom:"🇬🇧 Royaume-Uni",united_states:"🇺🇸 États-Unis",update:"Mise à jour",updateCancelButton:"Peut-être plus tard",updateComfirmButton:"Mettre à jour maintenant",updateMessage:"Le nouveau contenu est prêt ! Appuyez sur « Mettre à jour maintenant » pour charger les dernières ressources.",uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 Îles Vierges des États-Unis",uzbekistan:"🇺🇿 Ouzbékistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Viêt Nam",wallis_futuna:"🇼🇫 Wallis-et-Futuna",whiteEdgeTips:"Veuillez fournir une photo de portrait avec une plus grande étendue",width:"Largeur",width_range_should_be_within_10120:"La largeur doit être comprise entre {min}~{max}.",yemen:"🇾🇪 Yémen",yes:"Oui",zambia:"🇿🇲 Zambie",zimbabwe:"🇿🇼 Zimbabwe",agreement:"Pour utiliser cette fonction, vous devez connaître et accepter les {termsOfService} et {privacyPolicy}.",more:"Plus d'informations",homeCaption:"Powered by",termsOfService:"Conditions d'utilisation de SnapID",history:"L'histoire"},ko={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. Membuat Pakaian AI dapat <span>memakan waktu cukup lama</span> karena sedang banyak permintaan.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. Menggunakan pakaian buatan AI untuk dokumen resmi dapat meningkatkan <span>risiko penolakan pengajuan</span>. Silakan cek situs web resmi untuk persyaratan dokumen lebih lanjut sebelum melanjutkan.",IDPhoto:"Foto Identitas",JPPosterSize:"89x127mm","Keep waiting":"Harap tunggu","Server is busy.":"Server sedang sibuk.","Stop generating":"Berhenti memproses",THPosterSize:"4x6 in",adjustTips:"Harap atur wajah di sepanjang batas",adjust_tips:"Saat mengajukan dokumen resmi, pastikan fotonya tidak terbalik sisi kiri-kanan atau dicerminkan.",adjust_title:"Atur Posisi",afghanistan:"🇦🇫 Afganistan",africa:"Afrika",agree:"Setuju",agreeTOS:'Dengan melanjutkan, Anda menyetujui <a href="https://snapid.ai/terms-of-service">Ketentuan Layanan</a> dan <a href="https://snapid.ai/privacy-policy">Kebijakan Privasi</a> kami.',albania:"🇦🇱 Albania",albumPermissionContent:"Pastikan Anda mengizinkan aplikasi mengakses Kamera dan Semua Foto.",albumPermissionTitle:"Diperlukan izin.",algeria:"🇩🇿 Aljazair",almost_there_just_1_second_to_go:"Hampir selesai! Tinggal 1 detik lagi",american_samoa:"🇦🇸 Samoa Amerika",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua dan Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armenia",asia:"Asia",australia:"🇦🇺 Australia",austria:"🇦🇹 Austria",azerbaijan:"🇦🇿 Azerbaijan",background:"Latar",background_updated:"Latar Belakang Diperbarui",bahamas:"🇧🇸 Bahama",bahrain:"🇧🇭 Bahrain",bangladesh:"🇧🇩 Bangladesh",bangs:"Poni",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Belarus",belgium:"🇧🇪 Belgia",belize:"🇧🇿 Belize",benin:"🇧🇯 Benin",bermuda:"🇧🇲 Bermuda",bhutan:"🇧🇹 Bhutan",bolivia:"🇧🇴 Bolivia",bosnia_herzegovina:"🇧🇦 Bosnia & Herzegovina",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brasil",british_virgin_islands:"🇻🇬 Kepulauan Virgin Britania",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgaria",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Kamboja",cameroon:"🇨🇲 Kamerun",canada:"🇨🇦 Kanada",cancel:"Batal",cancelGeneratorTips:"Yakin ingin berhenti membuat?",central_african_republic:"🇨🇫 Republik Afrika Tengah",chad:"🇹🇩 Chad",chile:"🇨🇱 Chile",china:"🇨🇳 Tiongkok",christmas_island:"🇨🇽 Pulau Natal",close:"Tutup",closer:"Lebih Dekat",cocos_keeling_islands:"🇨🇨 Kepulauan Cocos (Keeling)",colombia:"🇨🇴 Kolombia",colorChange:"Ganti Warna",comoros:"🇰🇲 Komoro",confirm:"Konfirmasi",congo_brazzaville:"🇨🇬 Kongo - Brazzaville",congo_kinshasa:"🇨🇩 Kongo - Kinshasa",cook_islands:"🇨🇰 Kepulauan Cook",costa_rica:"🇨🇷 Kosta Rika",croatia:"🇭🇷 Kroasia",cte_divoire:"🇨🇮 Pantai Gading",cuba:"🇨🇺 Kuba",custom:"Kustom",custom_size:"Ukuran Kustom",cyprus:"🇨🇾 Siprus",czech_republic:"🇨🇿 Republik Ceko",delete:"Hapus",denmark:"🇩🇰 Denmark",digitalPhoto:"Foto Digital",digitalPhotoTips:"Foto kolase untuk dicetak",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominika",dominican_republic:"🇩🇴 Republik Dominika",ecuador:"🇪🇨 Ekuador",edit_title:"Edit",egypt:"🇪🇬 Mesir",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Guinea Khatulistiwa",eritrea:"🇪🇷 Eritrea",error1:"Pemrosesan gambar gagal.",error2:"Wajah tidak dapat dideteksi.",error3:"Tidak boleh foto dengan lebih dari satu orang.",estonia:"🇪🇪 Estonia",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Ethiopia",europe:"Eropa",exitFirstMenuTips:"Yakin ingin keluar tanpa menyimpan?",failed:"Gagal",failed_to_generate:"Gagal membuat",falkland_islands:"🇫🇰 Kepulauan Falkland",faroe_islands:"🇫🇴 Kepulauan Faroe",farther:"Lebih Jauh",female:"Wanita",fiji:"🇫🇯 Fiji",finland:"🇫🇮 Finlandia",france:"🇫🇷 Prancis",french_polynesia:"🇵🇫 Polinesia Prancis",gabon:"🇬🇦 Gabon",gambia:"🇬🇲 Gambia",generateFailed:"Pemrosesan Gagal",generate_id_photo_desc:`1. Foto dengan pencahayaan yang merata dan lembut<br/>
2. Modifikasi tidak berlebihan atau tidak menghapus fitur wajah asli.`,georgia:"🇬🇪 Georgia",germany:"🇩🇪 Jerman",getStarted:"Mulai",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Mengerti",greece:"🇬🇷 Yunani",greenland:"🇬🇱 Greenland",grenada:"🇬🇩 Grenada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haiti",head_position:"Posisi Kepala",height:"Tinggi",height_range_should_be_within_10120:"Rentang tinggi harus antara {min}~{max}.",holy_see_vatican_city:"🇻🇦 Tahta Suci (Vatikan)",homeTitle:"Foto Paspor",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 SAR Hong Kong Tiongkok",hungary:"🇭🇺 Hongaria",iceland:"🇮🇸 Islandia",idPhotoTip1:"1. Mintalah orang lain mengambil foto Anda dari jarak 1-2 m.",idPhotoTip2:"2. Gunakan latar belakang putih polos atau abu-abu muda.",idPhotoTip3:"3. Lepaskan semua aksesori. Tarik rambut ke belakang agar tidak menghalangi.",idPhotoTip4:"4. Tidak ada bayangan, rambut, atau pakaian yang menutupi wajah.",idPhotoTip5:"5. Ekspresi netral dengan mata terbuka dan mulut tertutup.",idPhotoTip6:"6. Tidak ada silau, pantulan dari kacamata, atau 'mata merah'.",idTaskEmpty:"Kosong! <br />Silakan membuat Foto.",inch:"inci",india:"🇮🇳 India",indonesia:"🇮🇩 Indonesia",iran:"🇮🇷 Iran",iraq:"🇮🇶 Irak",ireland:"🇮🇪 Irlandia",isle_of_man:"🇮🇲 Pulau Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Italia",jamaica:"🇯🇲 Jamaika",japan:"🇯🇵 Jepang",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Yordania",kazakhstan:"🇰🇿 Kazakhstan",kenya:"🇰🇪 Kenya",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kirgizstan",laos:"🇱🇦 Laos",later:"Lain kali",latvia:"🇱🇻 Latvia",lebanon:"🇱🇧 Lebanon",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libya",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lituania",loading:"Memuat",loadingText1:"Permintaan Anda sedang diproses",loadingText2:"Biarkan aplikasi tetap terbuka agar lebih cepat diselesaikan",loadingText3:"Hampir selesai! Tinggal {time} detik lagi",loadingText4:"Membuat pakaian AI untuk Anda",loadingText5:"Lebih cepat daripada datang langsung ke studio foto!",loadingText6:"Sebentar lagi jadi, harap tunggu",loadingText7:"Hasil yang bagus layak untuk ditunggu!",loadingText8:"Menyemchanakan pakaian yang dibuat AI, hampir selesai!",loadingText9:"Terima kasih sudah menunggu saat kami memproses permintaan Anda",loginSaveTip1:"Catatan yang dibuat akan disimpan selama <span>7 hari</span>; harap catat tanggal pembuatan dan segera simpan.",luxembourg:"🇱🇺 Luksemburg",macau_sar_china:"🇲🇴 SAR Makau Tiongkok",madagascar:"🇲🇬 Madagaskar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaysia",maldives:"🇲🇻 Maladewa",male:"Pria",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Kepulauan Marshall",mauritania:"🇲🇷 Mauritania",mauritius:"🇲🇺 Mauritius",mayotte:"🇾🇹 Mayotte",medium:"Sedang",mexico:"🇲🇽 Meksiko",micronesia:"🇫🇲 Mikronesia",mm:"mm",moldova:"🇲🇩 Moldova",monaco:"🇲🇨 Monako",mongolia:"🇲🇳 Mongolia",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Warna Lain",mozambique:"🇲🇿 Mozambik",myanmar_burma:"🇲🇲 Myanmar (Burma)",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Belanda",network_error:"Koneksi Jaringan Terputus",network_error_info:"Kesalahan jaringan.",network_error_message:"Harap periksa pengaturan jaringan Anda.",network_retry:"Coba lagi",new_caledonia:"🇳🇨 Kaledonia Baru",new_zealand:"🇳🇿 Selandia Baru",next:"Berikutnya",nicaragua:"🇳🇮 Nikaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"TIDAK",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Tidak ada hasil. Silakan coba kata kunci lainnya, atau buat ukuran sendiri.",no_search_results_found_please_try_a_different_keyword:"Tidak ada hasil pencarian. Silakan coba kata kunci lainnya.",norfolk_island:"🇳🇫 Pulau Norfolk",north_america:"Amerika Utara",north_korea:"🇰🇵 Korea Utara",north_macedonia:"🇲🇰 Makedonia Utara",northern_mariana_islands:"🇲🇵 Kepulauan Mariana Utara",norway:"🇳🇴 Norwegia",notice:"Pemberitahuan",oceania:"Oseania",ok:"OK",oman:"🇴🇲 Oman",outfit:"Pakaian",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Wilayah Palestina",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papua Nugini",paraguay:"🇵🇾 Paraguay",payment:"Pembayaran",paymentFailure:"Pembayaran gagal!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Filipina",photoResize:"Ubah Ukuran",photo_size:"Ukuran Foto",pitcairn_islands:"🇵🇳 Kepulauan Pitcairn",poland:"🇵🇱 Polandia",portugal:"🇵🇹 Portugal",posterSize:"5x7 in",preview_link:"Kunjungi situs web resmi",printSize:"Ukuran Cetak",privacy_policy:"Kebijakan Privasi",puerto_rico:"🇵🇷 Puerto Riko",purchase:"Beli",px:"px",qatar:"🇶🇦 Qatar",reset:"Atur ulang",results:"Hasil",revert:"Balik Horizontal",romania:"🇷🇴 Rumania",runion:"🇷🇪 Réunion",russia:"🇷🇺 Rusia",rwanda:"🇷🇼 Rwanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Arab Saudi",save:"Simpan",save_failed:"Gagal menyimpan",save_success:"Berhasil disimpan",search:"Cari",search_for_a_country_or_region:"Cari negara atau wilayah",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbia",settings:"Pengaturan",seychelles:"🇸🇨 Seychelles",share:"Bagikan",shootingTips:"Tips Pemotretan",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapura",size:"Ukuran",skin_retouch:"Kulit",slovakia:"🇸🇰 Slovakia",slovenia:"🇸🇮 Slovenia",so_tom_prncipe:"🇸🇹 São Tomé & Príncipe",solomon_islands:"🇸🇧 Kepulauan Solomon",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 Afrika Selatan",south_america:"Amerika Selatan",south_korea:"🇰🇷 Korea Selatan",south_sudan:"🇸🇸 Sudan Selatan",spain:"🇪🇸 Spanyol",specialNotes:"Catatan khusus",specifications:"Spesifikasi",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 Saint Kitts dan Nevis",st_lucia:"🇱🇨 Saint Lucia",st_pierre_miquelon:"🇵🇲 Saint Pierre dan Miquelon",st_vincent_grenadines:"🇻🇨 Saint Vincent dan Grenadines",sudan:"🇸🇩 Sudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard & Jan Mayen",sweden:"🇸🇪 Swedia",switzerland:"🇨🇭 Swiss",syria:"🇸🇾 Suriah",taiwan:"🇹🇼 Taiwan",tajikistan:"🇹🇯 Tajikistan",tanzania:"🇹🇿 Tanzania",taskExplore:"Jelajahi",thailand:"🇹🇭 Thailand",timorleste:"🇹🇱 Timor-Leste",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Total",trinidad_tobago:"🇹🇹 Trinidad & Tobago",try_again:"Coba Lagi",tunisia:"🇹🇳 Tunisia",turkey:"🇹🇷 Turki",turkmenistan:"🇹🇲 Turkmenistan",turks_caicos_islands:"🇹🇨 Kepulauan Turks dan Caicos",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ukraina",unit:"Fotokopi",united_arab_emirates:"🇦🇪 Uni Emirat Arab",united_kingdom:"🇬🇧 Britania Raya",united_states:"🇺🇸 Amerika Serikat",update:"Perbarui",updateCancelButton:"Mungkin Nanti",updateComfirmButton:"Perbarui Sekarang",updateMessage:'Konten baru sudah siap! Ketuk "Perbarui Sekarang" untuk memuat sumber daya terbaru.',uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 Kepulauan Virgin AS",uzbekistan:"🇺🇿 Uzbekistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnam",wallis_futuna:"🇼🇫 Wallis & Futuna",whiteEdgeTips:"Mohon sediakan foto potret dengan cakupan yang lebih luas",width:"Lebar",width_range_should_be_within_10120:"Rentang lebar harus antara {min}~{max}.",yemen:"🇾🇪 Yaman",yes:"Ya",zambia:"🇿🇲 Zambia",zimbabwe:"🇿🇼 Zimbabwe",agreement:"Untuk menggunakan fungsi ini, Anda harus membaca dan menyetujui {termsOfService} dan {privacyPolicy}.",more:"Lainnya",homeCaption:"Powered by",termsOfService:"Ketentuan Layanan SnapID",history:"Riwayat"},vo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. リクエストが大量にあるため、AI 衣装の生成には<span>時間がかかる</span>場合があります。",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. AI が生成した衣装を公式文書に使用すると、<span>提出拒否のリスク</span>が高まる可能性があります。手続きを進める前に、詳細な書類要件について公式 Web サイトを確認することをお勧めします。",IDPhoto:"証明写真",JPPosterSize:"Lサイズ（89x127mm）","Keep waiting":"待機","Server is busy.":"サーバーが混雑しています。","Stop generating":"生成を停止",THPosterSize:"4x6インチ",adjustTips:"顔を枠線に沿って調整してください",adjust_tips:"公文書の申請時には、写真の左右が反転していないこと、鏡像でないことをご確認ください。",adjust_title:"位置調整",afghanistan:"🇦🇫 アフガニスタン",africa:"アフリカ",agree:"同意する",agreeTOS:'続行することで、<a href="https://snapid.ai/terms-of-service">利用規約</a>および<a href="https://snapid.ai/privacy-policy">プライバシーポリシー</a>に同意するものとします。',albania:"🇦🇱 アルバニア",albumPermissionContent:"アプリにカメラとすべての写真へのアクセスを許可してください。",albumPermissionTitle:"アクセス許可が必要です。 ",algeria:"🇩🇿 アルジェリア",almost_there_just_1_second_to_go:"もうすぐです！あと1秒",american_samoa:"🇦🇸 アメリカ領サモア",andorra:"🇦🇩 アンドラ",angola:"🇦🇴 アンゴラ",anguilla:"🇦🇮 アンギラ",antigua_barbuda:"🇦🇬 アンティグア・バーブーダ",argentina:"🇦🇷 アルゼンチン",armenia:"🇦🇲 アルメニア",asia:"アジア",australia:"🇦🇺 オーストラリア",austria:"🇦🇹 オーストリア",azerbaijan:"🇦🇿 アゼルバイジャン",background:"背景",background_updated:"背景が更新されました",bahamas:"🇧🇸 バハマ",bahrain:"🇧🇭 バーレーン",bangladesh:"🇧🇩 バングラデシュ",bangs:"前髪",barbados:"🇧🇧 バルバドス",belarus:"🇧🇾 ベラルーシ",belgium:"🇧🇪 ベルギー",belize:"🇧🇿 ベリーズ",benin:"🇧🇯 ベナン",bermuda:"🇧🇲 バミューダ",bhutan:"🇧🇹 ブータン",bolivia:"🇧🇴 ボリビア",bosnia_herzegovina:"🇧🇦 ボスニア・ヘルツェゴビナ",botswana:"🇧🇼 ボツワナ",brazil:"🇧🇷 ブラジル",british_virgin_islands:"🇻🇬 英領ヴァージン諸島",brunei:"🇧🇳 ブルネイ",bulgaria:"🇧🇬 ブルガリア",burkina_faso:"🇧🇫 ブルキナファソ",burundi:"🇧🇮 ブルンジ",cabo_verde:"🇨🇻 カーボベルデ",cambodia:"🇰🇭 カンボジア",cameroon:"🇨🇲 カメルーン",canada:"🇨🇦 カナダ",cancel:"キャンセル",cancelGeneratorTips:"生成を停止してもよろしいですか?",central_african_republic:"🇨🇫 中央アフリカ共和国",chad:"🇹🇩 チャド",chile:"🇨🇱 チリ",china:"🇨🇳 中国",christmas_island:"🇨🇽 クリスマス島",close:"閉じる",closer:"より近く",cocos_keeling_islands:"🇨🇨 ココス（キーリング）諸島",colombia:"🇨🇴 コロンビア",colorChange:"色変更",comoros:"🇰🇲 コモロ",confirm:"確定",congo_brazzaville:"🇨🇬 コンゴ共和国",congo_kinshasa:"🇨🇩 コンゴ民主共和国",cook_islands:"🇨🇰 クック諸島",costa_rica:"🇨🇷 コスタリカ",croatia:"🇭🇷 クロアチア",cte_divoire:"🇨🇮 コートジボワール",cuba:"🇨🇺 キューバ",custom:"カスタム",custom_size:"カスタムサイズ",cyprus:"🇨🇾 キプロス",czech_republic:"🇨🇿 チェコ共和国",delete:"削除",denmark:"🇩🇰 デンマーク",digitalPhoto:"デジタル写真",digitalPhotoTips:"プリント用イメージ",djibouti:"🇩🇯 ジブチ",dominica:"🇩🇲 ドミニカ",dominican_republic:"🇩🇴 ドミニカ共和国",ecuador:"🇪🇨 エクアドル",edit_title:"編集",egypt:"🇪🇬 エジプト",el_salvador:"🇸🇻 エルサルバドル",equatorial_guinea:"🇬🇶 赤道ギニア",eritrea:"🇪🇷 エリトリア",error1:"画像処理に失敗しました。",error2:"顔が検出できませんでした。",error3:"複数名が写っている写真は不可となります。",estonia:"🇪🇪 エストニア",eswatini:"🇸🇿 エスワティニ",ethiopia:"🇪🇹 エチオピア",europe:"ヨーロッパ",exitFirstMenuTips:"保存せずに終了してもよろしいですか?",failed:"失敗",failed_to_generate:"生成に失敗しました。",falkland_islands:"🇫🇰 フォークランド諸島",faroe_islands:"🇫🇴 フェロー諸島",farther:"より遠く",female:"女性",fiji:"🇫🇯 フィジー",finland:"🇫🇮 フィンランド",france:"🇫🇷 フランス",french_polynesia:"🇵🇫 フランス領ポリネシア",gabon:"🇬🇦 ガボン",gambia:"🇬🇲 ガンビア",generateFailed:"生成失敗",generate_id_photo_desc:`1. 均一で柔らかな照明の下で撮影<br/>
2. 過剰なレタッチや顔の特徴の消去はしない`,georgia:"🇬🇪 ジョージア",germany:"🇩🇪 ドイツ",getStarted:"始めましょう",ghana:"🇬🇭 ガーナ",gibraltar:"🇬🇮 ジブラルタル",gotIt:"了解",greece:"🇬🇷 ギリシャ",greenland:"🇬🇱 グリーンランド",grenada:"🇬🇩 グレナダ",guam:"🇬🇺 グアム",guatemala:"🇬🇹 グアテマラ",guernsey:"🇬🇬 ガーンジー",guinea:"🇬🇳 ギニア",guineabissau:"🇬🇼 ギニアビサウ",guyana:"🇬🇾 ガイアナ",haiti:"🇭🇹 ハイチ",head_position:"頭の位置",height:"高さ",height_range_should_be_within_10120:"高さの範囲は {min} ～ {max} 以内にしてください。",holy_see_vatican_city:"🇻🇦 バチカン市国",homeTitle:"証明写真",honduras:"🇭🇳 ホンジュラス",hong_kong_sar_china:"🇭🇰 香港",hungary:"🇭🇺 ハンガリー",iceland:"🇮🇸 アイスランド",idPhotoTip1:"1. 他の人に 1 ～ 2 メートル離れた場所から写真を撮ってもらいます。",idPhotoTip2:"2. 無地の白またはライトグレーの背景を見つけます。",idPhotoTip3:"3. すべてのアクセサリーを取り外します。髪が後ろに引っ張られていることを確認してください。",idPhotoTip4:"4. 顔が前髪や衣服、影で隠れないようにします。",idPhotoTip5:"5. 目を開け口を閉じて自然な表情をします。",idPhotoTip6:"6. 跡や眼鏡の反射、赤目がないことを確認します。",idTaskEmpty:"ここには何もありません！ <br/>証明写真を作成しましょう。",inch:"inch",india:"🇮🇳 インド",indonesia:"🇮🇩 インドネシア",iran:"🇮🇷 イラン",iraq:"🇮🇶 イラク",ireland:"🇮🇪 アイルランド",isle_of_man:"🇮🇲 マン島",israel:"🇮🇱 イスラエル",italy:"🇮🇹 イタリア",jamaica:"🇯🇲 ジャマイカ",japan:"🇯🇵 日本",jersey:"🇯🇪 ジャージー",jordan:"🇯🇴 ヨルダン",kazakhstan:"🇰🇿 カザフスタン",kenya:"🇰🇪 ケニア",kiribati:"🇰🇮 キリバス",kuwait:"🇰🇼 クウェート",kyrgyzstan:"🇰🇬 キルギス",laos:"🇱🇦 ラオス",later:"後で",latvia:"🇱🇻 ラトビア",lebanon:"🇱🇧 レバノン",lesotho:"🇱🇸 レソト",liberia:"🇱🇷 ライベリア",libya:"🇱🇾 リビア",liechtenstein:"🇱🇮 リヒテンシュタイン",lithuania:"🇱🇹 リトアニア",loading:"読み込み中",loadingText1:"あなたのリクエストは準備中です",loadingText2:"迅速に完了するためにアプリを開いたままにしてください",loadingText3:"もうすぐです！あと{time}秒",loadingText4:"AI の衣装を縫い合わせる",loadingText5:"写真スタジオに行くより早い！",loadingText6:"もう少しお待ちください。ご理解のほどよろしくお願いいたします",loadingText7:"素晴らしい結果は待つ価値があります!",loadingText8:"AI が生成した衣装の磨き上げ完了まで後少しです！",loadingText9:"リクエストを完了させていただきますので、しばらくお待ちください",loginSaveTip1:"制作記録は<span>7日間</span>保存されます。生成日を確認し、すぐに保存してください。",luxembourg:"🇱🇺 ルクセンブルク",macau_sar_china:"🇲🇴 マカオ",madagascar:"🇲🇬 マダガスカル",malawi:"🇲🇼 マラウイ",malaysia:"🇲🇾 マレーシア",maldives:"🇲🇻 モルディブ",male:"男性",mali:"🇲🇱 マリ",malta:"🇲🇹 マルタ",marshall_islands:"🇲🇭 マーシャル諸島",mauritania:"🇲🇷 モーリタニア",mauritius:"🇲🇺 モーリシャス",mayotte:"🇾🇹 マヨット",medium:"ミディアム",mexico:"🇲🇽 メキシコ",micronesia:"🇫🇲 ミクロネシア連邦",mm:"mm",moldova:"🇲🇩 モルドバ",monaco:"🇲🇨 モナコ",mongolia:"🇲🇳 モンゴル",montenegro:"🇲🇪 モンテネグロ",montserrat:"🇲🇸 モントセラト",moreColors:"その他の色",mozambique:"🇲🇿 モザンビーク",myanmar_burma:"🇲🇲 ミャンマー",namibia:"🇳🇦 ナミビア",nauru:"🇳🇷 ナウル",nepal:"🇳🇵 ネパール",netherlands:"🇳🇱 オランダ",network_error:"ネットワークが切断されました",network_error_info:"ネットワーク接続に異常があります。",network_error_message:"ネットワークの接続を確認してください。",network_retry:"再試行",new_caledonia:"🇳🇨 ニューカレドニア",new_zealand:"🇳🇿 ニュージーランド",next:"次へ",nicaragua:"🇳🇮 ニカラグア",niger:"🇳🇪 ニジェール",nigeria:"🇳🇬 ナイジェリア",niue:"🇳🇺 ニウエ",no:"いいえ",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"結果が見つかりません。別のキーワードを試すか、独自のサイズをカスタマイズしてください。",no_search_results_found_please_try_a_different_keyword:"検索結果が見つかりませんでした。別のキーワードを試してください。",norfolk_island:"🇳🇫 ノーフォーク島",north_america:"北アメリカ",north_korea:"🇰🇵 北朝鮮",north_macedonia:"🇲🇰 北マケドニア",northern_mariana_islands:"🇲🇵 北マリアナ諸島",norway:"🇳🇴 ノルウェー",notice:"通知",oceania:"Oceania (オセアニア)",ok:"確定",oman:"🇴🇲 オマーン",outfit:"服装",pakistan:"🇵🇰 パキスタン",palau:"🇵🇼 パラオ",palestinian_territories:"🇵🇸 パレスチナ",panama:"🇵🇦 パナマ",papua_new_guinea:"🇵🇬 パプアニューギニア",paraguay:"🇵🇾 パラグアイ",payment:"支払い",paymentFailure:"お支払いに失敗しました！",peru:"🇵🇪 ペルー",philippines:"🇵🇭 フィリピン",photoResize:"サイズ変更",photo_size:"写真のサイズ",pitcairn_islands:"🇵🇳 ピトケアン諸島",poland:"🇵🇱 ポーランド",portugal:"🇵🇹 ポルトガル",posterSize:"5x7in",preview_link:"公式サイトへ",printSize:"プリントサイズ",privacy_policy:"プライバシーポリシー",puerto_rico:"🇵🇷 プエルトリコ",purchase:"購入",px:"px",qatar:"🇶🇦 カタール",reset:"リセット",results:"結果",revert:"左右反転",romania:"🇷🇴 ルーマニア",runion:"🇷🇪 レユニオン",russia:"🇷🇺 ロシア",rwanda:"🇷🇼 ルワンダ",samoa:"🇼🇸 サモア",san_marino:"🇸🇲 サンマリノ",saudi_arabia:"🇸🇦 サウジアラビア",save:"保存",save_failed:"保存に失敗しました",save_success:"保存しました",search:"検索",search_for_a_country_or_region:"国または地域を検索する",senegal:"🇸🇳 セネガル",serbia:"🇷🇸 セルビア",settings:"設定",seychelles:"🇸🇨 セーシェル",share:"共有",shootingTips:"撮影のヒント",sierra_leone:"🇸🇱 シエラレオネ",singapore:"🇸🇬 シンガポール",size:"サイズ",skin_retouch:"肌補正",slovakia:"🇸🇰 スロバキア",slovenia:"🇸🇮 スロベニア",so_tom_prncipe:"🇸🇹 サントメ・プリンシペ",solomon_islands:"🇸🇧 ソロモン諸島",somalia:"🇸🇴 ソマリア",south_africa:"🇿🇦 南アフリカ",south_america:"南アメリカ",south_korea:"🇰🇷 韓国",south_sudan:"🇸🇸 南スーダン",spain:"🇪🇸 スペイン",specialNotes:"特記事項",specifications:"仕様",sri_lanka:"🇱🇰 スリランカ",st_kitts_nevis:"🇰🇳 セントクリストファー・ネイビス",st_lucia:"🇱🇨 セントルシア",st_pierre_miquelon:"🇵🇲 サンピエール島・ミクロン島",st_vincent_grenadines:"🇻🇨 セントビンセント及びグレナディーン諸島",sudan:"🇸🇩 スーダン",suriname:"🇸🇷 スリナム",svalbard_jan_mayen:"🇸🇯 スバールバル諸島およびヤンマイエン島",sweden:"🇸🇪 スウェーデン",switzerland:"🇨🇭 スイス",syria:"🇸🇾 シリア",taiwan:"🇹🇼 台湾",tajikistan:"🇹🇯 タジキスタン",tanzania:"🇹🇿 タンザニア",taskExplore:"試してみる",thailand:"🇹🇭 タイ",timorleste:"🇹🇱 東ティモール",togo:"🇹🇬 トーゴ",tokelau:"🇹🇰 トケラウ",tonga:"🇹🇴 トンガ",total:"合計",trinidad_tobago:"🇹🇹 トリニダード・トバゴ",try_again:"やり直す",tunisia:"🇹🇳 チュニジア",turkey:"🇹🇷 トルコ",turkmenistan:"🇹🇲 トルクメニスタン",turks_caicos_islands:"🇹🇨 タークス・カイコス諸島",tuvalu:"🇹🇻 ツバル",uganda:"🇺🇬 ウガンダ",ukraine:"🇺🇦 ウクライナ",unit:"枚/1シート",united_arab_emirates:"🇦🇪 アラブ首長国連邦",united_kingdom:"🇬🇧 イギリス",united_states:"🇺🇸 アメリカ合衆国",update:"更新",updateCancelButton:"後で更新",updateComfirmButton:"アップデート",updateMessage:"新しいコンテンツが追加されました！最新のリソースをロードするには、「今すぐ更新」をタップしてください。",uruguay:"🇺🇾 ウルグアイ",us_virgin_islands:"🇻🇮 米領ヴァージン諸島",uzbekistan:"🇺🇿 ウズベキスタン",vanuatu:"🇻🇺 バヌアツ",venezuela:"🇻🇪 ベネズエラ",vietnam:"🇻🇳 ベトナム",wallis_futuna:"🇼🇫 ウォリス・フツナ諸島",whiteEdgeTips:"より広範囲の人物写真を提供してください",width:"幅",width_range_should_be_within_10120:"幅の範囲は {min} ～ {max} 以内にしてください。",yemen:"🇾🇪 イエメン",yes:"はい",zambia:"🇿🇲 ザンビア",zimbabwe:"🇿🇼 ジンバブエ",agreement:"この機能を使うには、 {termsOfService}と {privacyPolicy}に同意する必要があります。",more:"その他",homeCaption:"Powered by",termsOfService:"SnapID利用規約",history:"履歴"},wo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. 다수의 요청으로 인해 AI 의상 생성에 <span>다소 시간이 소요</span>될 수 있습니다.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. 공식 서류에 AI 생성 의상을 사용하면 <span>부적격 가능성</span>이 높아질 수 있습니다. 공식 홈페이지에서 자세한 서류 요구 사항을 확인 후 진행하시는 것을 권장합니다.",IDPhoto:"증명사진",JPPosterSize:"89x127mm","Keep waiting":"계속 대기","Server is busy.":"서버가 사용 중입니다.","Stop generating":"생성 중단",THPosterSize:"4x6인치",adjustTips:"테두리에 맞게 얼굴 위치를 조정하세요",adjust_tips:"서류 제출 시, 사진이 좌우 반전되거나 뒤집힌 이미지가 아닌지 반드시 확인하시기 바랍니다.",adjust_title:"위치 조정",afghanistan:"🇦🇫 아프가니스탄",africa:"아프리카",agree:"동의",agreeTOS:'계속 진행하면 <a href="https://snapid.ai/terms-of-service">서비스 약관</a> 및 <a href="https://snapid.ai/privacy-policy">개인정보 취급방침</a>에 동의하는 것으로 간주됩니다.',albania:"🇦🇱 알바니아",albumPermissionContent:"앱에 카메라 및 모든 사진에 대한 액세스 권한을 부여했는지 확인하세요.",albumPermissionTitle:"권한이 필요합니다.",algeria:"🇩🇿 알제리",almost_there_just_1_second_to_go:"거의 다 됐습니다! 단 1초만 기다려 주세요",american_samoa:"🇦🇸 아메리칸 사모아",andorra:"🇦🇩 안도라",angola:"🇦🇴 앙골라",anguilla:"🇦🇮 앵귈라",antigua_barbuda:"🇦🇬 앤티가 바부다",argentina:"🇦🇷 아르헨티나",armenia:"🇦🇲 아르메니아",asia:"아시아",australia:"🇦🇺 오스트레일리아",austria:"🇦🇹 오스트리아",azerbaijan:"🇦🇿 아제르바이잔",background:"배경",background_updated:"배경 업데이트됨",bahamas:"🇧🇸 바하마",bahrain:"🇧🇭 바레인",bangladesh:"🇧🇩 방글라데시",bangs:"앞머리",barbados:"🇧🇧 바베이도스",belarus:"🇧🇾 벨라루스",belgium:"🇧🇪 벨기에",belize:"🇧🇿 벨리즈",benin:"🇧🇯 베냉",bermuda:"🇧🇲 버뮤다",bhutan:"🇧🇹 부탄",bolivia:"🇧🇴 볼리비아",bosnia_herzegovina:"🇧🇦 보스니아 헤르체고비나",botswana:"🇧🇼 보츠와나",brazil:"🇧🇷 브라질",british_virgin_islands:"🇻🇬 영국령 버진아일랜드",brunei:"🇧🇳 브루나이",bulgaria:"🇧🇬 불가리아",burkina_faso:"🇧🇫 부르키나파소",burundi:"🇧🇮 부룬디",cabo_verde:"🇨🇻 카보베르데",cambodia:"🇰🇭 캄보디아",cameroon:"🇨🇲 카메룬",canada:"🇨🇦 캐나다",cancel:"취소",cancelGeneratorTips:"생성을 중단하시겠습니까?",central_african_republic:"🇨🇫 중앙아프리카 공화국",chad:"🇹🇩 차드",chile:"🇨🇱 칠레",china:"🇨🇳 중국",christmas_island:"🇨🇽 크리스마스 섬",close:"닫기",closer:"가깝게",cocos_keeling_islands:"🇨🇨 코코스 제도",colombia:"🇨🇴 콜롬비아",colorChange:"색상 변경",comoros:"🇰🇲 코모로",confirm:"확인",congo_brazzaville:"🇨🇬 콩고-브라자빌",congo_kinshasa:"🇨🇩 콩고-킨샤사",cook_islands:"🇨🇰 쿡 제도",costa_rica:"🇨🇷 코스타리카",croatia:"🇭🇷 크로아티아",cte_divoire:"🇨🇮 코트디부아르",cuba:"🇨🇺 쿠바",custom:"사용자 정의",custom_size:"사용자 지정 크기",cyprus:"🇨🇾 키프로스",czech_republic:"🇨🇿 체코 공화국",delete:"삭제",denmark:"🇩🇰 덴마크",digitalPhoto:"디지털 포토",digitalPhotoTips:"프린트용 콜라주 사진",djibouti:"🇩🇯 지부티",dominica:"🇩🇲 도미니카",dominican_republic:"🇩🇴 도미니카 공화국",ecuador:"🇪🇨 에콰도르",edit_title:"편집",egypt:"🇪🇬 이집트",el_salvador:"🇸🇻 엘살바도르",equatorial_guinea:"🇬🇶 적도 기니",eritrea:"🇪🇷 에리트레아",error1:"이미지 처리에 실패했습니다.",error2:"얼굴을 감지할 수 없습니다.",error3:"여러 사람이 있는 사진은 허용되지 않습니다.",estonia:"🇪🇪 에스토니아",eswatini:"🇸🇿 에스와티니",ethiopia:"🇪🇹 에티오피아",europe:"유럽",exitFirstMenuTips:"저장하지 않고 나가시겠습니까?",failed:"실패함",failed_to_generate:"생성에 실패하였습니다.",falkland_islands:"🇫🇰 폴클랜드 제도",faroe_islands:"🇫🇴 페로 제도",farther:"멀게",female:"여성",fiji:"🇫🇯 피지",finland:"🇫🇮 핀란드",france:"🇫🇷 프랑스",french_polynesia:"🇵🇫 프랑스령 폴리네시아",gabon:"🇬🇦 가봉",gambia:"🇬🇲 감비아",generateFailed:"생성 실패",generate_id_photo_desc:`1. 균일하고 부드러운 조명 아래에서 촬영합니다<br/>
2. 이목구비를 과도하게 리터치하거나 지우지 않습니다.`,georgia:"🇬🇪 조지아",germany:"🇩🇪 독일",getStarted:"시작하기",ghana:"🇬🇭 가나",gibraltar:"🇬🇮 지브롤터",gotIt:"알겠습니다",greece:"🇬🇷 그리스",greenland:"🇬🇱 그린란드",grenada:"🇬🇩 그레나다",guam:"🇬🇺 괌",guatemala:"🇬🇹 과테말라",guernsey:"🇬🇬 건지",guinea:"🇬🇳 기니",guineabissau:"🇬🇼 기니비사우",guyana:"🇬🇾 가이아나",haiti:"🇭🇹 아이티",head_position:"머리 위치",height:"높이",height_range_should_be_within_10120:"높이 범위는 {min}~{max} 이내여야 합니다.",holy_see_vatican_city:"🇻🇦 바티칸 시국(성좌)",homeTitle:"여권 사진",honduras:"🇭🇳 온두라스",hong_kong_sar_china:"🇭🇰 홍콩",hungary:"🇭🇺 헝가리",iceland:"🇮🇸 아이슬란드",idPhotoTip1:"1. 다른 사람에게 1-2m 떨어진 곳에서 사진을 촬영하도록 해 주세요.",idPhotoTip2:"2. 균일한 흰색 또는 밝은 회색 배경을 사용하세요.",idPhotoTip3:"3. 모든 액세서리를 제거하고 머리카락이 뒤로 넘겨졌는지 확인해 주세요.",idPhotoTip4:"4. 그림자, 앞머리 또는 옷이 얼굴을 가리지 않도록 해 주세요.",idPhotoTip5:"5. 눈을 뜬 채 입을 다물고 있는 무표정으로 촬영해 주세요.",idPhotoTip6:"6. 자국이나, 안경 빛 반사, '적목현상' 등이 없도록 해 주세요.",idTaskEmpty:"여기는 아무 것도 없어요! <br />증명사진 생성으로 이동하세요.",inch:"inch",india:"🇮🇳 인도",indonesia:"🇮🇩 인도네시아",iran:"🇮🇷 이란",iraq:"🇮🇶 이라크",ireland:"🇮🇪 아일랜드",isle_of_man:"🇮🇲 맨 섬",israel:"🇮🇱 이스라엘",italy:"🇮🇹 이탈리아",jamaica:"🇯🇲 자메이카",japan:"🇯🇵 일본",jersey:"🇯🇪 저지",jordan:"🇯🇴 요르단",kazakhstan:"🇰🇿 카자흐스탄",kenya:"🇰🇪 케냐",kiribati:"🇰🇮 키리바시",kuwait:"🇰🇼 쿠웨이트",kyrgyzstan:"🇰🇬 키르기스스탄",laos:"🇱🇦 라오스",later:"다음에",latvia:"🇱🇻 라트비아",lebanon:"🇱🇧 레바논",lesotho:"🇱🇸 레소토",liberia:"🇱🇷 라이베리아",libya:"🇱🇾 리비아",liechtenstein:"🇱🇮 리히텐슈타인",lithuania:"🇱🇹 리투아니아",loading:"로딩 중",loadingText1:"요청 처리 중입니다",loadingText2:"빠른 완료를 위해 앱을 활성화 상태로 유지하세요",loadingText3:"거의 다 됐습니다! 단 {time}초만 기다려 주세요",loadingText4:"AI 의상 바느질 중",loadingText5:"사진 스튜디오에 방문하는 것보다 훨씬 빨라요!",loadingText6:"조금만 더 기다려 주시면 감사하겠습니다",loadingText7:"기다릴 가치가 있는 뛰어난 결과를 완성해 드립니다!",loadingText8:"AI 의상 마감 작업 중, 거의 다 됐어요!",loadingText9:"요청을 완료하는 동안 기다려 주셔서 감사합니다",loginSaveTip1:"생성 기록은 <span>7일 동안</span> 저장되므로 생성 날짜를 기억해 두었다가 즉시 저장하시기 바랍니다.",luxembourg:"🇱🇺 룩셈부르크",macau_sar_china:"🇲🇴 마카오",madagascar:"🇲🇬 마다가스카르",malawi:"🇲🇼 말라위",malaysia:"🇲🇾 말레이시아",maldives:"🇲🇻 몰디브",male:"남성",mali:"🇲🇱 말리",malta:"🇲🇹 몰타",marshall_islands:"🇲🇭 마셜 제도",mauritania:"🇲🇷 모리타니",mauritius:"🇲🇺 모리셔스",mayotte:"🇾🇹 마요트",medium:"중간",mexico:"🇲🇽 멕시코",micronesia:"🇫🇲 미크로네시아 연방",mm:"mm",moldova:"🇲🇩 몰도바",monaco:"🇲🇨 모나코",mongolia:"🇲🇳 몽골",montenegro:"🇲🇪 몬테네그로",montserrat:"🇲🇸 몬트세랫",moreColors:"더 많은 색상",mozambique:"🇲🇿 모잠비크",myanmar_burma:"🇲🇲 미얀마",namibia:"🇳🇦 나미비아",nauru:"🇳🇷 나우루",nepal:"🇳🇵 네팔",netherlands:"🇳🇱 네덜란드",network_error:"네트워크 연결 끊김",network_error_info:"네트워크 오류.",network_error_message:"네트워크 설정을 확인해 주세요.",network_retry:"재시도",new_caledonia:"🇳🇨 뉴칼레도니아",new_zealand:"🇳🇿 뉴질랜드",next:"다음",nicaragua:"🇳🇮 니카라과",niger:"🇳🇪 니제르",nigeria:"🇳🇬 나이지리아",niue:"🇳🇺 니우에",no:"아니오",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"결과를 찾을 수 없습니다. 다른 키워드를 사용해 보시거나 크기를 사용자 지정하십시오.",no_search_results_found_please_try_a_different_keyword:"검색 결과가 없습니다. 다른 키워드를 사용해 보세요.",norfolk_island:"🇳🇫 노퍽 섬",north_america:"북아메리카",north_korea:"🇰🇵 북한",north_macedonia:"🇲🇰 북마케도니아",northern_mariana_islands:"🇲🇵 북마리아나 제도",norway:"🇳🇴 노르웨이",notice:"공지사항",oceania:"오세아니아",ok:"확인",oman:"🇴🇲 오만",outfit:"차림새",pakistan:"🇵🇰 파키스탄",palau:"🇵🇼 팔라우",palestinian_territories:"🇵🇸 팔레스타인 지구",panama:"🇵🇦 파나마",papua_new_guinea:"🇵🇬 파푸아뉴기니",paraguay:"🇵🇾 파라과이",payment:"결제",paymentFailure:"결제 실패!",peru:"🇵🇪 페루",philippines:"🇵🇭 필리핀",photoResize:"사진 크기 조정",photo_size:"사진 크기",pitcairn_islands:"🇵🇳 피트케언 제도",poland:"🇵🇱 폴란드",portugal:"🇵🇹 포르투갈",posterSize:"5x7인치",preview_link:"공식 홈페이지 확인",printSize:"프린트 사이즈",privacy_policy:"개인정보 보호 정책",puerto_rico:"🇵🇷 푸에르토리코",purchase:"구매",px:"픽셀",qatar:"🇶🇦 카타르",reset:"재설정",results:"결과",revert:"좌우 반전",romania:"🇷🇴 루마니아",runion:"🇷🇪 레위니옹",russia:"🇷🇺 러시아",rwanda:"🇷🇼 르완다",samoa:"🇼🇸 사모아",san_marino:"🇸🇲 산마리노",saudi_arabia:"🇸🇦 사우디아라비아",save:"저장",save_failed:"저장 실패",save_success:"저장 성공",search:"검색",search_for_a_country_or_region:"국가 또는 지역 검색",senegal:"🇸🇳 세네갈",serbia:"🇷🇸 세르비아",settings:"설정",seychelles:"🇸🇨 세이셸",share:"공유",shootingTips:"촬영 팁",sierra_leone:"🇸🇱 시에라리온",singapore:"🇸🇬 싱가포르",size:"사이즈",skin_retouch:"피부",slovakia:"🇸🇰 슬로바키아",slovenia:"🇸🇮 슬로베니아",so_tom_prncipe:"🇸🇹 상투메 프린시페",solomon_islands:"🇸🇧 솔로몬 제도",somalia:"🇸🇴 소말리아",south_africa:"🇿🇦 남아프리카",south_america:"남아메리카",south_korea:"🇰🇷 대한민국",south_sudan:"🇸🇸 남수단",spain:"🇪🇸 스페인",specialNotes:"주의 사항",specifications:"규격",sri_lanka:"🇱🇰 스리랑카",st_kitts_nevis:"🇰🇳 세인트키츠 네비스",st_lucia:"🇱🇨 세인트루시아",st_pierre_miquelon:"🇵🇲 생피에르 미클롱",st_vincent_grenadines:"🇻🇨 세인트빈센트 그레나딘",sudan:"🇸🇩 수단",suriname:"🇸🇷 수리남",svalbard_jan_mayen:"🇸🇯 스발바르 제도 및 얀마옌",sweden:"🇸🇪 스웨덴",switzerland:"🇨🇭 스위스",syria:"🇸🇾 시리아",taiwan:"🇹🇼 대만",tajikistan:"🇹🇯 타지키스탄",tanzania:"🇹🇿 탄자니아",taskExplore:"탐색",thailand:"🇹🇭 태국",timorleste:"🇹🇱 티모르-레스테",togo:"🇹🇬 토고",tokelau:"🇹🇰 토켈라우",tonga:"🇹🇴 통가",total:"총",trinidad_tobago:"🇹🇹 트리니다드 토바고",try_again:"재시도",tunisia:"🇹🇳 튀니지",turkey:"🇹🇷 터키",turkmenistan:"🇹🇲 투르크메니스탄",turks_caicos_islands:"🇹🇨 터크스 케이커스 제도",tuvalu:"🇹🇻 투발루",uganda:"🇺🇬 우간다",ukraine:"🇺🇦 우크라이나",unit:"컷/ 1장",united_arab_emirates:"🇦🇪 아랍에미리트 연합",united_kingdom:"🇬🇧 영국",united_states:"🇺🇸 미국",update:"업데이트",updateCancelButton:"나중에 다시",updateComfirmButton:"지금 업데이트",updateMessage:"새로운 콘텐츠가 준비되었습니다! '지금 업데이트'를 탭하여 최신 리소스를 로드하세요.",uruguay:"🇺🇾 우루과이",us_virgin_islands:"🇻🇮 미국령 버진아일랜드",uzbekistan:"🇺🇿 우즈베키스탄",vanuatu:"🇻🇺 바누아투",venezuela:"🇻🇪 베네수엘라",vietnam:"🇻🇳 베트남",wallis_futuna:"🇼🇫 왈리스 푸투나 제도",whiteEdgeTips:"더 넓은 범위의 인물 사진을 제공해 주세요",width:"너비",width_range_should_be_within_10120:"너비 범위는 {min}~{max} 이내여야 합니다.",yemen:"🇾🇪 예멘",yes:"네",zambia:"🇿🇲 잠비아",zimbabwe:"🇿🇼 짐바브웨",agreement:"이 기능을 사용하려면 {termsOfService} 및 {privacyPolicy}을 읽고 동의해야 합니다.",more:"더 보기",homeCaption:"Powered by",termsOfService:"SnapID 서비스 이용 약관",history:"히스토리"},So={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. A geração de Roupas de IA pode “demorar um pouco” devido ao grande volume de solicitações.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. O uso de equipamentos gerados por IA para documentos oficiais pode aumentar o “risco de rejeição da submissão”. Recomendamos verificar o site oficial para obter requisitos detalhados de documentos antes de prosseguir.",IDPhoto:"Foto de documento",JPPosterSize:"89x127mm","Keep waiting":"Continue esperando","Server is busy.":"O servidor está ocupado.","Stop generating":"Pare de gerar",THPosterSize:"4 x 6 pol.",adjustTips:"Ajuste o rosto ao longo da borda",adjust_tips:"Ao solicitar documentos oficiais, certifique-se de que a foto não esteja invertida da esquerda para a direita ou seja uma imagem espelhada.",adjust_title:"Ajustar posição",afghanistan:"🇦🇫 Afeganistão",africa:"África",agree:"Concordo",agreeTOS:'Ao continuar, você concorda com nossos <a href="https://snapid.ai/terms-of-service">Termos de Serviço</a> e <a href="https://snapid.ai/privacy-policy">Política de Privacidade</a>.',albania:"🇦🇱 Albânia",albumPermissionContent:"Certifique-se de ter concedido ao aplicativo acesso à Câmera e a Todas as fotos.",albumPermissionTitle:"É necessária permissão.",algeria:"🇩🇿 Argélia",almost_there_just_1_second_to_go:"Quase lá! Apenas 1 segundo para terminar",american_samoa:"🇦🇸 Samoa Americana",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguila",antigua_barbuda:"🇦🇬 Antígua e Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armênia",asia:"Ásia",australia:"🇦🇺 Austrália",austria:"🇦🇹 Áustria",azerbaijan:"🇦🇿 Azerbaijão",background:"Fundo",background_updated:"Plano de fundo atualizado",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Bahrein",bangladesh:"🇧🇩 Bangladesh",bangs:"Franja",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Bielorrússia",belgium:"🇧🇪 Bélgica",belize:"🇧🇿 Belize",benin:"🇧🇯 Benim",bermuda:"🇧🇲 Bermudas",bhutan:"🇧🇹 Butão",bolivia:"🇧🇴 Bolívia",bosnia_herzegovina:"🇧🇦 Bósnia e Herzegovina",botswana:"🇧🇼 Botsuana",brazil:"🇧🇷 Brasil",british_virgin_islands:"🇻🇬 Ilhas Virgens Britânicas",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgária",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Camboja",cameroon:"🇨🇲 Camarões",canada:"🇨🇦 Canadá",cancel:"Cancelar",cancelGeneratorTips:"Tem certeza de que deseja parar a geração?",central_african_republic:"🇨🇫 República Centro-Africana",chad:"🇹🇩 Chade",chile:"🇨🇱 Chile",china:"🇨🇳 China",christmas_island:"🇨🇽 Ilha Christmas",close:"Fechar",closer:"Mais perto",cocos_keeling_islands:"🇨🇨 Ilhas Cocos (Keeling)",colombia:"🇨🇴 Colômbia",colorChange:"Mudança de cor",comoros:"🇰🇲 Comores",confirm:"Confirmar",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Ilhas Cook",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Croácia",cte_divoire:"🇨🇮 Costa do Marfim",cuba:"🇨🇺 Cuba",custom:"Personalizada",custom_size:"Tamanho personalizado",cyprus:"🇨🇾 Chipre",czech_republic:"🇨🇿 República Tcheca",delete:"Excluir",denmark:"🇩🇰 Dinamarca",digitalPhoto:"Foto digital",digitalPhotoTips:"Colagem de fotos para impressão",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominica",dominican_republic:"🇩🇴 República Dominicana",ecuador:"🇪🇨 Equador",edit_title:"Editar",egypt:"🇪🇬 Egito",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Guiné Equatorial",eritrea:"🇪🇷 Eritreia",error1:"Falha no processamento da imagem.",error2:"Não foi possível detectar o rosto.",error3:"Não serão aceitas fotos com várias pessoas.",estonia:"🇪🇪 Estônia",eswatini:"🇸🇿 Essuatíni",ethiopia:"🇪🇹 Etiópia",europe:"Europa",exitFirstMenuTips:"Tem certeza de que deseja sair sem salvar?",failed:"Falhou",failed_to_generate:"Falha ao gerar.",falkland_islands:"🇫🇰 Ilhas Malvinas",faroe_islands:"🇫🇴 Ilhas Faroé",farther:"Mais afastado",female:"Feminino",fiji:"🇫🇯 Fiji",finland:"🇫🇮 Finlândia",france:"🇫🇷 França",french_polynesia:"🇵🇫 Polinésia Francesa",gabon:"🇬🇦 Gabão",gambia:"🇬🇲 Gâmbia",generateFailed:"Falha ao gerar",generate_id_photo_desc:`1. Fotografado sob iluminação suave e uniforme<br/>
2. Sem retoque excessivo ou apagamento de características faciais.`,georgia:"🇬🇪 Geórgia",germany:"🇩🇪 Alemanha",getStarted:"Comece",ghana:"🇬🇭 Gana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Entendi",greece:"🇬🇷 Grécia",greenland:"🇬🇱 Groenlândia",grenada:"🇬🇩 Granada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guiné",guineabissau:"🇬🇼 Guiné-Bissau",guyana:"🇬🇾 Guiana",haiti:"🇭🇹 Haiti",head_position:"Posição da cabeça",height:"Altura",height_range_should_be_within_10120:"A faixa de altura deve estar dentro de {min}~{max}.",holy_see_vatican_city:"🇻🇦 Santa Sé (Vaticano)",homeTitle:"Foto do passaporte",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong SAR China",hungary:"🇭🇺 Hungria",iceland:"🇮🇸 Islândia",idPhotoTip1:"1. Peça a outra pessoa que tire sua foto a uma distância de 1 a 2 m.",idPhotoTip2:"2. Encontre um fundo branco ou cinza claro.",idPhotoTip3:"3. Remova todos os acessórios. Certifique-se de que o cabelo esteja puxado para trás.",idPhotoTip4:"4. Nenhuma sombra, cabelo na frente ou roupa obscureça o rosto.",idPhotoTip5:"5. Expressão neutra com olhos abertos e boca fechada.",idPhotoTip6:"6. Sem marcas, reflexos nos óculos ou “olhos vermelhos”.",idTaskEmpty:"Nada para ver aqui! <br />Vá para criar fotos de documento.",inch:"polegada",india:"🇮🇳 Índia",indonesia:"🇮🇩 Indonésia",iran:"🇮🇷 Irã",iraq:"🇮🇶 Iraque",ireland:"🇮🇪 Irlanda",isle_of_man:"🇮🇲 Ilha de Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Itália",jamaica:"🇯🇲 Jamaica",japan:"🇯🇵 Japão",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordânia",kazakhstan:"🇰🇿 Cazaquistão",kenya:"🇰🇪 Quênia",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Quirguistão",laos:"🇱🇦 Laos",later:"Mais tarde",latvia:"🇱🇻 Letônia",lebanon:"🇱🇧 Líbano",lesotho:"🇱🇸 Lesoto",liberia:"🇱🇷 Libéria",libya:"🇱🇾 Líbia",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lituânia",loading:"Carregando",loadingText1:"Sua solicitação está a caminho",loadingText2:"Por favor, mantenha o aplicativo aberto para uma conclusão rápida",loadingText3:"Quase lá! Apenas {time} segundos para terminar",loadingText4:"Costurando sua roupa de IA",loadingText5:"Mais rápido do que uma ida ao estúdio fotográfico!",loadingText6:"Só mais um pouco, sua paciência é muito apreciada",loadingText7:"Vale a pena esperar por ótimos resultados!",loadingText8:"Polir sua roupa gerada por IA está quase pronto!",loadingText9:"Agradecemos sua paciência enquanto concluímos sua solicitação",loginSaveTip1:"Seu registro de geração ficará salvo por <span>7 dias</span>; anote a data de criação e salve-a imediatamente.",luxembourg:"🇱🇺 Luxemburgo",macau_sar_china:"🇲🇴 Macau SAR China",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malaui",malaysia:"🇲🇾 Malásia",maldives:"🇲🇻 Maldivas",male:"Masculino",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Ilhas Marshall",mauritania:"🇲🇷 Mauritânia",mauritius:"🇲🇺 Maurício",mayotte:"🇾🇹 Mayotte",medium:"Médio",mexico:"🇲🇽 México",micronesia:"🇫🇲 Micronésia",mm:"mm",moldova:"🇲🇩 Moldávia",monaco:"🇲🇨 Mônaco",mongolia:"🇲🇳 Mongólia",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Mais cores",mozambique:"🇲🇿 Moçambique",myanmar_burma:"🇲🇲 Mianmar (Birmânia)",namibia:"🇳🇦 Namíbia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Países Baixos",network_error:"Rede desconectada",network_error_info:"Erro de rede.",network_error_message:"Verifique suas configurações de rede.",network_retry:"Tente novamente",new_caledonia:"🇳🇨 Nova Caledônia",new_zealand:"🇳🇿 Nova Zelândia",next:"Próximo",nicaragua:"🇳🇮 Nicarágua",niger:"🇳🇪 Níger",nigeria:"🇳🇬 Nigéria",niue:"🇳🇺 Niue",no:"NÃO",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Nenhum resultado encontrado. Tente uma palavra-chave diferente ou personalize seu próprio tamanho.",no_search_results_found_please_try_a_different_keyword:"Nenhum resultado de pesquisa encontrado. Por favor, tente uma palavra-chave diferente.",norfolk_island:"🇳🇫 Ilha Norfolk",north_america:"América do Norte",north_korea:"🇰🇵 Coreia do Norte",north_macedonia:"🇲🇰 Macedônia do Norte",northern_mariana_islands:"🇲🇵 Ilhas Marianas do Norte",norway:"🇳🇴 Noruega",notice:"Aviso",oceania:"Oceania",ok:"OK",oman:"🇴🇲 Omã",outfit:"Roupa",pakistan:"🇵🇰 Paquistão",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Territórios Palestinos",panama:"🇵🇦 Panamá",papua_new_guinea:"🇵🇬 Papua-Nova Guiné",paraguay:"🇵🇾 Paraguai",payment:"Pagamento",paymentFailure:"Pagamento falhou!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Filipinas",photoResize:"Ajuste foto",photo_size:"Tamanho da foto",pitcairn_islands:"🇵🇳 Ilhas Pitcairn",poland:"🇵🇱 Polônia",portugal:"🇵🇹 Portugal",posterSize:"5 x 7 pol.",preview_link:"Visite o site oficial",printSize:"Tamanho de impressão",privacy_policy:"Política de Privacidade",puerto_rico:"🇵🇷 Porto Rico",purchase:"Compra",px:"px",qatar:"🇶🇦 Catar",reset:"Redefinir",results:"Resultados",revert:"Virar horizontalmente",romania:"🇷🇴 Romênia",runion:"🇷🇪 Reunião",russia:"🇷🇺 Rússia",rwanda:"🇷🇼 Ruanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Arábia Saudita",save:"Salvar",save_failed:"Falha ao salvar",save_success:"Salvo com sucesso",search:"Pesquisar",search_for_a_country_or_region:"Pesquise um país ou região",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Sérvia",settings:"Configurações",seychelles:"🇸🇨 Seicheles",share:"Compartilhar",shootingTips:"Dicas para fotografar",sierra_leone:"🇸🇱 Serra Leoa",singapore:"🇸🇬 Singapura",size:"Tamanho",skin_retouch:"Pele",slovakia:"🇸🇰 Eslováquia",slovenia:"🇸🇮 Eslovênia",so_tom_prncipe:"🇸🇹 São Tomé e Príncipe",solomon_islands:"🇸🇧 Ilhas Salomão",somalia:"🇸🇴 Somália",south_africa:"🇿🇦 África do Sul",south_america:"América do Sul",south_korea:"🇰🇷 Coreia do Sul",south_sudan:"🇸🇸 Sudão do Sul",spain:"🇪🇸 Espanha",specialNotes:"Notas especiais",specifications:"Especificações",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 São Cristóvão e Nevis",st_lucia:"🇱🇨 Santa Lúcia",st_pierre_miquelon:"🇵🇲 São Pedro e Miquelão",st_vincent_grenadines:"🇻🇨 São Vicente e Granadinas",sudan:"🇸🇩 Sudão",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard e Jan Mayen",sweden:"🇸🇪 Suécia",switzerland:"🇨🇭 Suíça",syria:"🇸🇾 Síria",taiwan:"🇹🇼 Taiwan",tajikistan:"🇹🇯 Tadjiquistão",tanzania:"🇹🇿 Tanzânia",taskExplore:"Explorar",thailand:"🇹🇭 Tailândia",timorleste:"🇹🇱 Timor-Leste",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Total",trinidad_tobago:"🇹🇹 Trinidad e Tobago",try_again:"Tente novamente",tunisia:"🇹🇳 Tunísia",turkey:"🇹🇷 Turquia",turkmenistan:"🇹🇲 Turcomenistão",turks_caicos_islands:"🇹🇨 Ilhas Turcas e Caicos",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ucrânia",unit:"Fotocópias",united_arab_emirates:"🇦🇪 Emirados Árabes Unidos",united_kingdom:"🇬🇧 Reino Unido",united_states:"🇺🇸 Estados Unidos",update:"Atualizar",updateCancelButton:"Talvez mais tarde",updateComfirmButton:"Atualizar agora",updateMessage:"Novo conteúdo está pronto! Toque em “Atualizar agora” para carregar os recursos mais recentes.",uruguay:"🇺🇾 Uruguai",us_virgin_islands:"🇻🇮 Ilhas Virgens Americanas",uzbekistan:"🇺🇿 Uzbequistão",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnã",wallis_futuna:"🇼🇫 Wallis e Futuna",whiteEdgeTips:"Forneça uma foto de retrato com uma gama maior",width:"Largura",width_range_should_be_within_10120:"A faixa de largura deve estar dentro de {min}~{max}.",yemen:"🇾🇪 Iêmen",yes:"Sim",zambia:"🇿🇲 Zâmbia",zimbabwe:"🇿🇼 Zimbábue",agreement:"O uso desta função requer que você conheça e concorde com {termsOfService} e {privacyPolicy}.",more:"Mais",homeCaption:"Powered by",termsOfService:"Termos de Serviço do SnapID",history:"Histórico"},To={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. На генерирование ИИ-одежды может <span>потребоваться время</span> из-за большого количества запросов.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. Использование сгенерированной ИИ одежды для официальных документов может повысить <span>риск отклонения заявления</span>. Мы рекомендуем ознакомиться с подробными требованиями к документам на официальном веб-сайте, перед тем как продолжить.",IDPhoto:"Фото на документы",JPPosterSize:"89x127мм","Keep waiting":"Подождать","Server is busy.":"Сервер занят.","Stop generating":"Прервать генерирование",THPosterSize:"4x6 дюймов",adjustTips:"Скорректируйте положение лица по линии рамки",adjust_tips:"Отправляя фотографию для получения официальных документов, убедитесь, что фотография не является зеркально отраженной.",adjust_title:"Скорректировать положение",afghanistan:"🇦🇫 Афганистан",africa:"Африка",agree:"Соглашаюсь",agreeTOS:'Продолжая работу, вы соглашаетесь с нашими <a href="https://snapid.ai/terms-of-service">Условиями обслуживания</a> и <a href="https://snapid.ai/privacy-policy">Политикой конфиденциальности</a>.',albania:"🇦🇱 Албания",albumPermissionContent:'Убедитесь, что вы разрешили доступ приложению к Камере и папке "Все фотографии".',albumPermissionTitle:"Требуется разрешение.",algeria:"🇩🇿 Алжир",almost_there_just_1_second_to_go:"Почто готово! Осталось всего 1 секунда",american_samoa:"🇦🇸 Американское Самоа",andorra:"🇦🇩 Андорра",angola:"🇦🇴 Ангола",anguilla:"🇦🇮 Ангилья",antigua_barbuda:"🇦🇬 Антигуа и Барбуда",argentina:"🇦🇷 Аргентина",armenia:"🇦🇲 Армения",asia:"Азия",australia:"🇦🇺 Австралия",austria:"🇦🇹 Австрия",azerbaijan:"🇦🇿 Азербайджан",background:"Фон",background_updated:"Фон обновлен",bahamas:"🇧🇸 Багамы",bahrain:"🇧🇭 Бахрейн",bangladesh:"🇧🇩 Бангладеш",bangs:"Челка",barbados:"🇧🇧 Барбадос",belarus:"🇧🇾 Беларусь",belgium:"🇧🇪 Бельгия",belize:"🇧🇿 Белиз",benin:"🇧🇯 Бенин",bermuda:"🇧🇲 Бермуды",bhutan:"🇧🇹 Бутан",bolivia:"🇧🇴 Боливия",bosnia_herzegovina:"🇧🇦 Босния и Герцеговина",botswana:"🇧🇼 Ботсвана",brazil:"🇧🇷 Бразилия",british_virgin_islands:"🇻🇬 Британские Виргинские острова",brunei:"🇧🇳 Бруней",bulgaria:"🇧🇬 Болгария",burkina_faso:"🇧🇫 Буркина-Фасо",burundi:"🇧🇮 Бурунди",cabo_verde:"🇨🇻 Кабо-Верде",cambodia:"🇰🇭 Камбоджа",cameroon:"🇨🇲 Камерун",canada:"🇨🇦 Канада",cancel:"Отмена",cancelGeneratorTips:"Вы уверены, что хотите остановить генерирование?",central_african_republic:"🇨🇫 Центральноафриканская Республика",chad:"🇹🇩 Чад",chile:"🇨🇱 Чили",china:"🇨🇳 Китай",christmas_island:"🇨🇽 Остров Рождества",close:"Закрыть",closer:"Ближне",cocos_keeling_islands:"🇨🇨 Кокосовые острова",colombia:"🇨🇴 Колумбия",colorChange:"Изменение цвета",comoros:"🇰🇲 Коморы",confirm:"Подтвердить",congo_brazzaville:"🇨🇬 Конго - Браззавиль",congo_kinshasa:"🇨🇩 Конго - Киншаса",cook_islands:"🇨🇰 Острова Кука",costa_rica:"🇨🇷 Коста-Рика",croatia:"🇭🇷 Хорватия",cte_divoire:"🇨🇮 Кот-д’Ивуар",cuba:"🇨🇺 Куба",custom:"Настройка",custom_size:"Пользовательский размер",cyprus:"🇨🇾 Кипр",czech_republic:"🇨🇿 Чехия",delete:"Удалить",denmark:"🇩🇰 Дания",digitalPhoto:"Цифровое фото",digitalPhotoTips:"Фотоколлаж для печати",djibouti:"🇩🇯 Джибути",dominica:"🇩🇲 Доминика",dominican_republic:"🇩🇴 Доминиканская Республика",ecuador:"🇪🇨 Эквадор",edit_title:"Редактировать",egypt:"🇪🇬 Египет",el_salvador:"🇸🇻 Сальвадор",equatorial_guinea:"🇬🇶 Экваториальная Гвинея",eritrea:"🇪🇷 Эритрея",error1:"Сбой обработки изображения.",error2:"Невозможно найти лицо.",error3:"Фотографии с несколькими людьми не принимаются.",estonia:"🇪🇪 Эстония",eswatini:"🇸🇿 Эсватини",ethiopia:"🇪🇹 Эфиопия",europe:"Европа",exitFirstMenuTips:"Вы уверены, что хотите выйти без сохранения?",failed:"Сбой",failed_to_generate:"Не удалось выполнить генерирование.",falkland_islands:"🇫🇰 Фолклендские острова",faroe_islands:"🇫🇴 Фарерские острова",farther:"Дальше",female:"Женщина",fiji:"🇫🇯 Фиджи",finland:"🇫🇮 Финляндия",france:"🇫🇷 Франция",french_polynesia:"🇵🇫 Французская Полинезия",gabon:"🇬🇦 Габон",gambia:"🇬🇲 Гамбия",generateFailed:"Генерирование не выполнено",generate_id_photo_desc:`1. Фотография должна быть сделана при равномерном мягком освещении<br/>
2. Запрещается избыточная ретушь и коррекция черт лица.`,georgia:"🇬🇪 Грузия",germany:"🇩🇪 Германия",getStarted:"Начало работы",ghana:"🇬🇭 Гана",gibraltar:"🇬🇮 Гибралтар",gotIt:"Понятно",greece:"🇬🇷 Греция",greenland:"🇬🇱 Гренландия",grenada:"🇬🇩 Гренада",guam:"🇬🇺 Гуам",guatemala:"🇬🇹 Гватемала",guernsey:"🇬🇬 Гернси",guinea:"🇬🇳 Гвинея",guineabissau:"🇬🇼 Гвинея-Бисау",guyana:"🇬🇾 Гайана",haiti:"🇭🇹 Гаити",head_position:"Положение головы",height:"Высота",height_range_should_be_within_10120:"Высота должна быть в пределах {min}~{max}.",holy_see_vatican_city:"🇻🇦 Ватикан",homeTitle:"Фото на паспорт",honduras:"🇭🇳 Гондурас",hong_kong_sar_china:"🇭🇰 Гонконг САР",hungary:"🇭🇺 Венгрия",iceland:"🇮🇸 Исландия",idPhotoTip1:"1. Попросите кого-нибудь сфотографировать вас с расстояния 1-2 м.",idPhotoTip2:"2. Найдите однородный белый или светло-серый фон.",idPhotoTip3:"3. Снимите все аксессуары. Уберите волосы от лица.",idPhotoTip4:"4. На лицо не должны падать тени, волосы, его не должны закрывать элементы одежды.",idPhotoTip5:"5. Нейтральное выражение лица с открытыми глазами и закрытым ртом.",idPhotoTip6:"6. Без дефектов, бликов на очках и эффекта красных глаз.",idTaskEmpty:"Здесь ничего нет!<br />Создайте фото на документы.",inch:"дюйма(-ов)",india:"🇮🇳 Индия",indonesia:"🇮🇩 Индонезия",iran:"🇮🇷 Иран",iraq:"🇮🇶 Ирак",ireland:"🇮🇪 Ирландия",isle_of_man:"🇮🇲 Остров Мэн",israel:"🇮🇱 Израиль",italy:"🇮🇹 Италия",jamaica:"🇯🇲 Ямайка",japan:"🇯🇵 Япония",jersey:"🇯🇪 Джерси",jordan:"🇯🇴 Иордания",kazakhstan:"🇰🇿 Казахстан",kenya:"🇰🇪 Кения",kiribati:"🇰🇮 Кирибати",kuwait:"🇰🇼 Кувейт",kyrgyzstan:"🇰🇬 Киргизия",laos:"🇱🇦 Лаос",later:"Позже",latvia:"🇱🇻 Латвия",lebanon:"🇱🇧 Ливан",lesotho:"🇱🇸 Лесото",liberia:"🇱🇷 Либерия",libya:"🇱🇾 Ливия",liechtenstein:"🇱🇮 Лихтенштейн",lithuania:"🇱🇹 Литва",loading:"Загрузка",loadingText1:"Ваш запрос выполняется",loadingText2:"Пожалуйста, не закрывайте приложение, процесс будет скоро завершен",loadingText3:"Почто готово! Осталось всего {time} секунд",loadingText4:"Выполняется примерка вашей ИИ-одежды",loadingText5:"Быстрее, чем поездка в фотостудию!",loadingText6:"Осталось чуть-чуть, мы очень ценим ваше терпение",loadingText7:"Превосходный результат стоит того, чтобы подождать!",loadingText8:"Корректируем вашу сгенерированную ИИ одежду, почти готово!",loadingText9:"Благодарим за терпение при выполнении нами вашего запроса",loginSaveTip1:"Сгенерированные вами записи будут храниться <span>7 дней</span>. Обратите внимание на дату создания и своевременно выполните сохранение.",luxembourg:"🇱🇺 Люксембург",macau_sar_china:"🇲🇴 Макао САР",madagascar:"🇲🇬 Мадагаскар",malawi:"🇲🇼 Малави",malaysia:"🇲🇾 Малайзия",maldives:"🇲🇻 Мальдивы",male:"Мужчина",mali:"🇲🇱 Мали",malta:"🇲🇹 Мальта",marshall_islands:"🇲🇭 Маршалловы Острова",mauritania:"🇲🇷 Мавритания",mauritius:"🇲🇺 Маврикий",mayotte:"🇾🇹 Майотта",medium:"Среднее",mexico:"🇲🇽 Мексика",micronesia:"🇫🇲 Микронезия",mm:"мм",moldova:"🇲🇩 Молдавия",monaco:"🇲🇨 Монако",mongolia:"🇲🇳 Монголия",montenegro:"🇲🇪 Черногория",montserrat:"🇲🇸 Монтсеррат",moreColors:"Другие цвета",mozambique:"🇲🇿 Мозамбик",myanmar_burma:"🇲🇲 Мьянма",namibia:"🇳🇦 Намибия",nauru:"🇳🇷 Науру",nepal:"🇳🇵 Непал",netherlands:"🇳🇱 Нидерланды",network_error:"Сеть отключена",network_error_info:"Ошибка сети.",network_error_message:"Пожалуйста, проверьте настройки сети.",network_retry:"Повторить",new_caledonia:"🇳🇨 Новая Каледония",new_zealand:"🇳🇿 Новая Зеландия",next:"Далее",nicaragua:"🇳🇮 Никарагуа",niger:"🇳🇪 Нигер",nigeria:"🇳🇬 Нигерия",niue:"🇳🇺 Ниуэ",no:"НЕТ",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Результатов не найдено. Попробуйте другое ключевое слово или установите свой собственный размер.",no_search_results_found_please_try_a_different_keyword:"Результатов не найдено. Попробуйте использовать другое ключевое слово.",norfolk_island:"🇳🇫 Остров Норфолк",north_america:"Северная Америка",north_korea:"🇰🇵 Северная Корея",north_macedonia:"🇲🇰 Северная Македония",northern_mariana_islands:"🇲🇵 Северные Марианские острова",norway:"🇳🇴 Норвегия",notice:"Уведомление",oceania:"Океания",ok:"OK",oman:"🇴🇲 Оман",outfit:"Комплект одежды",pakistan:"🇵🇰 Пакистан",palau:"🇵🇼 Палау",palestinian_territories:"🇵🇸 Палестинские территории",panama:"🇵🇦 Панама",papua_new_guinea:"🇵🇬 Папуа - Новая Гвинея",paraguay:"🇵🇾 Парагвай",payment:"Оплата",paymentFailure:"Сбой платежа!",peru:"🇵🇪 Перу",philippines:"🇵🇭 Филиппины",photoResize:"Калибровка",photo_size:"Размер фото",pitcairn_islands:"🇵🇳 Острова Питкэрн",poland:"🇵🇱 Польша",portugal:"🇵🇹 Португалия",posterSize:"5х7 дюймов",preview_link:"Перейти на официальный веб-сайт",printSize:"Размер при печати",privacy_policy:"Политика конф-ти",puerto_rico:"🇵🇷 Пуэрто-Рико",purchase:"Купить",px:"px",qatar:"🇶🇦 Катар",reset:"Сброс",results:"Результаты",revert:"Отразить по горизонтали",romania:"🇷🇴 Румыния",runion:"🇷🇪 Реюньон",russia:"🇷🇺 Россия",rwanda:"🇷🇼 Руанда",samoa:"🇼🇸 Самоа",san_marino:"🇸🇲 Сан-Марино",saudi_arabia:"🇸🇦 Саудовская Аравия",save:"Сохранить",save_failed:"Не удалось сохранить",save_success:"Успешно сохранено",search:"Поиск",search_for_a_country_or_region:"Поиск страны или региона",senegal:"🇸🇳 Сенегал",serbia:"🇷🇸 Сербия",settings:"Настройки",seychelles:"🇸🇨 Сейшельские Острова",share:"Поделиться",shootingTips:"Советы по съемке",sierra_leone:"🇸🇱 Сьерра-Леоне",singapore:"🇸🇬 Сингапур",size:"Размер",skin_retouch:"Кожа",slovakia:"🇸🇰 Словакия",slovenia:"🇸🇮 Словения",so_tom_prncipe:"🇸🇹 Сан-Томе и Принсипи",solomon_islands:"🇸🇧 Соломоновы Острова",somalia:"🇸🇴 Сомали",south_africa:"🇿🇦 Южная Африка",south_america:"Южная Америка",south_korea:"🇰🇷 Южная Корея",south_sudan:"🇸🇸 Южный Судан",spain:"🇪🇸 Испания",specialNotes:"Особые примечания",specifications:"Требования",sri_lanka:"🇱🇰 Шри-Ланка",st_kitts_nevis:"🇰🇳 Сент-Китс и Невис",st_lucia:"🇱🇨 Сент-Люсия",st_pierre_miquelon:"🇵🇲 Сен-Пьер и Микелон",st_vincent_grenadines:"🇻🇨 Сент-Винсент и Гренадины",sudan:"🇸🇩 Судан",suriname:"🇸🇷 Суринам",svalbard_jan_mayen:"🇸🇯 Шпицберген и Ян-Майен",sweden:"🇸🇪 Швеция",switzerland:"🇨🇭 Швейцария",syria:"🇸🇾 Сирия",taiwan:"🇹🇼 Тайвань",tajikistan:"🇹🇯 Таджикистан",tanzania:"🇹🇿 Танзания",taskExplore:"Познакомиться",thailand:"🇹🇭 Таиланд",timorleste:"🇹🇱 Восточный Тимор",togo:"🇹🇬 Того",tokelau:"🇹🇰 Токелау",tonga:"🇹🇴 Тонга",total:"Итого",trinidad_tobago:"🇹🇹 Тринидад и Тобаго",try_again:"Повторить попытку",tunisia:"🇹🇳 Тунис",turkey:"🇹🇷 Турция",turkmenistan:"🇹🇲 Туркменистан",turks_caicos_islands:"🇹🇨 Теркс и Кайкос",tuvalu:"🇹🇻 Тувалу",uganda:"🇺🇬 Уганда",ukraine:"🇺🇦 Украина",unit:"Фотокопии",united_arab_emirates:"🇦🇪 Объединённые Арабские Эмираты",united_kingdom:"🇬🇧 Великобритания",united_states:"🇺🇸 Соединенные Штаты Америки",update:"Обновить",updateCancelButton:"Возможно, позже",updateComfirmButton:"Обновить",updateMessage:'Новый контент готов! Нажмите "Обновить", чтобы загрузить актуальные ресурсы.',uruguay:"🇺🇾 Уругвай",us_virgin_islands:"🇻🇮 Виргинские острова США",uzbekistan:"🇺🇿 Узбекистан",vanuatu:"🇻🇺 Вануату",venezuela:"🇻🇪 Венесуэла",vietnam:"🇻🇳 Вьетнам",wallis_futuna:"🇼🇫 Уоллис и Футуна",whiteEdgeTips:"Пожалуйста, предоставьте портретное фото с более широким охватом",width:"Ширина",width_range_should_be_within_10120:"Ширина должна быть в пределах {min}~{max}.",yemen:"🇾🇪 Йемен",yes:"Да",zambia:"🇿🇲 Замбия",zimbabwe:"🇿🇼 Зимбабве",agreement:"Для использования этой функции необходимо ознакомиться и согласиться с {termsOfService} и {privacyPolicy}.",more:"Еще",homeCaption:"Powered by",termsOfService:"Условия обслуживания SnapID",history:"История"},xo={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. การสร้างชุดเสื้อผ้า AI อาจ <span>ใช้เวลาสักครู่</span> เนื่องจากมีคำขอจำนวนมาก",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. การใช้ชุดเสื้อผ้าที่สร้างโดย AI สำหรับเอกสารราชการอาจเพิ่ม <span>ความเสี่ยงต่อการถูกปฏิเสธการส่ง</span> เราขอแนะนำให้ตรวจสอบกับเว็บไซต์อย่างเป็นทางการเพื่อดูรายละเอียดข้อกำหนดของเอกสารก่อนดำเนินการต่อ",IDPhoto:"รูปถ่ายติดบัตร",JPPosterSize:"89x127มม.","Keep waiting":"กรุณารอสักครู่","Server is busy.":"เซิร์ฟเวอร์ไม่ว่าง","Stop generating":"หยุดสร้าง",THPosterSize:"4x6 นิ้ว",adjustTips:"โปรดปรับใบหน้าตามเส้นขอบ",adjust_tips:"เมื่อยื่นขอเอกสารทางราชการ ขอให้ตรวจสอบให้แน่ใจว่าภาพถ่ายไม่ได้กลับด้านซ้าย-ขวาหรือเป็นภาพสะท้อน",adjust_title:"ปรับตำแหน่ง",afghanistan:"🇦🇫 อัฟกานิสถาน",africa:"แอฟริกา",agree:"ยอมรับ",agreeTOS:'การดำเนินการต่อแสดงว่าคุณยอมรับ <a href="https://snapid.ai/terms-of-service">ข้อตกลงการให้บริการ</a> และ <a href="https://snapid.ai/privacy-policy">นโยบายความเป็นส่วนตัว</a> ของเรา',albania:"🇦🇱 แอลเบเนีย",albumPermissionContent:"โปรดตรวจสอบให้แน่ใจว่าคุณได้ให้สิทธิ์แอปในการเข้าถึงกล้องถ่ายรูปและรูปภาพทั้งหมด",albumPermissionTitle:"ต้องได้รับอนุญาต",algeria:"🇩🇿 แอลจีเรีย",almost_there_just_1_second_to_go:"ใกล้จะเสร็จแล้ว อีก 1 วินาทีเท่านั้น",american_samoa:"🇦🇸 อเมริกันซามัว",andorra:"🇦🇩 อันดอร์รา",angola:"🇦🇴 แองโกลา",anguilla:"🇦🇮 แองกวิลลา",antigua_barbuda:"🇦🇬 แอนติกาและบาร์บูดา",argentina:"🇦🇷 อาร์เจนตินา",armenia:"🇦🇲 อาร์เมเนีย",asia:"เอเชีย",australia:"🇦🇺 ออสเตรเลีย",austria:"🇦🇹 ออสเตรีย",azerbaijan:"🇦🇿 อาเซอร์ไบจาน",background:"พื้นหลัง",background_updated:"อัปเดตพื้นหลังแล้ว",bahamas:"🇧🇸 บาฮามาส",bahrain:"🇧🇭 บาห์เรน",bangladesh:"🇧🇩 บังกลาเทศ",bangs:"หน้าม้า",barbados:"🇧🇧 บาร์เบโดส",belarus:"🇧🇾 เบลารุส",belgium:"🇧🇪 เบลเยียม",belize:"🇧🇿 เบลีซ",benin:"🇧🇯 เบนิน",bermuda:"🇧🇲 เบอร์มิวดา",bhutan:"🇧🇹 ภูฏาน",bolivia:"🇧🇴 โบลิเวีย",bosnia_herzegovina:"🇧🇦 บอสเนียและเฮอร์เซโกวีนา",botswana:"🇧🇼 บอตสวานา",brazil:"🇧🇷 บราซิล",british_virgin_islands:"🇻🇬 หมู่เกาะบริติชเวอร์จิน",brunei:"🇧🇳 บรูไน",bulgaria:"🇧🇬 บัลแกเรีย",burkina_faso:"🇧🇫 บูร์กินาฟาโซ",burundi:"🇧🇮 บุรุนดี",cabo_verde:"🇨🇻 กาบูเวร์ดี",cambodia:"🇰🇭 กัมพูชา",cameroon:"🇨🇲 แคเมอรูน",canada:"🇨🇦 แคนาดา",cancel:"ยกเลิก",cancelGeneratorTips:"คุณแน่ใจหรือไม่ว่าต้องการหยุดการสร้าง?",central_african_republic:"🇨🇫 สาธารณรัฐแอฟริกากลาง",chad:"🇹🇩 ชาด",chile:"🇨🇱 ชิลี",china:"🇨🇳 จีน",christmas_island:"🇨🇽 เกาะคริสต์มาส",close:"ปิด",closer:"ใกล้ขึ้น",cocos_keeling_islands:"🇨🇨 เกาะโคโคส (คีลิง)",colombia:"🇨🇴 โคลอมเบีย",colorChange:"เปลี่ยนสี",comoros:"🇰🇲 คอโมโรส",confirm:"ยืนยัน",congo_brazzaville:"🇨🇬 คองโก - บราซซาวิล",congo_kinshasa:"🇨🇩 คองโก - กินชาซา",cook_islands:"🇨🇰 หมู่เกาะคุก",costa_rica:"🇨🇷 คอสตาริกา",croatia:"🇭🇷 โครเอเชีย",cte_divoire:"🇨🇮 โกตดิวัวร์",cuba:"🇨🇺 คิวบา",custom:"กำหนดเอง",custom_size:"กำหนดขนาดเอง",cyprus:"🇨🇾 ไซปรัส",czech_republic:"🇨🇿 สาธารณรัฐเช็ก",delete:"ลบ",denmark:"🇩🇰 เดนมาร์ก",digitalPhoto:"รูปภาพดิจิทัล",digitalPhotoTips:"ตัดปะรูปภาพสำหรับการพิมพ์",djibouti:"🇩🇯 จิบูตี",dominica:"🇩🇲 โดมินิกา",dominican_republic:"🇩🇴 สาธารณรัฐโดมินิกัน",ecuador:"🇪🇨 เอกวาดอร์",edit_title:"แก้ไข",egypt:"🇪🇬 อียิปต์",el_salvador:"🇸🇻 เอลซัลวาดอร์",equatorial_guinea:"🇬🇶 อิเควทอเรียลกินี",eritrea:"🇪🇷 เอริเทรีย",error1:"ประมวลผลภาพไม่สำเร็จ",error2:"ตรวจสอบไม่พบใบหน้า",error3:"ภาพถ่ายที่มีหลายคนจะไม่ได้รับการยอมรับ",estonia:"🇪🇪 เอสโตเนีย",eswatini:"🇸🇿 อีสวาตีนี",ethiopia:"🇪🇹 เอธิโอเปีย",europe:"ยุโรป",exitFirstMenuTips:"คุณแน่ใจหรือไม่ว่าต้องการออกโดยไม่บันทึก?",failed:"ไม่สำเร็จ",failed_to_generate:"สร้างไม่สำเร็จ",falkland_islands:"🇫🇰 หมู่เกาะฟอล์กแลนด์",faroe_islands:"🇫🇴 หมู่เกาะแฟโร",farther:"ไกลออกไป",female:"หญิง",fiji:"🇫🇯 ฟิจิ",finland:"🇫🇮 ฟินแลนด์",france:"🇫🇷 ฝรั่งเศส",french_polynesia:"🇵🇫 โปลินีเซียฝรั่งเศส",gabon:"🇬🇦 กาบอง",gambia:"🇬🇲 แกมเบีย",generateFailed:"การสร้างไม่สำเร็จ",generate_id_photo_desc:`1. ถ่ายภาพภายใต้แสงที่สม่ำเสมอและนุ่มนวล
<br /> 2. ไม่มีการรีทัชหรือลบส่วนใบหน้ามากเกินไป`,georgia:"🇬🇪 จอร์เจีย",germany:"🇩🇪 เยอรมนี",getStarted:"เริ่มต้นใช้งาน",ghana:"🇬🇭 กานา",gibraltar:"🇬🇮 ยิบรอลตาร์",gotIt:"ทราบแล้ว",greece:"🇬🇷 กรีซ",greenland:"🇬🇱 กรีนแลนด์",grenada:"🇬🇩 เกรนาดา",guam:"🇬🇺 กวม",guatemala:"🇬🇹 กัวเตมาลา",guernsey:"🇬🇬 เกิร์นซีย์",guinea:"🇬🇳 กินี",guineabissau:"🇬🇼 กินี-บิสเซา",guyana:"🇬🇾 กายอานา",haiti:"🇭🇹 ไฮติ",head_position:"ตำแหน่งศีรษะ",height:"ความสูง",height_range_should_be_within_10120:"ช่วงความสูงควรอยู่ระหว่าง {min}~{max}.",holy_see_vatican_city:"🇻🇦 นครวาติกัน (วาติกันซิตี้)",homeTitle:"รูปถ่ายพาสปอร์ต",honduras:"🇭🇳 ฮอนดูรัส",hong_kong_sar_china:"🇭🇰 ฮ่องกง SAR จีน",hungary:"🇭🇺 ฮังการี",iceland:"🇮🇸 ไอซ์แลนด์",idPhotoTip1:"1. ให้คนอื่นถ่ายรูปคุณในระยะห่าง 1-2 เมตร",idPhotoTip2:"2. หาพื้นหลังที่เรียบและสม่ำเสมอ สีขาวหรือสีเทาอ่อน",idPhotoTip3:"3. ถอดเครื่องประดับทั้งหมดออก ตรวจสอบให้แน่ใจว่าผมหวีไปข้างหลัง",idPhotoTip4:"4. ไม่มีเงา ผมด้านหน้า หรือเสื้อผ้ามาบดบังใบหน้า",idPhotoTip5:"5. การแสดงออกทางใบหน้าที่เป็นกลางโดยลืมตาและปิดปาก",idPhotoTip6:"6. ไม่มีรอย แสงสะท้อนจากแว่นตาหรือ'จุดตาแดง'",idTaskEmpty:"ไม่มีอะไรที่นี่! <br />ไปสร้างรูปถ่ายติดบัตร",inch:"นิ้ว",india:"🇮🇳 อินเดีย",indonesia:"🇮🇩 อินโดนีเซีย",iran:"🇮🇷 อิหร่าน",iraq:"🇮🇶 อิรัก",ireland:"🇮🇪 ไอร์แลนด์",isle_of_man:"🇮🇲 เกาะแมน",israel:"🇮🇱 อิสราเอล",italy:"🇮🇹 อิตาลี",jamaica:"🇯🇲 จาเมกา",japan:"🇯🇵 ญี่ปุ่น",jersey:"🇯🇪 เจอร์ซีย์",jordan:"🇯🇴 จอร์แดน",kazakhstan:"🇰🇿 คาซัคสถาน",kenya:"🇰🇪 เคนยา",kiribati:"🇰🇮 คิริบาส",kuwait:"🇰🇼 คูเวต",kyrgyzstan:"🇰🇬 คีร์กีซสถาน",laos:"🇱🇦 ลาว",later:"ไว้ทีหลัง",latvia:"🇱🇻 ลัตเวีย",lebanon:"🇱🇧 เลบานอน",lesotho:"🇱🇸 เลโซโท",liberia:"🇱🇷 ไลบีเรีย",libya:"🇱🇾 ลิเบีย",liechtenstein:"🇱🇮 ลิกเตนสไตน์",lithuania:"🇱🇹 ลิทัวเนีย",loading:"กำลังโหลด",loadingText1:"คำขอของคุณอยู่ระหว่างดำเนินการ",loadingText2:"โปรดเปิดแอปไว้เพื่อให้ดำเนินการเสร็จอย่างรวดเร็ว",loadingText3:"ใกล้จะเสร็จแล้ว อีก {time} วินาทีเท่านั้น",loadingText4:"ตัดเย็บชุดเสื้อผ้า AI ของคุณ",loadingText5:"เร็วกว่าไปสตูดิโอถ่ายรูปซะอีก!",loadingText6:"รออีกนิด ขอบคุณสำหรับการรอคอย",loadingText7:"ผลลัพธ์ที่ยอดเยี่ยมคุ้มค่าแก่การรอคอย!",loadingText8:"ชุดเสื้อผ้าที่สร้างขึ้นโดย AI ของคุณ ใกล้เสร็จแล้ว!",loadingText9:"ขอขอบคุณที่รอในขณะที่เราดำเนินการตามคำขอของคุณ",loginSaveTip1:"ประวัติการสร้างของคุณจะถูกบันทึกไว้เป็นเวลา <span>7 วัน</span> โปรดจดวันที่สร้างและบันทึกทันที",luxembourg:"🇱🇺 ลักเซมเบิร์ก",macau_sar_china:"🇲🇴 มาเก๊า SAR จีน",madagascar:"🇲🇬 มาดากัสการ์",malawi:"🇲🇼 มาลาวี",malaysia:"🇲🇾 มาเลเซีย",maldives:"🇲🇻 มัลดีฟส์",male:"ชาย",mali:"🇲🇱 มาลี",malta:"🇲🇹 มอลตา",marshall_islands:"🇲🇭 หมู่เกาะมาร์แชลล์",mauritania:"🇲🇷 มอริเตเนีย",mauritius:"🇲🇺 มอริเชียส",mayotte:"🇾🇹 มายอต",medium:"สีน้ำตาลอ่อน",mexico:"🇲🇽 เม็กซิโก",micronesia:"🇫🇲 ไมโครนีเซีย",mm:"มม.",moldova:"🇲🇩 มอลโดวา",monaco:"🇲🇨 โมนาโก",mongolia:"🇲🇳 มองโกเลีย",montenegro:"🇲🇪 มอนเตเนโกร",montserrat:"🇲🇸 มอนต์เซอร์รัต",moreColors:"สีเพิ่มเติม",mozambique:"🇲🇿 โมซัมบิก",myanmar_burma:"🇲🇲 พม่า (เมียนมาร์)",namibia:"🇳🇦 นามิเบีย",nauru:"🇳🇷 นาอูรู",nepal:"🇳🇵 เนปาล",netherlands:"🇳🇱 เนเธอร์แลนด์",network_error:"เครือข่ายถูกตัดการเชื่อมต่อ",network_error_info:"ข้อผิดพลาดของเครือข่าย",network_error_message:"โปรดตรวจสอบการตั้งค่าเครือข่ายของคุณ",network_retry:"ลองอีกครั้ง",new_caledonia:"🇳🇨 นิวแคลิโดเนีย",new_zealand:"🇳🇿 นิวซีแลนด์",next:"ต่อไป",nicaragua:"🇳🇮 นิการากัว",niger:"🇳🇪 ไนเจอร์",nigeria:"🇳🇬 ไนจีเรีย",niue:"🇳🇺 นีอูเอ",no:"ไม่",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"ไม่พบผลลัพธ์ โปรดลองใช้คีย์เวิร์ดอื่นหรือปรับขนาดของคุณเอง",no_search_results_found_please_try_a_different_keyword:"ไม่พบผลการค้นหา โปรดลองใช้คีย์เวิร์ดอื่น",norfolk_island:"🇳🇫 หมู่เกาะนอร์ฟอล์ก",north_america:"อเมริกาเหนือ",north_korea:"🇰🇵 เกาหลีเหนือ",north_macedonia:"🇲🇰 มาซิโดเนียเหนือ",northern_mariana_islands:"🇲🇵 หมู่เกาะนอร์เทิร์นมาเรียนา",norway:"🇳🇴 นอร์เวย์",notice:"ข้อสังเกต",oceania:"โอเชียเนีย",ok:"ตกลง",oman:"🇴🇲 โอมาน",outfit:"เครื่องแต่งกาย",pakistan:"🇵🇰 ปากีสถาน",palau:"🇵🇼 ปาเลา",palestinian_territories:"🇵🇸 ดินแดนปาเลสไตน์",panama:"🇵🇦 ปานามา",papua_new_guinea:"🇵🇬 ปาปัวนิวกินี",paraguay:"🇵🇾 ปารากวัย",payment:"การชำระเงิน",paymentFailure:"ชำระเงินไม่สำเร็จ",peru:"🇵🇪 เปรู",philippines:"🇵🇭 ฟิลิปปินส์",photoResize:"ปรับขนาด",photo_size:"ขนาดรูปภาพ",pitcairn_islands:"🇵🇳 หมู่เกาะพิตแคร์น",poland:"🇵🇱 โปแลนด์",portugal:"🇵🇹 โปรตุเกส",posterSize:"5x7in",preview_link:"เยี่ยมชมเว็บไซต์อย่างเป็นทางการ",printSize:"ขนาดการพิมพ์",privacy_policy:"นโยบายความเป็นส่วนตัว",puerto_rico:"🇵🇷 เปอร์โตริโก",purchase:"ซื้อ",px:"พิกเซล",qatar:"🇶🇦 กาตาร์",reset:"รีเซ็ต",results:"ผลลัพธ์",revert:"พลิกในแนวนอน",romania:"🇷🇴 โรมาเนีย",runion:"🇷🇪 เรอูนียง",russia:"🇷🇺 รัสเซีย",rwanda:"🇷🇼 รวันดา",samoa:"🇼🇸 ซามัว",san_marino:"🇸🇲 ซานมารีโน",saudi_arabia:"🇸🇦 ซาอุดีอาระเบีย",save:"บันทึก",save_failed:"บันทึกไม่สำเร็จ",save_success:"บันทึกสำเร็จ",search:"ค้นหา",search_for_a_country_or_region:"ค้นหาประเทศหรือภูมิภาค",senegal:"🇸🇳 เซเนกัล",serbia:"🇷🇸 เซอร์เบีย",settings:"การตั้งค่า",seychelles:"🇸🇨 เซเชลส์",share:"แชร์",shootingTips:"เคล็ดลับการถ่ายภาพ",sierra_leone:"🇸🇱 เซียร์ราลีโอน",singapore:"🇸🇬 สิงคโปร์",size:"ขนาด",skin_retouch:"ผิว",slovakia:"🇸🇰 สโลวะเกีย",slovenia:"🇸🇮 สโลวีเนีย",so_tom_prncipe:"🇸🇹 เซาตูเมและปรินซิปี",solomon_islands:"🇸🇧 หมู่เกาะโซโลมอน",somalia:"🇸🇴 โซมาเลีย",south_africa:"🇿🇦 แอฟริกาใต้",south_america:"อเมริกาใต้",south_korea:"🇰🇷 เกาหลีใต้",south_sudan:"🇸🇸 ซูดานใต้",spain:"🇪🇸 สเปน",specialNotes:"หมายเหตุพิเศษ",specifications:"ข้อมูลจำเพาะ",sri_lanka:"🇱🇰 ศรีลังกา",st_kitts_nevis:"🇰🇳 เซนต์คิตส์และเนวิส",st_lucia:"🇱🇨 เซนต์ลูเซีย",st_pierre_miquelon:"🇵🇲 เซนต์ปิแอร์และมีเกอลง",st_vincent_grenadines:"🇻🇨 เซนต์วินเซนต์และเกรนาดีนส์",sudan:"🇸🇩 ซูดาน",suriname:"🇸🇷 ซูรินาม",svalbard_jan_mayen:"🇸🇯 สฟาลบาร์และแยนไมเอน",sweden:"🇸🇪 สวีเดน",switzerland:"🇨🇭 สวิตเซอร์แลนด์",syria:"🇸🇾 ซีเรีย",taiwan:"🇹🇼 ไต้หวัน",tajikistan:"🇹🇯 ทาจิกิสถาน",tanzania:"🇹🇿 แทนซาเนีย",taskExplore:"สำรวจ",thailand:"🇹🇭 ไทย",timorleste:"🇹🇱 ติมอร์-เลสเต",togo:"🇹🇬 โตโก",tokelau:"🇹🇰 โตเกเลา",tonga:"🇹🇴 ตองกา",total:"รวม",trinidad_tobago:"🇹🇹 ตรินิแดดและโตเบโก",try_again:"ลองอีกครั้ง",tunisia:"🇹🇳 ตูนิเซีย",turkey:"🇹🇷 ตุรกี",turkmenistan:"🇹🇲 เติร์กเมนิสถาน",turks_caicos_islands:"🇹🇨 หมู่เกาะเติกส์และคีโกส",tuvalu:"🇹🇻 ตูวาลู",uganda:"🇺🇬 ยูกันดา",ukraine:"🇺🇦 ยูเครน",unit:"รูป",united_arab_emirates:"🇦🇪 สหรัฐอาหรับเอมิเรตส์",united_kingdom:"🇬🇧 สหราชอาณาจักร",united_states:"🇺🇸 สหรัฐอเมริกา",update:"อัปเดต",updateCancelButton:"ไว้ก่อน",updateComfirmButton:"อัปเดตตอนนี้",updateMessage:'เนื้อหาใหม่พร้อมแล้ว! แตะ "อัปเดตตอนนี้" เพื่อโหลดทรัพยากรล่าสุด',uruguay:"🇺🇾 อุรุกวัย",us_virgin_islands:"🇻🇮 หมู่เกาะยูเอสเวอร์จิน",uzbekistan:"🇺🇿 อุซเบกิสถาน",vanuatu:"🇻🇺 วานูอาตู",venezuela:"🇻🇪 เวเนซุเอลา",vietnam:"🇻🇳 เวียดนาม",wallis_futuna:"🇼🇫 วาลลิสและฟุตูนา",whiteEdgeTips:"โปรดให้ภาพถ่ายบุคคลที่มีขอบเขตกว้างขึ้น",width:"ความกว้าง",width_range_should_be_within_10120:"ช่วงความกว้างควรอยู่ระหว่าง {min}~{max}.",yemen:"🇾🇪 เยเมน",yes:"ใช่",zambia:"🇿🇲 แซมเบีย",zimbabwe:"🇿🇼 ซิมบับเว",agreement:"การใช้ฟังก์ชันนี้คุณต้องทราบและยอมรับ {termsOfService} และ {privacyPolicy}",more:"เพิ่มเติม",homeCaption:"Powered by",termsOfService:"เงื่อนไขการให้บริการของ SnapID",history:"สร้างประวัติ"},Co={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. YZ Kıyafet oluşturma işlemi, çok sayıda istek sayısına bağlı olarak <span>biraz zaman alabilir</span>.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. YZ Tarafından Oluşturulan kıyafetleri kullanmak, <span>gönderi reddi riskini</span> artırabilir. Devam etmeden önce belgelerle ilgili ayrıntılı gereksinimler için resmi web sitesini konttrol etmenizi öneririz.",IDPhoto:"Kimlik Fotoğrafı",JPPosterSize:"89x127mm",THPosterSize:"10,16 x 15,24 cm (4 x 6 inç)",adjustTips:"Lütfen yüzü sınır boyunca ayarlayın",adjust_tips:"Resmi belgeler için başvuru yaparken lütfen fotoğrafın yatay ekranda sağa ya da sola çevrilmediğinden emin olun.",adjust_title:"Pozisyonu ayarlayın",afghanistan:"🇦🇫 Afghanistan",africa:"Africa",agree:"Kabul ediyorum",agreeTOS:'Devam ederek <a href="https://snapid.ai/terms-of-service">Hizmet Şartlarımızı</a> ve <a href="https://snapid.ai/privacy-policy">Gizlilik Politikamızı</a> kabul etmiş olursunuz.',albania:"🇦🇱 Albania",albumPermissionContent:"Lütfen uygulamaya Kamera ve Tüm Fotoğraflara erişim izni verdiğinizden emin olun.",albumPermissionTitle:"İzin gerekiyor.",algeria:"🇩🇿 Algeria",almost_there_just_1_second_to_go:"Neredeyse bitti! Sadece 1 saniye kaldı",american_samoa:"🇦🇸 American Samoa",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua ve Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armenia",asia:"Asia",australia:"🇦🇺 Australia",austria:"🇦🇹 Austria",azerbaijan:"🇦🇿 Azerbaijan",background:"Arka plan",background_updated:"Arka Plan Güncellendi",bahamas:"🇧🇸 Bahamalar",bahrain:"🇧🇭 Bahrain",bangladesh:"🇧🇩 Bangladesh",bangs:"Kâküller",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Belarus",belgium:"🇧🇪 Belgium",belize:"🇧🇿 Belize",benin:"🇧🇯 Benin",bermuda:"🇧🇲 Bermuda",bhutan:"🇧🇹 Bhutan",bolivia:"🇧🇴 Bolivia",bosnia_herzegovina:"🇧🇦 Bosnia & Herzegovina",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brazil",british_virgin_islands:"🇻🇬 Britanya Virjin Adaları",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgaria",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Cambodia",cameroon:"🇨🇲 Cameroon",canada:"🇨🇦 Kanada",cancel:"İptal",cancelGeneratorTips:"Oluşturma işlemini durdurmak istediğinizden emin misiniz?",central_african_republic:"🇨🇫 Central African Republic",chad:"🇹🇩 Chad",chile:"🇨🇱 Chile",china:"🇨🇳 China",christmas_island:"🇨🇽 Christmas Island",close:"Kapat",closer:"Daha Yakın",cocos_keeling_islands:"🇨🇨 Cocos (Keeling) Islands",colombia:"🇨🇴 Colombia",colorChange:"Renk Değiştirme",comoros:"🇰🇲 Comoros",confirm:"ONAYLA",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Cook Islands",costa_rica:"🇨🇷 Kosta Rika",croatia:"🇭🇷 Croatia",cte_divoire:"🇨🇮 Côte d’Ivoire",cuba:"🇨🇺 Küba",custom:"Özel",custom_size:"Özel Boyut",cyprus:"🇨🇾 Cyprus",czech_republic:"🇨🇿 Czech Republic",delete:"Sil",denmark:"🇩🇰 Denmark",digitalPhoto:"Dijital fotoğraf",digitalPhotoTips:"Baskı için kolaj fotoğraf",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominika",dominican_republic:"🇩🇴 Dominik Cumhuriyeti",ecuador:"🇪🇨 Ecuador",edit_title:"Düzenle",egypt:"🇪🇬 Egypt",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Equatorial Guinea",eritrea:"🇪🇷 Eritrea",error1:"Resim işlenemedi.",error2:"Yüz algılanamadı.",error3:"Birden fazla kişi içeren fotoğraflar kabul edilmez.",estonia:"🇪🇪 Estonia",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Ethiopia",europe:"Europe",exitFirstMenuTips:"Kaydetmeden çıkmak istediğinizden emin misiniz?",failed:"Başarısız",failed_to_generate:"Oluşturulamadı",falkland_islands:"🇫🇰 Falkland Islands",faroe_islands:"🇫🇴 Faroe Islands",farther:"Daha Uzak",female:"Kadın",fiji:"🇫🇯 Fiji",finland:"🇫🇮 Finland",france:"🇫🇷 France",french_polynesia:"🇵🇫 French Polynesia",gabon:"🇬🇦 Gabon",gambia:"🇬🇲 Gambia",generateFailed:"Oluşturma Başarısız",generate_id_photo_desc:`1. Fotoğraf eşit yayılan, yumuşak ışık altında çekilmiş olmalıdır<br/>
2. Yüz özelliklerinde aşırı rötuş ya da silme olmamalıdır`,georgia:"🇬🇪 Georgia",germany:"🇩🇪 Germany",getStarted:"Başlayın",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Anladım",greece:"🇬🇷 Greece",greenland:"🇬🇱 Grönland",grenada:"🇬🇩 Grenada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haiti",head_position:"Baş Boyutu",height:"Yükseklik",height_range_should_be_within_10120:"Yükseklik aralığı, {min}-{max} arasında olmalıdır.",holy_see_vatican_city:"🇻🇦 Holy See (Vatican City)",homeTitle:"Pasaport Fotoğrafı",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong SAR China",hungary:"🇭🇺 Hungary",iceland:"🇮🇸 Iceland",idPhotoTip1:"1. Fotoğrafınızı 1-2 m uzaktan başka birine çektirin.",idPhotoTip2:"2. Düz beyaz veya açık gri bir arka plan bulun.",idPhotoTip3:"3. Tüm aksesuarları çıkarın. Saçlarınızı arkaya topladığınızdan emin olun.",idPhotoTip4:"4. Gölgeler, kaküller veya giysiler yüzün herhangi bir kısmını kapatmamalıdır.",idPhotoTip5:"5. Gözler açık, ağız kapalı, nötr bir ifade takının.",idPhotoTip6:"6. Fotoğrafta iz, gözlük yansıması veya “kırmızı göz” bozukluğu olmamalıdır.",idTaskEmpty:"Burada hiçbir şey yok! <br />Hemen kimlik fotoğrafları oluşturun.",inch:"inç",india:"🇮🇳 India",indonesia:"🇮🇩 Indonesia",iran:"🇮🇷 Iran",iraq:"🇮🇶 Iraq",ireland:"🇮🇪 Ireland",isle_of_man:"🇮🇲 Isle of Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Italy",jamaica:"🇯🇲 Jamaika",japan:"🇯🇵 Japan",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordan",kazakhstan:"🇰🇿 Kazakhstan",kenya:"🇰🇪 Kenya",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kyrgyzstan",laos:"🇱🇦 Laos",later:"Sonra",latvia:"🇱🇻 Latvia",lebanon:"🇱🇧 Lebanon",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libya",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lithuania",loading:"Yükleniyor",loadingText1:"Talebiniz işleniyor",loadingText2:"Hızlı tamamlanması için lütfen uygulamayı açık tutun",loadingText3:"Neredeyse bitti! Sadece {time} saniye kaldı",loadingText4:"YZ kıyaferiniz dikiliyor",loadingText5:"Fotoğraf stüdyosuna gitmekten daha hızlı!",loadingText6:"Sadece biraz daha zaman alacak. Sabrınız için teşekkürler!",loadingText7:"Harika sonuçlar için beklemeye değer!",loadingText8:"YZ tarafından oluşturulan kıyaferinizin son dokunuşlarını yapıyoruz!",loadingText9:"İsteğinizi tamamlarken sabırla beklediğiniz için teşekkürler",loginSaveTip1:"Oluşturduğunuz öğeler <span>7 gün</span> boyunca saklanır. Lütfen oluşturulma tarihine dikkat edin ve kısa süre içinde bu öğeleri kaydedin.",luxembourg:"🇱🇺 Luxembourg",macau_sar_china:"🇲🇴 Macau SAR China",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaysia",maldives:"🇲🇻 Maldives",male:"Erkek",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Marshall Islands",mauritania:"🇲🇷 Mauritania",mauritius:"🇲🇺 Mauritius",mayotte:"🇾🇹 Mayotte",medium:"Orta",mexico:"🇲🇽 Meksika",micronesia:"🇫🇲 Micronesia",mm:"mm",moldova:"🇲🇩 Moldova",monaco:"🇲🇨 Monaco",mongolia:"🇲🇳 Mongolia",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Daha Fazla Renk",mozambique:"🇲🇿 Mozambique",myanmar_burma:"🇲🇲 Myanmar (Burma)",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Netherlands",network_error:"Ağ Bağlantısı Kesildi",network_error_info:"Ağ hatası.",network_error_message:"Lütfen ağ ayarlarınızı kontrol edin.",network_retry:"Tekrar Dene",new_caledonia:"🇳🇨 New Caledonia",new_zealand:"🇳🇿 New Zealand",next:"Sonraki",nicaragua:"🇳🇮 Nikaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"HAYIR",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Sonuç bulunamadı. Lütfen farklı bir anahtar kelime deneyin veya boyutu özelleştirin.",no_search_results_found_please_try_a_different_keyword:"Arama sonucu bulunamadı. Lütfen farklı bir anahtar sözcük deneyin.",norfolk_island:"🇳🇫 Norfolk Island",north_america:"Kuzey Amerika",north_korea:"🇰🇵 North Korea",north_macedonia:"🇲🇰 North Macedonia",northern_mariana_islands:"🇲🇵 Northern Mariana Islands",norway:"🇳🇴 Norway",notice:"Uyarı",oceania:"Oceania",ok:"Tamam",oman:"🇴🇲 Oman",outfit:"Kıyafet",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Palestinian Territories",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papua New Guinea",paraguay:"🇵🇾 Paraguay",payment:"Ödeme",paymentFailure:"Ödeme başarısız!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Philippines",photoResize:"Foto Boyutu",photo_size:"Fotoğraf Boyutu",pitcairn_islands:"🇵🇳 Pitcairn Islands",poland:"🇵🇱 Poland",portugal:"🇵🇹 Portugal",posterSize:"12,7 x 17,8 cm (5 x 7 inç)",preview_link:"Resmi web sitesini ziyaret edin",printSize:"Baskı Boyutu",privacy_policy:"Gizlilik Politikası",puerto_rico:"🇵🇷 Porto Riko",purchase:"Satın Alın",px:"piksel",qatar:"🇶🇦 Qatar",reset:"Sıfırla",results:"Sonuçlar",revert:"Yatay Ters Çevir",romania:"🇷🇴 Romania",runion:"🇷🇪 Réunion",russia:"🇷🇺 Russia",rwanda:"🇷🇼 Rwanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Saudi Arabia",save:"Kaydet",save_failed:"Kaydetme başarısız",save_success:"Başarıyla kaydedildi",search:"Ara",search_for_a_country_or_region:"Ülke veya bölge arayın",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbia",settings:"Ayarlar",seychelles:"🇸🇨 Seychelles",share:"Paylaş",shootingTips:"Fotoğraf Çekme İpuçları",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapore",size:"Boyut",skin_retouch:"Cilt",slovakia:"🇸🇰 Slovakia",slovenia:"🇸🇮 Slovenia",so_tom_prncipe:"🇸🇹 São Tomé & Príncipe",solomon_islands:"🇸🇧 Solomon Islands",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 South Africa",south_america:"South America",south_korea:"🇰🇷 South Korea",south_sudan:"🇸🇸 South Sudan",spain:"🇪🇸 Spain",specialNotes:"Özel notlar",specifications:"Spesifikasyonlar",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 Saint Kitts ve Nevis",st_lucia:"🇱🇨 Saint Lucia",st_pierre_miquelon:"🇵🇲 Saint Pierre ve Miquelon",st_vincent_grenadines:"🇻🇨 Saint Vincent ve Grenadinler",sudan:"🇸🇩 Sudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard & Jan Mayen",sweden:"🇸🇪 Sweden",switzerland:"🇨🇭 Switzerland",syria:"🇸🇾 Syria",taiwan:"🇹🇼 Taiwan",tajikistan:"🇹🇯 Tajikistan",tanzania:"🇹🇿 Tanzania",taskExplore:"Keşfet",thailand:"🇹🇭 Thailand",timorleste:"🇹🇱 Timor-Leste",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Toplam",trinidad_tobago:"🇹🇹 Trinidad & Tobago",try_again:"Tekrar Dene",tunisia:"🇹🇳 Tunisia",turkey:"🇹🇷 Turkey",turkmenistan:"🇹🇲 Turkmenistan",turks_caicos_islands:"🇹🇨 Turks ve Caicos Adaları",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ukraine",unit:"Fotokopiler",united_arab_emirates:"🇦🇪 United Arab Emirates",united_kingdom:"🇬🇧 United Kingdom",united_states:"🇺🇸 Amerika Birleşik Devletleri",update:"Güncelle",updateCancelButton:"Belki Daha Sonra",updateComfirmButton:"Şimdi Güncelle",updateMessage:'Yeni içerik hazır! En yeni kaynakları yüklemek için "Şimdi Güncelle" öğesine dokunun.',uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 ABD Virjin Adaları",uzbekistan:"🇺🇿 Uzbekistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Vietnam",wallis_futuna:"🇼🇫 Wallis & Futuna",whiteEdgeTips:"Daha geniş kapsamlı bir portre fotoğrafı sağlayın",width:"Genişlik",width_range_should_be_within_10120:"Genişlik aralığı, {min}-{max} arasında olmalıdır.",yemen:"🇾🇪 Yemen",yes:"Evet",zambia:"🇿🇲 Zambia",zimbabwe:"🇿🇼 Zimbabwe",agreement:"Bu işlevi kullanmak için {termsOfService} ve {privacyPolicy} belgelerini okuyarak kabul etmeniz gerekir.",more:"Daha fazla",homeCaption:"Powered by",termsOfService:"SnapID Hizmet Koşulları",history:"Geçmiş"},Po={'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"1. Thời gian tạo Trang phục AI có thể <span>mất một lúc</span> do có nhiều yêu cầu.",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"2. Sử dụng trang phục do AI tạo cho các loại giấy tờ chính thức có thể làm tăng <span>nguy cơ bị từ chối</span>. Bạn nên kiểm tra trang web chính thức để biết các yêu cầu chi tiết về giấy tờ trước khi tiếp tục.",IDPhoto:"Ảnh thẻ",JPPosterSize:"89x127mm","Keep waiting":"Hãy đợi chút","Server is busy.":"Máy chủ đang bận.","Stop generating":"Ngừng tạo",THPosterSize:"4x6 inch",adjustTips:"Vui lòng điều chỉnh khuôn mặt theo đường viền",adjust_tips:"Khi đi làm các loại giấy tờ chính thức, hãy đảm bảo ảnh không bị đảo ngược từ trái sang phải hoặc ngược lại hoặc bị phản chiếu.",adjust_title:"Điều chỉnh vị trí",afghanistan:"🇦🇫 Afghanistan",africa:"Châu Phi",agree:"Đồng ý",agreeTOS:"Bằng cách tiếp tục, bạn đồng ý với Điều khoản dịch vụ và Chính sách quyền riêng tư của chúng tôi.",albania:"🇦🇱 Albania",albumPermissionContent:"Hãy đảm bảo rằng bạn đã cấp cho ứng dụng quyền truy cập vào Máy ảnh và Tất cả ảnh.",albumPermissionTitle:"Cần có quyền.",algeria:"🇩🇿 Algeria",almost_there_just_1_second_to_go:"Sắp xong rồi! Chỉ còn 1 giây nữa",american_samoa:"🇦🇸 Samoa Mỹ",andorra:"🇦🇩 Andorra",angola:"🇦🇴 Angola",anguilla:"🇦🇮 Anguilla",antigua_barbuda:"🇦🇬 Antigua và Barbuda",argentina:"🇦🇷 Argentina",armenia:"🇦🇲 Armenia",asia:"Châu Á",australia:"🇦🇺 Úc",austria:"🇦🇹 Áo",azerbaijan:"🇦🇿 Azerbaijan",background:"Nền",background_updated:"Đã cập nhật nền",bahamas:"🇧🇸 Bahamas",bahrain:"🇧🇭 Bahrain",bangladesh:"🇧🇩 Bangladesh",bangs:"Tóc mái",barbados:"🇧🇧 Barbados",belarus:"🇧🇾 Belarus",belgium:"🇧🇪 Bỉ",belize:"🇧🇿 Belize",benin:"🇧🇯 Benin",bermuda:"🇧🇲 Bermuda",bhutan:"🇧🇹 Bhutan",bolivia:"🇧🇴 Bolivia",bosnia_herzegovina:"🇧🇦 Bosnia và Herzegovina",botswana:"🇧🇼 Botswana",brazil:"🇧🇷 Brazil",british_virgin_islands:"🇻🇬 Quần đảo Virgin thuộc Anh",brunei:"🇧🇳 Brunei",bulgaria:"🇧🇬 Bulgaria",burkina_faso:"🇧🇫 Burkina Faso",burundi:"🇧🇮 Burundi",cabo_verde:"🇨🇻 Cabo Verde",cambodia:"🇰🇭 Campuchia",cameroon:"🇨🇲 Cameroon",canada:"🇨🇦 Canada",cancel:"Hủy",cancelGeneratorTips:"Bạn có chắc muốn dừng tạo không?",central_african_republic:"🇨🇫 Cộng hòa Trung Phi",chad:"🇹🇩 Chad",chile:"🇨🇱 Chile",china:"🇨🇳 Trung Quốc",christmas_island:"🇨🇽 Đảo Christmas",close:"Đóng",closer:"Gần hơn",cocos_keeling_islands:"🇨🇨 Quần đảo Cocos (Keeling)",colombia:"🇨🇴 Colombia",colorChange:"Đổi màu",comoros:"🇰🇲 Comoros",confirm:"Xác nhận",congo_brazzaville:"🇨🇬 Congo - Brazzaville",congo_kinshasa:"🇨🇩 Congo - Kinshasa",cook_islands:"🇨🇰 Quần đảo Cook",costa_rica:"🇨🇷 Costa Rica",croatia:"🇭🇷 Croatia",cte_divoire:"🇨🇮 Côte d’Ivoire",cuba:"🇨🇺 Cuba",custom:"Tùy chỉnh",custom_size:"Kích cỡ tùy chỉnh",cyprus:"🇨🇾 Síp",czech_republic:"🇨🇿 Cộng hòa Séc",delete:"Xóa",denmark:"🇩🇰 Đan Mạch",digitalPhoto:"Ảnh kỹ thuật số",digitalPhotoTips:"Ảnh cắt dán để in ra",djibouti:"🇩🇯 Djibouti",dominica:"🇩🇲 Dominica",dominican_republic:"🇩🇴 Cộng hòa Dominica",ecuador:"🇪🇨 Ecuador",edit_title:"Chỉnh sửa",egypt:"🇪🇬 Ai Cập",el_salvador:"🇸🇻 El Salvador",equatorial_guinea:"🇬🇶 Guinea Xích đạo",eritrea:"🇪🇷 Eritrea",error1:"Xử lý hình ảnh không thành công.",error2:"Không phát hiện thấy khuôn mặt.",error3:"Không chấp nhận ảnh chụp có nhiều người.",estonia:"🇪🇪 Estonia",eswatini:"🇸🇿 Eswatini",ethiopia:"🇪🇹 Ethiopia",europe:"Châu Âu",exitFirstMenuTips:"Bạn có chắc muốn thoát mà không lưu gì?",failed:"Không thành công",failed_to_generate:"Không tạo được.",falkland_islands:"🇫🇰 Quần đảo Falkland",faroe_islands:"🇫🇴 Quần đảo Faroe",farther:"Xa hơn",female:"Nữ",fiji:"🇫🇯 Fiji",finland:"🇫🇮 Phần Lan",france:"🇫🇷 Pháp",french_polynesia:"🇵🇫 Polynesia Pháp",gabon:"🇬🇦 Gabon",gambia:"🇬🇲 Gambia",generateFailed:"Không thể tạo",generate_id_photo_desc:`1. Chụp ảnh với ánh sáng nhẹ, đồng đều<br/>
2. Không chỉnh sửa hoặc tẩy xóa các đặc điểm trên khuôn mặt quá mức.`,georgia:"🇬🇪 Georgia",germany:"🇩🇪 Đức",getStarted:"Bắt đầu",ghana:"🇬🇭 Ghana",gibraltar:"🇬🇮 Gibraltar",gotIt:"Đã hiểu",greece:"🇬🇷 Hy Lạp",greenland:"🇬🇱 Greenland",grenada:"🇬🇩 Grenada",guam:"🇬🇺 Guam",guatemala:"🇬🇹 Guatemala",guernsey:"🇬🇬 Guernsey",guinea:"🇬🇳 Guinea",guineabissau:"🇬🇼 Guinea-Bissau",guyana:"🇬🇾 Guyana",haiti:"🇭🇹 Haiti",head_position:"Vị trí đầu",height:"Chiều cao",height_range_should_be_within_10120:"Phạm vi chiều cao phải từ {min}~{max}.",holy_see_vatican_city:"🇻🇦 Vatican",homeTitle:"Ảnh hộ chiếu",honduras:"🇭🇳 Honduras",hong_kong_sar_china:"🇭🇰 Hong Kong",hungary:"🇭🇺 Hungary",iceland:"🇮🇸 Iceland",idPhotoTip1:"1. Nhờ người khác đứng xa 1-2 m chụp ảnh cho bạn.",idPhotoTip2:"2. Tìm phông nền có màu trắng trơn hoặc xám nhạt.",idPhotoTip3:"3. Tháo toàn bộ phụ kiện. Đảm bảo kéo tóc ra phía sau.",idPhotoTip4:"4. Không có bóng, tóc phía trước hoặc quần áo che khuất khuôn mặt.",idPhotoTip5:"5. Biểu cảm bình thường với mắt mở và miệng nhắm.",idPhotoTip6:"6. Không có dấu vết, phản chiếu từ kính hoặc hiện tượng <span>mắt đỏ</span>.",idTaskEmpty:"Không có gì để xem! <br />Đến phần tạo ảnh thẻ.",inch:"inch",india:"🇮🇳 Ấn Độ",indonesia:"🇮🇩 Indonesia",iran:"🇮🇷 Iran",iraq:"🇮🇶 Iraq",ireland:"🇮🇪 Ireland",isle_of_man:"🇮🇲 Đảo Man",israel:"🇮🇱 Israel",italy:"🇮🇹 Ý",jamaica:"🇯🇲 Jamaica",japan:"🇯🇵 Nhật Bản",jersey:"🇯🇪 Jersey",jordan:"🇯🇴 Jordan",kazakhstan:"🇰🇿 Kazakhstan",kenya:"🇰🇪 Kenya",kiribati:"🇰🇮 Kiribati",kuwait:"🇰🇼 Kuwait",kyrgyzstan:"🇰🇬 Kyrgyzstan",laos:"🇱🇦 Lào",later:"Để sau",latvia:"🇱🇻 Latvia",lebanon:"🇱🇧 Lebanon",lesotho:"🇱🇸 Lesotho",liberia:"🇱🇷 Liberia",libya:"🇱🇾 Libya",liechtenstein:"🇱🇮 Liechtenstein",lithuania:"🇱🇹 Lithuania",loading:"Đang tải",loadingText1:"Yêu cầu của bạn đang được xử lý",loadingText2:"Không được đóng ứng dụng để có thể hoàn thành nhanh chóng",loadingText3:"Sắp xong rồi! Chỉ còn {time} giây nữa",loadingText4:"AI đang thiết kế trang phục của bạn",loadingText5:"Nhanh hơn cả thời gian ra tiệm chụp ảnh!",loadingText6:"Chỉ một chút nữa, sự kiên nhẫn của bạn quá đỗi kinh ngạc",loadingText7:"Kết quả tuyệt vời đáng để chờ đợi!",loadingText8:"Đang hoàn thiện trang phục AI tạo cho bạn, sắp xong rồi!",loadingText9:"Cảm ơn bạn đã kiên nhẫn trong khi chúng tôi hoàn thành yêu cầu",loginSaveTip1:"Bản ghi bạn tạo sẽ được lưu trong <span>7 ngày</span>; hãy ghi lại ngày tạo và lưu lại kịp thời.",luxembourg:"🇱🇺 Luxembourg",macau_sar_china:"🇲🇴 Macau",madagascar:"🇲🇬 Madagascar",malawi:"🇲🇼 Malawi",malaysia:"🇲🇾 Malaysia",maldives:"🇲🇻 Maldives",male:"Nam",mali:"🇲🇱 Mali",malta:"🇲🇹 Malta",marshall_islands:"🇲🇭 Quần đảo Marshall",mauritania:"🇲🇷 Mauritania",mauritius:"🇲🇺 Mauritius",mayotte:"🇾🇹 Mayotte",medium:"Trung bình",mexico:"🇲🇽 Mexico",micronesia:"🇫🇲 Micronesia",mm:"mm",moldova:"🇲🇩 Moldova",monaco:"🇲🇨 Monaco",mongolia:"🇲🇳 Mông Cổ",montenegro:"🇲🇪 Montenegro",montserrat:"🇲🇸 Montserrat",moreColors:"Thêm màu",mozambique:"🇲🇿 Mozambique",myanmar_burma:"🇲🇲 Myanmar (Burma)",namibia:"🇳🇦 Namibia",nauru:"🇳🇷 Nauru",nepal:"🇳🇵 Nepal",netherlands:"🇳🇱 Hà Lan",network_error:"Đã ngắt kết nối mạng",network_error_info:"Lỗi mạng.",network_error_message:"Vui lòng kiểm tra cài đặt mạng của bạn.",network_retry:"Thử lại",new_caledonia:"🇳🇨 New Caledonia",new_zealand:"🇳🇿 New Zealand",next:"Tiếp",nicaragua:"🇳🇮 Nicaragua",niger:"🇳🇪 Niger",nigeria:"🇳🇬 Nigeria",niue:"🇳🇺 Niue",no:"KHÔNG",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"Không tìm thấy kết quả nào. Hãy thử một từ khóa khác hoặc tùy chỉnh kích cỡ tùy ý bạn.",no_search_results_found_please_try_a_different_keyword:"Không tìm thấy kết quả nào. Hãy thử một từ khóa khác.",norfolk_island:"🇳🇫 Đảo Norfolk",north_america:"Bắc Mỹ",north_korea:"🇰🇵 Bắc Triều Tiên",north_macedonia:"🇲🇰 Bắc Macedonia",northern_mariana_islands:"🇲🇵 Quần đảo Bắc Mariana",norway:"🇳🇴 Na Uy",notice:"Thông báo",oceania:"Châu Đại Dương",ok:"OK",oman:"🇴🇲 Oman",outfit:"Trang phục",pakistan:"🇵🇰 Pakistan",palau:"🇵🇼 Palau",palestinian_territories:"🇵🇸 Lãnh thổ Palestine",panama:"🇵🇦 Panama",papua_new_guinea:"🇵🇬 Papua New Guinea",paraguay:"🇵🇾 Paraguay",payment:"Thanh toán",paymentFailure:"Lỗi thanh toán!",peru:"🇵🇪 Peru",philippines:"🇵🇭 Philippines",photoResize:"Đổi cỡ ảnh",photo_size:"Kích cỡ ảnh",pitcairn_islands:"🇵🇳 Quần đảo Pitcairn",poland:"🇵🇱 Ba Lan",portugal:"🇵🇹 Bồ Đào Nha",posterSize:"5x7 inch",preview_link:"Xem trang web chính thức",printSize:"Cỡ in ấn",privacy_policy:"Chính sách quyền riêng tư",puerto_rico:"🇵🇷 Puerto Rico",purchase:"Mua",px:"px",qatar:"🇶🇦 Qatar",reset:"Đặt lại",results:"Kết quả",revert:"Lật ngang",romania:"🇷🇴 Romania",runion:"🇷🇪 Réunion",russia:"🇷🇺 Nga",rwanda:"🇷🇼 Rwanda",samoa:"🇼🇸 Samoa",san_marino:"🇸🇲 San Marino",saudi_arabia:"🇸🇦 Ả Rập Saudi",save:"Lưu",save_failed:"Không thể lưu",save_success:"Đã lưu",search:"Tìm kiếm",search_for_a_country_or_region:"Tìm kiếm quốc gia hoặc khu vực",senegal:"🇸🇳 Senegal",serbia:"🇷🇸 Serbia",seychelles:"🇸🇨 Seychelles",share:"Chia sẻ",shootingTips:"Mẹo chụp ảnh",sierra_leone:"🇸🇱 Sierra Leone",singapore:"🇸🇬 Singapore",size:"Kích cỡ",skin_retouch:"Da",slovakia:"🇸🇰 Slovakia",slovenia:"🇸🇮 Slovenia",so_tom_prncipe:"🇸🇹 São Tomé và Príncipe",solomon_islands:"🇸🇧 Quần đảo Solomon",somalia:"🇸🇴 Somalia",south_africa:"🇿🇦 Nam Phi",south_america:"Châu Mỹ Nam",south_korea:"🇰🇷 Hàn Quốc",south_sudan:"🇸🇸 Nam Sudan",spain:"🇪🇸 Tây Ban Nha",specialNotes:"Lưu ý đặc biệt",specifications:"Chi tiết kỹ thuật",sri_lanka:"🇱🇰 Sri Lanka",st_kitts_nevis:"🇰🇳 Saint Kitts và Nevis",st_lucia:"🇱🇨 Saint Lucia",st_pierre_miquelon:"🇵🇲 Saint Pierre và Miquelon",st_vincent_grenadines:"🇻🇨 Saint Vincent và Grenadines",sudan:"🇸🇩 Sudan",suriname:"🇸🇷 Suriname",svalbard_jan_mayen:"🇸🇯 Svalbard và Jan Mayen",sweden:"🇸🇪 Thụy Điển",switzerland:"🇨🇭 Thụy Sĩ",syria:"🇸🇾 Syria",taiwan:"🇹🇼 Đài Loan",tajikistan:"🇹🇯 Tajikistan",tanzania:"🇹🇿 Tanzania",taskExplore:"Khám phá",thailand:"🇹🇭 Thái Lan",timorleste:"🇹🇱 Đông Timor",togo:"🇹🇬 Togo",tokelau:"🇹🇰 Tokelau",tonga:"🇹🇴 Tonga",total:"Tổng",trinidad_tobago:"🇹🇹 Trinidad và Tobago",try_again:"Thử lại",tunisia:"🇹🇳 Tunisia",turkey:"🇹🇷 Thổ Nhĩ Kỳ",turkmenistan:"🇹🇲 Turkmenistan",turks_caicos_islands:"🇹🇨 Quần đảo Turks và Caicos",tuvalu:"🇹🇻 Tuvalu",uganda:"🇺🇬 Uganda",ukraine:"🇺🇦 Ukraine",unit:"Bản sao",united_arab_emirates:"🇦🇪 Các Tiểu vương quốc Ả Rập Thống nhất",united_kingdom:"🇬🇧 Vương quốc Anh",united_states:"🇺🇸 Hoa Kỳ",update:"Cập nhật",updateCancelButton:"Để sau",updateComfirmButton:"Cập nhật ngay",updateMessage:'Nội dung mới đã sẵn sàng! Nhấn vào "Cập nhật ngay" để tải các tài nguyên mới nhất.',uruguay:"🇺🇾 Uruguay",us_virgin_islands:"🇻🇮 Quần đảo Virgin thuộc Mỹ",uzbekistan:"🇺🇿 Uzbekistan",vanuatu:"🇻🇺 Vanuatu",venezuela:"🇻🇪 Venezuela",vietnam:"🇻🇳 Việt Nam",wallis_futuna:"🇼🇫 Wallis và Futuna",whiteEdgeTips:"Vui lòng cung cấp ảnh chân dung với phạm vi rộng hơn",width:"Chiều rộng",width_range_should_be_within_10120:"Phạm vi chiều rộng phải từ {min}~{max}.",yemen:"🇾🇪 Yemen",yes:"Có",zambia:"🇿🇲 Zambia",zimbabwe:"🇿🇼 Zimbabwe",agreement:"Sử dụng chức năng này yêu cầu bạn phải biết và đồng ý với {termsOfService} và {privacyPolicy}.",more:"Thêm",homeCaption:"Powered by",termsOfService:"Điều khoản dịch vụ của SnapID",history:"Lịch sử"},et={de:ho,en:fo,es:bo,fr:yo,id:ko,ja:vo,ko:wo,pt:So,ru:To,th:xo,tr:Co,vi:Po,"zh-Hant":{'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.':"製作AI服裝可能會因為大量的請求而<span>需要一些時間</span>。",'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.':"使用AI製作的服裝來製作正式文件可能會提升「提交被拒絕的風險」。建議先查看官方網站上的詳細要求。",IDPhoto:"證件照",JPPosterSize:"89x127mm","Keep waiting":"繼續等待","Server is busy.":"伺服器忙碌中。","Stop generating":"停止製作",THPosterSize:"4x6英吋",adjustTips:"請將臉部對準邊界進行調整。",adjust_tips:"申請官方文檔時，請確保照片不是左右顛倒或鏡像。",adjust_title:"調整位置",afghanistan:"🇦🇫 阿富汗",africa:"非洲",agree:"同意",agreeTOS:'繼續表示您同意<a href="https://snapid.ai/terms-of-service">《服務條款》</a>和<a href="https://snapid.ai/privacy-policy">《隱私權政策》</a>。',albania:"🇦🇱 阿爾巴尼亞",albumPermissionContent:"請確保已開啟相機及照片使用權限",albumPermissionTitle:"需要使用權限",algeria:"🇩🇿 阿爾及利亞",almost_there_just_1_second_to_go:"快到了！只剩下1秒了",american_samoa:"🇦🇸 美屬薩摩亞",andorra:"🇦🇩 安道爾",angola:"🇦🇴 安哥拉",anguilla:"🇦🇮 安圭拉",antigua_barbuda:"🇦🇬 安提瓜和巴布達",argentina:"🇦🇷 阿根廷",armenia:"🇦🇲 亞美尼亞",asia:"亞洲",australia:"🇦🇺 澳洲",austria:"🇦🇹 奧地利",azerbaijan:"🇦🇿 亞塞拜疆",background:"背景",background_updated:"背景已更新",bahamas:"🇧🇸 巴哈馬",bahrain:"🇧🇭 巴林",bangladesh:"🇧🇩 孟加拉",bangs:"瀏海",barbados:"🇧🇧 巴巴多斯",belarus:"🇧🇾 白俄羅斯",belgium:"🇧🇪 比利時",belize:"🇧🇿 伯利茲",benin:"🇧🇯 貝寧",bermuda:"🇧🇲 百慕大",bhutan:"🇧🇹 不丹",bolivia:"🇧🇴 玻利維亞",bosnia_herzegovina:"🇧🇦 波斯尼亞和黑塞哥維那",botswana:"🇧🇼 波札那",brazil:"🇧🇷 巴西",british_virgin_islands:"🇻🇬 英屬維京群島",brunei:"🇧🇳 汶萊",bulgaria:"🇧🇬 保加利亞",burkina_faso:"🇧🇫 布吉納法索",burundi:"🇧🇮 蒲隆地",cabo_verde:"🇨🇻 維德角",cambodia:"🇰🇭 柬埔寨",cameroon:"🇨🇲 喀麥隆",canada:"🇨🇦 加拿大",cancel:"取消",cancelGeneratorTips:"確定要停止製作嗎？",central_african_republic:"🇨🇫 中非共和國",chad:"🇹🇩 查德",chile:"🇨🇱 智利",china:"🇨🇳 中國",christmas_island:"🇨🇽 聖誕島",close:"關閉",closer:"較近",cocos_keeling_islands:"🇨🇨 科科斯（基林）群島",colombia:"🇨🇴 哥倫比亞",colorChange:"顏色變更",comoros:"🇰🇲 科摩羅",confirm:"確認",congo_brazzaville:"🇨🇬 剛果（布）",congo_kinshasa:"🇨🇩 剛果（金）",cook_islands:"🇨🇰 庫克群島",costa_rica:"🇨🇷 哥斯達黎加",croatia:"🇭🇷 克羅地亞",cte_divoire:"🇨🇮 象牙海岸",cuba:"🇨🇺 古巴",custom:"自訂",custom_size:"自訂大小",cyprus:"🇨🇾 塞浦路斯",czech_republic:"🇨🇿 捷克共和國",delete:"刪除",denmark:"🇩🇰 丹麥",digitalPhoto:"電子照片",digitalPhotoTips:"列印拼貼照片",djibouti:"🇩🇯 吉布地",dominica:"🇩🇲 多米尼克",dominican_republic:"🇩🇴 多明尼加共和國",ecuador:"🇪🇨 厄瓜多",edit_title:"編輯",egypt:"🇪🇬 埃及",el_salvador:"🇸🇻 薩爾瓦多",equatorial_guinea:"🇬🇶 赤道幾內亞",eritrea:"🇪🇷 厄立特里亞",error1:"圖片處理失敗",error2:"無法偵測到臉部",error3:"目前僅支援單人圖片",estonia:"🇪🇪 愛沙尼亞",eswatini:"🇸🇿 史瓦濟蘭",ethiopia:"🇪🇹 衣索比亞",europe:"歐洲",exitFirstMenuTips:"確定要在不儲存的情況下退出嗎？",failed:"製作失敗",failed_to_generate:"無法製作。",falkland_islands:"🇫🇰 福克蘭群島",faroe_islands:"🇫🇴 法羅群島",farther:"較遠",female:"女性",fiji:"🇫🇯 斐濟",finland:"🇫🇮 芬蘭",france:"🇫🇷 法國",french_polynesia:"🇵🇫 法屬波利尼西亞",gabon:"🇬🇦 加彭",gambia:"🇬🇲 甘比亞",generateFailed:"製作失敗",generate_id_photo_desc:`1. 光線柔和均勻<br/>
 2. 請勿過度修片或擦除臉部特徵`,georgia:"🇬🇪 喬治亞",germany:"🇩🇪 德國",getStarted:"立即開始",ghana:"🇬🇭 迦納",gibraltar:"🇬🇮 直布羅陀",gotIt:"明白了",greece:"🇬🇷 希臘",greenland:"🇬🇱 格陵蘭",grenada:"🇬🇩 格瑞那達",guam:"🇬🇺 關島",guatemala:"🇬🇹 瓜地馬拉",guernsey:"🇬🇬 根西島",guinea:"🇬🇳 幾內亞",guineabissau:"🇬🇼 幾內亞比索",guyana:"🇬🇾 圭亞那",haiti:"🇭🇹 海地",head_position:"頭部位置",height:"高度",height_range_should_be_within_10120:"高度範圍應在 {min} ~ {max}之間。",holy_see_vatican_city:"🇻🇦 梵蒂岡城（聖座）",homeTitle:"證件照",honduras:"🇭🇳 宏都拉斯",hong_kong_sar_china:"🇭🇰 香港特別行政區",hungary:"🇭🇺 匈牙利",iceland:"🇮🇸 冰島",idPhotoTip1:"1. 請別人在1至2公尺外幫您拍照。",idPhotoTip2:"2. 找一張純白或淺灰的背景。",idPhotoTip3:"3. 請摘下所有首飾。把頭髮往後梳。",idPhotoTip4:"4. 整理瀏海和衣服，不可遮住臉部。",idPhotoTip5:"5. 眼睛張開，嘴巴閉上，表情自然。",idPhotoTip6:"6. 不可有標記、眼鏡反射或「紅眼」。",idTaskEmpty:"這裡空空的！<br />快去製作證件照吧~",inch:"英吋",india:"🇮🇳 印度",indonesia:"🇮🇩 印尼",iran:"🇮🇷 伊朗",iraq:"🇮🇶 伊拉克",ireland:"🇮🇪 愛爾蘭",isle_of_man:"🇮🇲 馬恩島",israel:"🇮🇱 以色列",italy:"🇮🇹 意大利",jamaica:"🇯🇲 牙買加",japan:"🇯🇵 日本",jersey:"🇯🇪 澤西島",jordan:"🇯🇴 約旦",kazakhstan:"🇰🇿 哈薩克斯坦",kenya:"🇰🇪 肯亞",kiribati:"🇰🇮 基里巴斯",kuwait:"🇰🇼 科威特",kyrgyzstan:"🇰🇬 吉爾吉斯斯坦",laos:"🇱🇦 老撾",later:"下次再說",latvia:"🇱🇻 拉脫維亞",lebanon:"🇱🇧 黎巴嫩",lesotho:"🇱🇸 萊索托",liberia:"🇱🇷 賴比瑞亞",libya:"🇱🇾 利比亞",liechtenstein:"🇱🇮 列支敦士登",lithuania:"🇱🇹 立陶宛",loading:"載入",loadingText1:"您的請求已經在路上了。",loadingText2:"請保持應用程式開啟，以便快速完成。",loadingText3:"快到了！只剩下{time}秒了",loadingText4:"為您的AI服裝進行縫製",loadingText5:"比去照相館快得多！",loadingText6:"再稍微等一下，非常感謝耐心等候。",loadingText7:"偉大的成果是值得等待的！",loadingText8:"正在擦亮 AI 製作的服裝，快完成了！",loadingText9:"感謝耐心等候，我們將盡快達成要求。",loginSaveTip1:"製作記錄將存放<span>7天</span>，請盡快儲存。",luxembourg:"🇱🇺 盧森堡",macau_sar_china:"🇲🇴 澳門特別行政區",madagascar:"🇲🇬 馬達加斯加",malawi:"🇲🇼 馬拉威",malaysia:"🇲🇾 馬來西亞",maldives:"🇲🇻 馬爾代夫",male:"男性",mali:"🇲🇱 馬利",malta:"🇲🇹 馬耳他",marshall_islands:"🇲🇭 馬紹爾群島",mauritania:"🇲🇷 茅利塔尼亞",mauritius:"🇲🇺 模里西斯",mayotte:"🇾🇹 馬約特",medium:"中等",mexico:"🇲🇽 墨西哥",micronesia:"🇫🇲 密克羅尼西亞",mm:"mm",moldova:"🇲🇩 摩爾多瓦",monaco:"🇲🇨 摩納哥",mongolia:"🇲🇳 蒙古",montenegro:"🇲🇪 蒙特內哥羅",montserrat:"🇲🇸 蒙特塞拉特",moreColors:"更多顏色",mozambique:"🇲🇿 莫三比克",myanmar_burma:"🇲🇲 緬甸（緬甸）",namibia:"🇳🇦 納米比亞",nauru:"🇳🇷 諾魯",nepal:"🇳🇵 尼泊爾",netherlands:"🇳🇱 荷蘭",network_error:"網路已斷線",network_error_info:"網路異常",network_error_message:"請檢查網路設定。",network_retry:"請重試",new_caledonia:"🇳🇨 新喀里多尼亞",new_zealand:"🇳🇿 紐西蘭",next:"下一步",nicaragua:"🇳🇮 尼加拉瓜",niger:"🇳🇪 尼日爾",nigeria:"🇳🇬 奈及利亞",niue:"🇳🇺 紐埃",no:"不可以",no_results_found_please_try_a_different_keyword_or_customize_your_own_size:"找不到檔案。請改用其他關鍵字，或自訂尺寸。",no_search_results_found_please_try_a_different_keyword:"查無結果。請改用其他關鍵字。",norfolk_island:"🇳🇫 諾福克島",north_america:"北美洲",north_korea:"🇰🇵 北韓",north_macedonia:"🇲🇰 北馬其頓",northern_mariana_islands:"🇲🇵 北馬里安納群島",norway:"🇳🇴 挪威",notice:"通知",oceania:"大洋洲",ok:"確認",oman:"🇴🇲 阿曼",outfit:"套裝",pakistan:"🇵🇰 巴基斯坦",palau:"🇵🇼 帛琉",palestinian_territories:"🇵🇸 巴勒斯坦領土",panama:"🇵🇦 巴拿馬",papua_new_guinea:"🇵🇬 巴布亞紐幾內亞",paraguay:"🇵🇾 巴拉圭",payment:"付款",paymentFailure:"付款失敗！",peru:"🇵🇪 秘魯",philippines:"🇵🇭 菲律賓",photoResize:"相片調整大小",photo_size:"相片尺寸",pitcairn_islands:"🇵🇳 皮特凱恩群島",poland:"🇵🇱 波蘭",portugal:"🇵🇹 葡萄牙",posterSize:"5x7英吋",preview_link:"訪問官網",printSize:"沖洗尺寸",privacy_policy:"隱私原則",puerto_rico:"🇵🇷 波多黎各",purchase:"付款",px:"px",qatar:"🇶🇦 卡塔爾",reset:"重新設定",results:"結果",revert:"水平翻轉",romania:"🇷🇴 羅馬尼亞",runion:"🇷🇪 留尼旺",russia:"🇷🇺 俄羅斯",rwanda:"🇷🇼 盧安達",samoa:"🇼🇸 薩摩亞",san_marino:"🇸🇲 聖馬力諾",saudi_arabia:"🇸🇦 沙特阿拉伯",save:"儲存",save_failed:"儲存失敗",save_success:"儲存成功",search:"搜尋",search_for_a_country_or_region:"搜尋國家與地區",senegal:"🇸🇳 塞內加爾",serbia:"🇷🇸 塞爾維亞",settings:"設定",seychelles:"🇸🇨 塞席爾",share:"分享",shootingTips:"拍攝建議",sierra_leone:"🇸🇱 獅子山",singapore:"🇸🇬 新加坡",size:"尺寸",skin_retouch:"肌膚",slovakia:"🇸🇰 斯洛伐克",slovenia:"🇸🇮 斯洛文尼亞",so_tom_prncipe:"🇸🇹 聖多美普林西比",solomon_islands:"🇸🇧 所羅門群島",somalia:"🇸🇴 索馬利亞",south_africa:"🇿🇦 南非",south_america:"南美洲",south_korea:"🇰🇷 南韓",south_sudan:"🇸🇸 南蘇丹",spain:"🇪🇸 西班牙",specialNotes:"溫馨提醒",specifications:"規格",sri_lanka:"🇱🇰 斯里蘭卡",st_kitts_nevis:"🇰🇳 聖基茨和尼維斯",st_lucia:"🇱🇨 聖露西亞",st_pierre_miquelon:"🇵🇲 聖皮埃爾和密克隆",st_vincent_grenadines:"🇻🇨 聖文森特和格林納丁斯",sudan:"🇸🇩 蘇丹",suriname:"🇸🇷 蘇利南",svalbard_jan_mayen:"🇸🇯 斯瓦爾巴特和揚馬延",sweden:"🇸🇪 瑞典",switzerland:"🇨🇭 瑞士",syria:"🇸🇾 敘利亞",taiwan:"🇹🇼 台灣",tajikistan:"🇹🇯 塔吉克斯坦",tanzania:"🇹🇿 坦尚尼亞",taskExplore:"探索",thailand:"🇹🇭 泰國",timorleste:"🇹🇱 東帝汶",togo:"🇹🇬 多哥",tokelau:"🇹🇰 托克勞",tonga:"🇹🇴 湯加",total:"全部",trinidad_tobago:"🇹🇹 千里達及托巴哥",try_again:"再試一次",tunisia:"🇹🇳 突尼西亞",turkey:"🇹🇷 土耳其",turkmenistan:"🇹🇲 土庫曼斯坦",turks_caicos_islands:"🇹🇨 特克斯和凱科斯群島",tuvalu:"🇹🇻 圖瓦盧",uganda:"🇺🇬 烏干達",ukraine:"🇺🇦 烏克蘭",unit:"張",united_arab_emirates:"🇦🇪 阿拉伯聯合酋長國",united_kingdom:"🇬🇧 英國",united_states:"🇺🇸 美國",update:"更新",updateCancelButton:"下次再說",updateComfirmButton:"立即更新",updateMessage:"上架啦~點選「立即更新」即可載入最新資源。",uruguay:"🇺🇾 烏拉圭",us_virgin_islands:"🇻🇮 美屬維京群島",uzbekistan:"🇺🇿 烏茲別克斯坦",vanuatu:"🇻🇺 萬那杜",venezuela:"🇻🇪 委內瑞拉",vietnam:"🇻🇳 越南",wallis_futuna:"🇼🇫 瓦利斯與富圖納群島",whiteEdgeTips:"請提供更廣範圍的人像照片",width:"寬度",width_range_should_be_within_10120:"寬度應在 {min} ~ {max}之間。",yemen:"🇾🇪 也門",yes:"確定",zambia:"🇿🇲 尚比亞",zimbabwe:"🇿🇼 辛巴威",agreement:"使用此功能需要了解並同意《{termsOfService}》與《{privacyPolicy}》。",more:"更多尺寸",homeCaption:"Powered by",termsOfService:"SnapID服務條款",history:"製作記錄"}},zo="ERR_NETWORK",jo={timeout:3e4,validateResponse(A){if(A.data&&typeof A.data.code=="number"){if(A.data.code!==0&&A.data.code!==29901&&A.data.code!==100001)throw new $a(A.data.message,String(A.data.code),A.config,A.request,A);A.data=A.data.data}else throw new $a("Server error","ERR_BAD_RESPONSE",A.config,A.request,A)},validateStatus(A){return A>0},errorMessages:{ETIMEDOUT:"Request timeout",ERR_NETWORK:"Unstable network connection",ECONNABORTED:"Request canceled"}};function yt(A){const a=Object.assign({},jo,A),e=De.create(a);return e.interceptors.response.use(i=>{var n,s;return(s=(n=i.config).validateResponse)==null||s.call(n,i),i},i=>{var s,r,m,u;const n=i.code||"UNKOWN";throw i.code=String(n),i.message=((r=(s=i.config)==null?void 0:s.errorMessages)==null?void 0:r[n])||((m=e.defaults.errorMessages)==null?void 0:m[n])||((u=i.config)==null?void 0:u.failMessage)||i.message,i}),e.Cancel=De.Cancel,e.CanceledError=De.CanceledError,e.CancelToken=De.CancelToken,e.isCancel=De.isCancel,e.isError=ha,e.creatError=ja,e}De.CancelToken;De.isCancel;const ha=De.isAxiosError,at=A=>ha(A)&&(A.code==="ERR_NETWORK"||A.code==="ETIMEDOUT");function ja(A,a,e,i,n){return new $a(A,a,e,i,n)}function tt(){return ja("Network error",zo)}function it(A){return Math.round(Number(A)*100)/100}function Bo(A){const a=new Date(A*1e3);return new Intl.DateTimeFormat(window.lang,{year:"numeric",month:"long",day:"numeric"}).format(a)}function XA(A,a){ia.getState().connected&&(a==null?void 0:a.force)!==!0?A():WA.getState().present({closeable:!1,title:sA("network_error"),message:sA("network_error_message"),actions:[{type:"primary",title:sA("network_retry"),handler(){setTimeout(()=>{XA(A)},300)}},{title:sA("later"),handler(){var e;(e=a==null?void 0:a.onCancel)==null||e.call(a)}}]})}function wi(A){[109,110].includes(Se())?IA.openURLSchema({url:`airbrush://p_webview?url=${encodeURIComponent(A)}`}):IA.openURLSchema({url:`beautyplus://webview?url=${encodeURIComponent(A)}`})}function Mo(A){return new Promise((a,e)=>{const i=new Image;i.src=A,i.onload=a,i.onerror=e})}function ze(A){return A.substring(A.indexOf(",")+1)}function Si(A){const a=document.createElement("canvas");a.width=A.naturalWidth,a.height=A.naturalHeight;const e=a.getContext("2d");if(!e)throw new Error("Can't get 2d context");return e.drawImage(A,0,0,A.naturalWidth,A.naturalHeight),a.toDataURL("image/jpeg")}async function Io(A,a){const e=await te(A),i=document.createElement("canvas");i.width=e.naturalWidth,i.height=e.naturalHeight;const n=i.getContext("2d");if(!n)throw new Error("Can't get 2d context");return n.fillStyle=a,n.fillRect(0,0,i.width,i.height),n.drawImage(e,0,0,e.naturalWidth,e.naturalHeight),i.toDataURL("image/jpg")}const Ti=(A,a=0,e=0,i,n,s,r)=>new Promise(m=>{const u=new Image;u.crossOrigin="anonymous",u.onload=()=>{const b=document.createElement("canvas"),y=b.getContext("2d");b.width=u.width,b.height=u.height,y.clearRect(0,0,u.width,u.height),i||(i=u.width),n||(n=u.height);let h;!s||!r?(y.drawImage(u,a,e,i,n),h=y.getImageData(0,0,i,n)):(y.drawImage(u,a,e,i,n,0,0,s,r),h=y.getImageData(0,0,s,r)),m(h)},u.onerror=()=>{},u.src=A}),te=A=>new Promise((a,e)=>{const i=new Image;i.crossOrigin="anonymous",i.onload=()=>{a(i)},i.onerror=n=>{e(n)},i.src=A}),Bt=async A=>{const a=await Ti(A),e=[];for(let i=3;i<a.data.length;i+=4)e.push(a.data[i]);return e},sa=async(A,a)=>{const e=await Ti(A);for(let s=3,r=0;s<e.data.length;s+=4,r++)e.data[s]=a[r];const i=document.createElement("canvas");return i.width=e.width,i.height=e.height,i.getContext("2d").putImageData(e,0,0),i.toDataURL("image/png",1)},ra=(A,a,e,i,n,s,r)=>{r=r||{width:i,height:n,unit:"mm"},A.textAlign="left",A.fillStyle="#666666",A.font="bold 16px sans-serif",A.textBaseline="top",A.beginPath(),A.textAlign="center";const m=70*Math.ceil(`${r.width}${r.unit}`.length*100/4)/100,u=70*Math.ceil(`${r.height}${r.unit}`.length*100/4)/100;switch(s){case"left":A.moveTo(a-16,e),A.lineTo(a-16,e+(n*10-u)/2),A.moveTo(a-16-6,e),A.lineTo(a-16+6,e),A.moveTo(a-16,e+(n*10-u)/2+u),A.lineTo(a-16,e+(n*10-u)/2+u+(n*10-u)/2),A.moveTo(a-16-6,e+(n*10-u)/2+u+(n*10-u)/2),A.lineTo(a-16+6,e+(n*10-u)/2+u+(n*10-u)/2),A.save(),A.translate(a-16-8,e+n*10/2),A.rotate(-90*Math.PI/180),A.fillText(`${r.height}${r.unit}`,0,0),A.restore();break;case"bottom":A.moveTo(a,e+n*10+16),A.lineTo(a+(i*10-m)/2,e+n*10+16),A.moveTo(a,e+n*10+16-6),A.lineTo(a,e+n*10+16+6),A.moveTo(a+(i*10-m)/2+m,e+n*10+16),A.lineTo(a+(i*10-m)/2+m+(i*10-m)/2,e+n*10+16),A.moveTo(a+(i*10-m)/2+m+(i*10-m)/2,e+n*10+16-6),A.lineTo(a+(i*10-m)/2+m+(i*10-m)/2,e+n*10+16+6),A.fillText(`${r.width}${r.unit}`,a+i*10/2,e+n*10+16-10);break;case"right":A.moveTo(a+i*10+16,e),A.lineTo(a+i*10+16,e+(n*10-u)/2),A.moveTo(a+i*10+16-6,e),A.lineTo(a+i*10+16+6,e),A.moveTo(a+i*10+16,e+(n*10-u)/2+u),A.lineTo(a+i*10+16,e+(n*10-u)/2+u+(n*10-u)/2),A.moveTo(a+i*10+16-6,e+(n*10-u)/2+u+(n*10-u)/2),A.lineTo(a+i*10+16+6,e+(n*10-u)/2+u+(n*10-u)/2),A.save(),A.translate(a+i*10+16+8,e+n*10/2),A.rotate(90*Math.PI/180),A.fillText(`${r.height}${r.unit}`,0,0),A.restore();break;case"top":default:A.moveTo(a,e-16),A.lineTo(a+(i*10-m)/2,e-16),A.moveTo(a,e-16-6),A.lineTo(a,e-16+6),A.moveTo(a+(i*10-m)/2+m,e-16),A.lineTo(a+(i*10-m)/2+m+(i*10-m)/2,e-16),A.moveTo(a+(i*10-m)/2+m+(i*10-m)/2,e-16-6),A.lineTo(a+(i*10-m)/2+m+(i*10-m)/2,e-16+6),A.fillText(`${r.width}${r.unit}`,a+i*10/2,e-16-10);break}A.lineWidth=2,A.strokeStyle="#666666",A.lineCap="round",A.stroke()},Ze=Object.assign({"../assets/images/IDPhoto/bangs/active.png":so,"../assets/images/IDPhoto/bangs/index.ts":mo,"../assets/images/IDPhoto/bangs/model1.jpg":co,"../assets/images/IDPhoto/bangs/model2.jpg":lo,"../assets/images/IDPhoto/bangs/model3.jpg":uo,"../assets/images/IDPhoto/bangs/model4.jpg":_o,"../assets/images/IDPhoto/bangs/normal.png":ro,"../assets/images/IDPhoto/bangs/normal.svg":po});Ze["../assets/images/IDPhoto/bangs/normal.svg"].default,Ze["../assets/images/IDPhoto/bangs/model1.jpg"].default,Ze["../assets/images/IDPhoto/bangs/model2.jpg"].default,Ze["../assets/images/IDPhoto/bangs/model3.jpg"].default,Ze["../assets/images/IDPhoto/bangs/model4.jpg"].default;const xi=A=>new Promise((a,e)=>{fetch(A).then(i=>i.blob()).then(i=>{const n=new FileReader;n.readAsDataURL(i),n.onerror=()=>e(tt()),n.onloadend=()=>{const s=n.result;a(s)}}).catch(()=>e(tt()))}),Ba=A=>{const a=$i({locale:window.lang,messages:et[window.lang]}),{width:e,height:i,unit:n="mm"}=A;if(!e||!i)return"";let s="";const r=a.formatMessage({id:n});switch(n){case kA.inch:s=`${Math.round(e*100/25.4)/100} x ${Math.round(i*100/25.4)/100}${r}`;break;case kA.px:s=`${Math.round(e*10)} x ${Math.round(i*10)}${r}`;break;case kA.mm:default:s=`${Math.round(e)} x ${Math.round(i)}${r}`;break}return console.log("res",s),s};function fa(A){const a={"٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9"};let e=A.replace(/[\u0660-\u0669]/g,i=>a[i]);return(A.includes("٫")||A.includes(","))&&(e=e.replace(/(,|，)/g,".")),e}yt({timeout:30*1e3,validateResponse(A){if(A.status!==200)throw ja("Server error","ERR_BAD_RESPONSE",A.config,A.request,A)}});const ta=yt({baseURL:"https://openapi.mtlab.meitu.com/",timeout:30*1e3,validateResponse:A=>{if(A.status!==200)throw ja("Server error","ERR_BAD_RESPONSE",A.config,A.request,A);return!0}});ta.interceptors.request.use(A=>(A.params={api_key:"2edbcf550aa54575906eebbad6f98f9d",api_secret:"4d9abcdf56d64bb7897ff84e04b50a02"},A.data={media_info_list:[{media_data:ze(A.source),media_profiles:{media_data_type:A.source.includes("https://")?"url":"jpg"}}],parameter:{rsp_media_type:"url",...A.parameter}},A),A=>Promise.reject(A));ta.interceptors.response.use(A=>{var a,e,i,n;return((e=(a=A.data)==null?void 0:a.media_info_list[0])==null?void 0:e.media_data)||((n=(i=A.data)==null?void 0:i.media_info_list[0])==null?void 0:n.media_extra)},A=>{var a,e;switch((e=(a=A==null?void 0:A.response)==null?void 0:a.data)==null?void 0:e.ErrorCode){case 20003:case 20004:case 20010:return Promise.reject(20003);case 20011:case 21102:case 21103:break;case 20012:case 21100:case 21101:break;default:case 20008:break}return Promise.reject(A)});var Eo={VITE_APP_BASE_API:"https://openapi.mtlab.meitu.com/",VITE_APP_API_KEY:"2edbcf550aa54575906eebbad6f98f9d",VITE_APP_API_SECRET:"4d9abcdf56d64bb7897ff84e04b50a02",BASE_URL:"/undefinedminiapp.snapid.ai/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const Be=yt({timeout:30*1e3}),Mt={0:"https://api.beta.snapid.ai/api",1:"https://api.snapid.ai/api"};Be.interceptors.request.use(async A=>{var i,n,s;const a=ne.getState().data,e=Eo.VITE_MOCK==="true"?"/snapid/api":Mt[(i=a==null?void 0:a.environment)!=null?i:1]||Mt[1];return A.url=`${e}${A.url}`,A.headers["X-App-Id"]=(n=a==null?void 0:a.hostAppID)!=null?n:"",A.headers["X-Gid"]=(s=a==null?void 0:a.gid)!=null?s:"",A});const Ma=FA(Ta((A,a)=>({country:"",setCountry(e){A({country:e})},async initiate(){try{await Ma.persist.rehydrate()}catch(e){}}}),{name:"snapid.id_photo.region",storage:xa(za),skipHydration:!0}));function No(){return Ma.getState().country||Na()}function Ia(){return Ma(A=>[A.country||Na(),A.setCountry])}const Ci=FA(A=>({setIntl(a){A({current:a})}}));function Ro(){const A=nA();return o.useEffect(()=>{Ci.getState().setIntl(A)},[A]),null}function sA(A,a){var e,i;return(i=(e=Ci.getState().current)==null?void 0:e.formatMessage({id:A},a))!=null?i:""}const Ea=FA((A,a)=>({loading:null,photoSize:[],initiate(){const e=new Promise(async(i,n)=>{var s,r;try{const m=await Be.get("/v1/id_photo/list",{params:{lang:nt(),country:No(),group_by:"classify",platform:"mobile",phase:(s=ne.getState().data)==null?void 0:s.preReleasePassword}}),u=nt(),b=((r=m.data)==null?void 0:r.filter(y=>y.classify_key).map(y=>{var h;return{key:y.classify_key,name:y.classify,data:((h=y==null?void 0:y.data)==null?void 0:h.filter(_=>_.id&&_.slug&&_.count&&_.parameter&&_.parameter.size&&_.parameter.ratio).map(({count:_,parameter:f,link:v,...c})=>{var L;const{size:d,inch:l,doubleCheck:g,lineBottom:p,lineHeight:k,lineTop:S,ratio:P,previewLink:x}=f,[C,T]=_,[B,w]=d,I=l!=null&&l.length&&l.length>1?` ${l==null?void 0:l.join("x")}${sA("inch")}`:"",E=`${C*T} ${sA("unit")}`;return{key:c.slug,name:c.name,type:c.type,desc:"",size:d,inch:l,sizeString:`${B}x${w}${sA("mm")}${I} | ${E}`,sizeDisplay:`${B}x${w}${sA("mm")}${I?` | ${I}`:""}`,sizeValue:d.join("*"),count:_,lineBottom:p,lineHeight:k,lineTop:S,ratio:P,link:v,previewLink:x,specialNotes:c.special_notes||"",doubleCheck:g?{...g,descList:(L=g==null?void 0:g.descList)==null?void 0:L.map(z=>(z==null?void 0:z[u])||(z==null?void 0:z.default)||(z==null?void 0:z.en)||"")}:void 0}}))||[]}}))||[];A({photoSize:b}),i()}catch(m){n(m)}finally{A({loading:null})}});return A({loading:e}),e},async initiateIfNeed(){const{loading:e,photoSize:i,initiate:n}=a();i.length>0||(e?await e:await n())}})),Do=()=>{const{photoSize:A}=Ea();return A},ia=FA((A,a)=>({connected:!0,connectionType:"unknown",async initiate(){try{const e=await ut.getStatus();A(e)}catch(e){}return a().connected}}));ut.addListener("networkStatusChange",A=>{ia.setState(A)});const na=FA((A,a)=>({loading:null,inited:!1,balance:0,vouchers:[],async load(){const{loading:e}=a();if(e)return e;const i=Be.get("/v1/asset/balance").then(n=>(A({balance:0,vouchers:[],...n.data,loading:null,inited:!0}),a())).catch(n=>(A({loading:null}),Promise.reject(n)));return A({loading:i}),i},async initIfNeed(e=!1){if(e&&A({inited:!1}),!a().inited)try{a().load()}catch(i){}},async refresh(){return a().load()}}));function Pi(A=na.getState()){const{vouchers:a}=A;return a.reduce((e,i)=>(e.push(i.id),e),[])}function we(A=na.getState()){const{vouchers:a}=A;return a.reduce((e,i)=>e+i.quantity,0)}function Lo(){return na(we)}function ba(){return na.getState().refresh()}const zi=A=>Be("/v1/ai/pre_record_upload",{method:"POST",data:{type:12,module:2,...A}}),kt=A=>Be("/v1/ai/payment_completed",{method:"POST",data:A}),Oo=()=>Be("/v1/ai/history",{method:"GET",params:{page:1,page_size:9999,type:12,module:2}}),qe=FA(Ta((A,a)=>({empty:!0,loading:!1,list:[],async initiate(){try{A({loading:!0});const{data:e}=await Oo(),i=(e==null?void 0:e.list)||[];A({empty:i.length===0,list:i.map(n=>({...n.ext,createdTime:n.created_time,updatedTime:n.updated_time,id:n.msg_id})),loading:!1})}catch(e){A({error:e,loading:!1})}},async initiateIfNeed(){a().list.length===0&&await a().initiate()}}),{name:"snapid.paper.history",storage:xa(za),partialize:A=>({empty:A.empty})})),ya=(A,a="url")=>ta({url:"/v1/matting_beautyplus",method:"POST",parameter:{nMask:!1,nResize:"INTER_AREA",rsp_media_type:a},source:A}),Ka=(A,a,e="url")=>ta({url:"/v2/bangs",method:"POST",parameter:{image_type:"0",bangs_type:a,rsp_media_type:e},source:A}).then(i=>i.indexOf("http")!==-1?xi(i):i),ca=(A,a,e="url")=>a===0||!a?Promise.resolve(""):ta({url:"/v1/mtdlbeautyplus",method:"POST",parameter:{FaceBeautyAlpha:parseInt((a*80).toString(),10),FaceRestoreAlpha:0,DenoiseSkinIntensityCoef:parseInt((a*100).toString(),10),DenoiseIntensityCoef:0,SuperResolutionAlpha:parseInt((a*50).toString(),10),WrinkleForeheadRemovalAlpha:parseInt((a*90).toString(),10),WrinklePeriorbitalRemovalAlpha:parseInt((a*90).toString(),10),WrinkleNasolabialRemovalAlpha:parseInt((a*90).toString(),10),PandaEyeCleanAlpha:parseInt((a*70).toString(),10),WrinkleNeckRemovalAlpha:parseInt((a*90).toString(),10),WrinkleCheekRemovalAlpha:parseInt((a*90).toString(),10),FaceBalanceAlpha:parseInt((a*80).toString(),10),SkinBalanceAlpha:parseInt((a*100).toString(),10),EnhanceCoef:0,DehazeCoef:0,AWBNormCoef:0,ExposureNormCoef:0,FaceTextureAlpha:parseInt((a*60).toString(),10),rsp_media_type:e,PhotoEffectFlag:1,BrightLowDarkImageFlag:0,HdrCorrectionAlpha:0,ColorNoiseAlpha:0,FleckCleanFlag:0,SkinFleckCleanFlag:0,BlackHeadCleanFlag:0,SkinBlackCleanFlag:0},source:A});async function Go({base64:A,gender:a,width:e,height:i,style:n}){const s=await Io(A,"#ffffff"),r=Wo(),{url:m}=await _t.upload({type:1,suffix:"jpg",data:ze(s),storageType:0});if(!m)throw new Error("Failed to upload.");const{data:u}=await Be({url:"/v1/ai/batch_generate",method:"POST",data:{type:15,module:1,urls:[m],parameter:{gender:a,height:i,width:e},styles:[{goods_id:r,style:n}]}}),[{msg_id:b}]=u||[{msg_id:""}];return b}const Fo=A=>A?Be({url:"/v1/ai/cancel",method:"POST",data:{msg_id:A}}):Promise.resolve(),Uo=A=>{let a,e=!1;const i=35,n=async()=>{var h,_,f,v,c,d;try{let l="";const{data:g}=await Be({url:"/v1/ai/history",method:"GET",params:{msg_id:A,type:15}});if(e)return Promise.reject(new Error("stop"));const p=((h=g==null?void 0:g.list)==null?void 0:h[0])||{status:-1,output_urls:[]};if(p&&p.status===0){if(l=((_=p.output_urls)==null?void 0:_[0])||"",!l)throw new Error(sA("generateFailed"));if(l=await xi(l),e)return Promise.reject(new Error("stop"));if(!l)throw new Error(sA("generateFailed"));return{source:l,remainTime:(f=p==null?void 0:p.ext)!=null&&f.remaining_time&&((v=p==null?void 0:p.ext)!=null&&v.current_time)?p.ext.remaining_time-p.ext.current_time:i}}else if(p&&p.status!==1)throw new Error(sA("generateFailed"));return{source:l,remainTime:(c=p==null?void 0:p.ext)!=null&&c.remaining_time&&((d=p==null?void 0:p.ext)!=null&&d.current_time)?p.ext.remaining_time-p.ext.current_time:i}}catch(l){throw l}};let s,r;const m=new Promise((h,_)=>{s=h,r=_});let u;const b=()=>{a&&clearTimeout(a),e||(e=!0,r==null||r(new Error("stop")),u==null||u(new Error("stop")))};return{result:new Promise((h,_)=>{u=_;let f=10,v=0;const c=(l=!1)=>{a=setTimeout(async()=>{if(!e)if(v<f){v++;try{const g=(await n()).source;if(e)return;g?h(g):c()}catch(g){if(e)return;at(g)?XA(()=>{c(!0)},{force:!0,onCancel:b}):_(g)}}else WA.getState().present({message:sA("Server is busy."),actions:[{title:sA("Keep waiting"),type:"primary",handler:async()=>{WA.getState().dismiss(),setTimeout(()=>{XA(()=>{v=0,c(!0)},{onCancel(){b()}})},100)}},{title:sA("Stop generating"),type:"secondary",handler(){WA.getState().dismiss(),b()}}]})},l?0:1e3*3)},d=async()=>{var l;try{const g=await n();if(e)return;s==null||s(g);const{source:p}=g;p?h(p):a=setTimeout(()=>{c(!0)},Math.min(i,Math.max((l=g.remainTime)!=null?l:i,0))*1e3)}catch(g){if(e)return;at(g)?XA(d,{force:!0,onCancel:b}):_(g)}};d()}),time:m,clear:b}},vt=FA(Ta((A,a)=>({orders:[],add(e,i){A(n=>({orders:[...n.orders,{id:e,task:i}]}))},delete(e){A(i=>({orders:i.orders.filter(n=>n.id!==e)}))},async restore(){const{orders:e}=a();if(e.length>0)try{await Promise.allSettled(e.slice().map(async i=>{await kt({task_id:i.task,aw_trans_id:i.id}),a().delete(i.id)}))}finally{qe.getState().initiate()}}}),{name:"snapid.payment.restore",storage:xa(za)})),da="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/icon_loading-EOR-tx9u.png",ua=FA((A,a)=>({cached:{},load(e){a().cached[e]||Mo(e).then(()=>{A(i=>({...i,cached:{...i.cached,[e]:!0}}))}).catch(()=>{})},initiate(){[da].forEach(a().load)}}));let It=!1;function Ho(){if(!It){It=!0;try{IA.loadFinish().catch(()=>{})}catch(A){}}}const ne=FA((A,a)=>({loading:!0,async initiate(){var e,i;try{A({loading:!0});const n=await IA.getInfo();n.language==="tw-Hant"&&(n.language="zh-Hant");const s=zn.includes(n.language)?n.language:"en";if(n.language=s,window.lang=s,A({data:n}),n.statusBarHeight>0){const r=n.statusBarHeight>90?n.statusBarHeight/window.devicePixelRatio:n.statusBarHeight;(e=document.documentElement.style)==null||e.setProperty("--ion-safe-area-top",`${r}px`)}if(n.mode===0){const r=(i=window.console)==null?void 0:i.log,m=[];window.console&&(window.console.log=function(...b){m.push(b)},window.console.log("result","AppPlugin.getInfo",n));const u=document.createElement("script");u.type="text/javascript",u.src="https://unpkg.com/eruda@3.0.1/eruda.js",u.onload=function(){var b;window.console&&(window.console.log=r,(b=window.eruda)==null||b.init(),m.forEach(y=>{window.console.log(...y)}))},document.body.appendChild(u)}if(!await ia.getState().initiate())throw tt();A({error:void 0,loading:!1,data:n}),a().initiateAsync()}catch(n){A({error:n,loading:!1})}finally{Ho()}},async initiateAsync(){try{await Ma.getState().initiate(),na.getState().initIfNeed(),Ea.getState().initiate(),qe.getState().initiateIfNeed(),vt.getState().restore(),ua.getState().initiate()}catch(e){}}})),Ko=()=>ne(A=>[A.data,A.error,A.loading,A.initiate],mt);function ji(){const[A,a]=ne(e=>[e.loading,e.error],mt);return!A&&!a}function qo(){const A=ne(a=>{var e,i;return(i=(e=a.data)==null?void 0:e.country)!=null?i:""});return jn.includes(A)}function ka(){var A,a;return Number((a=(A=ne.getState().data)==null?void 0:A.systemType)!=null?a:0)}function Qo(){var A,a;return(a=(A=ne.getState().data)==null?void 0:A.systemVersion)!=null?a:""}function Se(){var A,a;return Number((a=(A=ne.getState().data)==null?void 0:A.hostAppID)!=null?a:"0")}function uA(){return Se()===103||Se()===104||Se()===92}function mA(){return Se()===109||Se()===110}function Vo(){var A,a;return(a=(A=ne.getState().data)==null?void 0:A.mode)!=null?a:1}function Na(){var A,a;return(a=(A=ne.getState().data)==null?void 0:A.country)!=null?a:""}function nt(){var A,a;return(a=(A=ne.getState().data)==null?void 0:A.language)!=null?a:"en"}const WA=FA(A=>({async present(a){A({current:a})},dismiss(){A({current:void 0})}})),Jo=()=>WA(A=>A.current),wt=()=>WA(A=>[A.present,A.dismiss],mt),NA=FA((A,a)=>({width:30,height:40,photoName:"",photoType:0,photoPosition:0,sizeItem:void 0,originImg:"",effectImg:"",alphaImg0:"",alphaImg100:"",adjustImg:"",maskImg:"",poster:null,sizeDisplay:"",reversal:!1,activeMakeIndex:{color:0,bangs:0,alpha:0},alphaChannel:[],moreColors:[],moreColorPhotos:[],moreSizePhotos:[],moreReadySizePhotos:[],moreSizes:[],idName:"",sex:2,unit:"mm",analynicData:{background:0,beauty:0,clothing:0,bang:0},setSex(e){A({sex:e})},setIdName(e){A({idName:e})},setSizeDisplay(e){A({sizeDisplay:e})},setMoreColors(e){A({moreColors:e})},setMoreColorPhotos(e){A({moreColorPhotos:e})},setMoreSizePhotos(e){A({moreSizePhotos:e})},setMoreReadySizePhotos(e){A({moreReadySizePhotos:e})},setMoreSizes(e){A({moreSizes:e})},setWidth(e){A({width:e})},setHeight(e){A({height:e})},setPhotoName(e){A({photoName:e})},setPhotoType(e){A({photoType:e})},setPhotoPosition(e){A({photoPosition:e})},setSizeItem(e){A({sizeItem:{...a().sizeItem,...e}})},setOriginImg(e){A({originImg:e})},setEffectImg(e){A({effectImg:e})},setAlpha0Img(e){A({alphaImg0:e})},setAlpha100Img(e){A({alphaImg100:e})},setAdjustImg(e){A({adjustImg:e})},setMaskImg(e){A({maskImg:e})},setPoster(e){A({poster:e})},setReversal(e){A({reversal:e})},setActiveMakeIndex(e){A({activeMakeIndex:e})},setAnalynicData(e){A({analynicData:{...a().analynicData,...e}})},setAlphaChannel(e){A({alphaChannel:e})},setUnit(e){A({unit:e})},async initiate(){A({width:30,height:40,photoName:"",photoType:0,sizeItem:void 0,originImg:"https://gcs.beautyplus.com/d24ab345052659dc78bc73ab690bf18a.png",effectImg:"",alphaImg0:"",alphaImg100:"",adjustImg:"",maskImg:"",poster:null,reversal:!1,activeMakeIndex:{color:0,bangs:0,alpha:0},alphaChannel:[]})}})),ot=FA(A=>({image:null,mattedImage:"",setImage:a=>A({image:a}),setMattedImage:a=>A({mattedImage:a}),reset:()=>A({image:null,mattedImage:""})})),st=FA(A=>({image:null,mattedImage:"",setImage:a=>A({image:a}),reset:()=>A({image:null})})),Et=["ECONNABORTED","ERR_CANCELED","11306","11308","30002","20004","20002"],Ra=FA(A=>({async present(a){A({current:{type:"text",...a}})},dismiss(){A({current:void 0})}})),Bi=()=>Ra(A=>A.current),jA=(()=>{const{present:A,dismiss:a}=Ra.getState();return{show:A,hide(){a()},loading(e){A({...typeof e=="string"?{message:e}:e,type:"loading"})},text(e){if(ha(e)){const{code:i}=e;if(i&&Et.includes(i)){jA.hide();return}A({type:"text",message:e.message})}else e instanceof Error?A({type:"text",message:e.message}):A({type:"text",message:e})},info(e){A({type:"info",message:e})},warn(e){A({type:"warn",message:e})},error(e){if(ha(e)){const{code:i}=e;if(i&&Et.includes(i)){jA.hide();return}A({type:"error",message:e.message})}else e instanceof Error?A({type:"error",message:e.message}):A({type:"error",message:e})},success(e){A({type:"success",message:e})}}})(),Me=()=>jA;function Mi(A,a){return FA((e,i)=>({data:new Map,async initiate(){const n=typeof A=="function"?A():A,s=Array.isArray(n)?n:[n],{products:r=[]}=await aa.request({skus:s});if(r.length<s.length)throw new Error(sA("network_error_info"));(await Promise.all(r.map((u,b)=>{var y;return aa.formatPrice({price:(y=u.price)!=null?y:0}).then(h=>h.price).catch(()=>{var h,_;return(_=r[b].localPrice)!=null?_:`${u.localSymbol}${it((h=u.price)!=null?h:0)}`})}))).forEach((u,b)=>{r[b].localPrice=u});const m=r.reduce((u,b)=>(u.set(b.productIdentifier,b),u),new Map);if(e({data:m}),a){const u=await a(s,m);e({extra:u})}},async initiateIfNeed(){i().data.size===0&&await i().initiate()}}))}function va(){const A=Se();return A===103?10094:A===104||A===109?10096:A===110?10097:A===92?11e3:0}function Wo(){const A=Se();return A===103?11007:A===104?11008:A===109?11009:A===110?11010:A===92?11001:0}function Ii(){const A=Na(),a=Se();return a===103?"beautyplus.consum.IDphoto.ver2":a===104?ma.includes(A)?"beautyplus.consum.idphoto.ver7":ga.includes(A)?"beautyplus.consum.idphoto.ver8":"beautyplus.consum.idphoto.ver6":a===109?"airbrush.consum.idphoto.ver2":a===110?ma.includes(A)?"airbrush.consum.idphoto.ver7":ga.includes(A)?"airbrush.consum.idphoto.ver8":"airbrush.consum.idphoto.ver6":a===92?"beautypluscam.consum.idphoto.ver8":""}const St=Mi(Ii);function Yo(){return St(A=>A.data).get(Ii())}function Ei(){const A=Na(),a=Se();return a===103?["beautyplus.consum.IDphoto.ver0","beautyplus.consum.IDphoto.ver1"]:a===104?ma.includes(A)?["beautyplus.consum.idphoto.ver1","beautyplus.consum.idphoto.ver4"]:ga.includes(A)?["beautyplus.consum.idphoto.ver2","beautyplus.consum.idphoto.ver5"]:["beautyplus.consum.idphoto.ver0","beautyplus.consum.idphoto.ver3"]:a===109?["airbrush.consum.idphoto.ver0","airbrush.consum.idphoto.ver1"]:a===110?ma.includes(A)?["airbrush.consum.idphoto.ver1","airbrush.consum.idphoto.ver4"]:ga.includes(A)?["airbrush.consum.idphoto.ver2","airbrush.consum.idphoto.ver5"]:["airbrush.consum.idphoto.ver0","airbrush.consum.idphoto.ver3"]:a===92?["beautypluscam.consum.idphoto.ver2","beautypluscam.consum.idphoto.ver5"]:["",""]}const Tt=Mi(Ei,async(A,a)=>{var e,i;if(A.length>=2&&a.size>=2){const n=a.get(A[0]),s=a.get(A[1]);if(n&&s){const r=((e=s.price)!=null?e:0)-((i=n.price)!=null?i:0);return aa.formatPrice({price:r}).then(m=>m.price).catch(()=>{var m,u;return n.localPrice.indexOf(n.localSymbol)>0?`${it(r)}${(m=n.localSymbol)!=null?m:""}`:`${(u=n.localSymbol)!=null?u:""}${it(r)}`})}}return""});function Zo(){return Ei()}function Xo(){return Tt(A=>A.data)}const $o=540,Nt=A=>A.shadowRoot||A,As=(A,a)=>{var e;try{const i="cubic-bezier(0.32,0.72,0,1)",n="opacity",s="transform",r="0%",u=A.ownerDocument.dir==="rtl",b=u?"-99.5%":"99.5%",y=u?"33%":"-33%",{enteringEl:h,leavingEl:_}=a,f=a.direction==="back",v=Fa(h),c=ke(),d=ke();if(c.addElement(h).duration(((e=a.duration)!=null?e:0)||$o).easing(a.easing||i).fill("both").beforeRemoveClass("ion-page-invisible"),_&&A!==null&&A!==void 0){const l=ke();l.addElement(A),c.addAnimation(l)}if(v?d.addElement(v):d.addElement(h.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),c.addAnimation(d),f?d.beforeClearStyles([n]).fromTo("transform",`translateX(${y})`,`translateX(${r})`).fromTo(n,.8,1):d.beforeClearStyles([n]).fromTo("transform",`translateX(${b})`,`translateX(${r})`),v){const l=Nt(v).querySelector(".transition-effect");if(l){const g=l.querySelector(".transition-cover"),p=l.querySelector(".transition-shadow"),k=ke(),S=ke(),P=ke();k.addElement(l).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),S.addElement(g).beforeClearStyles([n]).fromTo(n,0,.1),P.addElement(p).beforeClearStyles([n]).fromTo(n,.03,.7),k.addAnimation([S,P]),d.addAnimation([k])}}if(_){const l=ke(),g=Fa(_);if(g?l.addElement(g):l.addElement(_.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs")),c.addAnimation(l),f){l.beforeClearStyles([n]).fromTo("transform",`translateX(${r})`,u?"translateX(-100%)":"translateX(100%)");const p=Fa(_);c.afterAddWrite(()=>{c.getDirection()==="normal"&&p.style.setProperty("display","none")})}else l.fromTo("transform",`translateX(${r})`,`translateX(${y})`).fromTo(n,1,.8);if(g){const p=Nt(g).querySelector(".transition-effect");if(p){const k=p.querySelector(".transition-cover"),S=p.querySelector(".transition-shadow"),P=ke(),x=ke(),C=ke();P.addElement(p).beforeStyles({opacity:"1",display:"block"}).afterStyles({opacity:"",display:""}),x.addElement(k).beforeClearStyles([n]).fromTo(n,.1,0),C.addElement(S).beforeClearStyles([n]).fromTo(n,.7,.03),P.addAnimation([x,C]),l.addAnimation([P])}}}return c}catch(i){throw i}};class es extends Event{constructor(e,i){super("appnavigate",i);Ye(this,"url");Ye(this,"action","push");this.url=typeof e=="string"?e:`${e.pathname}`,i!=null&&i.action&&(this.action=i==null?void 0:i.action)}}class rt extends Event{constructor(e){super("appnavigationdidenter");Ye(this,"id");this.id=e}}window.AppNavigateEvent=es;window.AppNavigationDidEnterEvent=rt;const xt=o.createContext({id:-1,url:"",pathname:"",search:""});function Qe(){return o.useContext(xt)}function BA(A,a){const e=o.useContext(xt),i=o.useRef(e);i.current=e;const[n,s]=o.useState(!0);o.useEffect(()=>{const m=u=>{u.id===i.current.id?s(!1):s(!0)};return window.addEventListener("appnavigationdidenter",m),()=>{window.removeEventListener("appnavigationdidenter",m)}},[]);const r=o.useCallback(()=>{let m,u=setTimeout(()=>{u=void 0,m=A()},0);return()=>{m==null||m(),u&&clearTimeout(u)}},a);o.useEffect(()=>n?()=>{}:r(),[n,r])}function as({id:A,url:a,children:e}){const i=ji(),n=o.useMemo(()=>{const[r,m]=a.split("?");return{id:ct,url:a,pathname:r,search:m?`?${m}`:"",searchParams:m?new URLSearchParams(m):void 0}},[ct,a]),s=o.useMemo(()=>t.jsx(xt.Provider,{value:n,children:e}),[n,e]);return i?s:null}const Ni=o.createContext({getActive(){return Promise.resolve(void 0)},getByPathname(){return Promise.resolve(void 0)},canGoBack(){return Promise.resolve(!1)},goBack(){},pop(){},popTo(){},popToRoot(){},removeToRoot(A){},push(){},setSwipeBackEnabled(){}});function UA(){return o.useContext(Ni)}let ct=0;const Rt=(A,a,e)=>{var m;const i=e!=null?e:++ct,n=()=>t.jsx(as,{id:i,url:A,children:a.element},i),[s,r]=A.split("?");return n.id=i,n.url=A,n.pathname=s,n.search=r?`?${r}`:"",n.swipeGesture=(m=a.swipeGesture)!=null?m:!0,n},ts=o.forwardRef(({className:A,root:a,routes:e,children:i},n)=>{const s=o.useRef(null),[r,m]=o.useState(!0),u=o.useMemo(()=>e.reduce((d,l)=>(d[l.path]=l,d),{}),[e]),b=o.useMemo(()=>u[a],[u,a]),y=o.useCallback((d,l)=>{var k;const g=d.split("?")[0]||"",p=u[g];p&&((k=s.current)==null||k.push(Rt(d,p),{},{animationBuilder:As,...l}))},[u]),[h]=o.useState(()=>({getActive(){var d;return((d=s.current)==null?void 0:d.getActive())||Promise.resolve(void 0)},getByPathname(d){const{current:l}=s;if(l){const g=p=>{var k;return((k=p.component)==null?void 0:k.pathname)===d?Promise.resolve(p):l.getPrevious(p).then(S=>S?g(S):Promise.resolve(void 0))};return l==null?void 0:l.getActive().then(p=>{if(p)return g(p)})}return Promise.resolve(void 0)},canGoBack(){var d;return((d=s.current)==null?void 0:d.canGoBack())||Promise.resolve(!1)},goBack(){var d;(d=s.current)==null||d.pop()},pop(){var d;(d=s.current)==null||d.pop()},popTo(d){const{current:l}=s;if(l){const g=p=>l.getPrevious(p).then(k=>{var S;return k?((S=k.component)==null?void 0:S.pathname)===d?Promise.resolve(k):g(k):Promise.resolve(void 0)});l==null||l.getActive().then(p=>{p&&g(p).then(k=>{k&&l.popTo(k)})})}},popToRoot(){var d;(d=s.current)==null||d.popToRoot()},async removeToRoot(d){try{const{current:l}=s;if(l){const g=await l.getByIndex(0),p=d?await this.getByPathname(d):await l.getActive();if(!p)return;let k=0,S=await l.getPrevious(p);for(;S&&S!==g;){if(k+=1,k>100){k=0;break}S=await l.getPrevious(S)}k>0&&l.removeIndex(1,k)}}catch(l){}},push:y,setSwipeBackEnabled(d){m(d)}}));o.useImperativeHandle(n,()=>h,[h]),o.useEffect(()=>{const d=({action:l,url:g})=>{var p;l==="push"?y(g):l==="pop"&&((p=s.current)==null||p.pop())};return window.addEventListener("appnavigate",d),()=>{window.removeEventListener("appnavigate",d)}},[y]);const _=o.useMemo(()=>Rt(a,b,0),[a,b]),f=o.useCallback(d=>{},[]),v=ji(),c=o.useCallback(()=>{var d;(d=s==null?void 0:s.current)==null||d.getActive().then(l=>{var g,p,k,S;l&&(m((p=(g=l==null?void 0:l.component)==null?void 0:g.swipeGesture)!=null?p:!0),window.dispatchEvent(new rt((S=(k=l==null?void 0:l.component)==null?void 0:k.id)!=null?S:0)))})},[]);return o.useEffect(()=>{if(v){const d=setTimeout(()=>{var l;(l=s==null?void 0:s.current)==null||l.getActive().then(g=>{var p,k,S,P;g&&(m((k=(p=g==null?void 0:g.component)==null?void 0:p.swipeGesture)!=null?k:!0),window.dispatchEvent(new rt((P=(S=g==null?void 0:g.component)==null?void 0:S.id)!=null?P:0)))})},100);return()=>{clearTimeout(d)}}return()=>{}},[v]),o.useMemo(()=>t.jsxs(Ni.Provider,{value:h,children:[t.jsx(An,{ref:s,className:A,root:_,swipeGesture:r,onIonNavWillChange:f,onIonNavDidChange:c}),i]}),[h,_,r,A,i,f,c])});function fe(A){return o.memo(a=>t.jsx(Ss,{children:t.jsx(A,{...a})}))}function oe(){const A=o.useContext(lt),a=o.useRef(A);o.useEffect(()=>{a.current=A},[A]);const e=o.useCallback((i,n)=>{const s=nt(),r=uA()?{language:s==="zh-Hant"?"tw-Hant":s}:{};en.event({name:i,params:{...r,...a.current,...n}})},[]);return o.useMemo(()=>({event:e,get params(){return a.current}}),[e])}function He(A,a){return o.useCallback((...e)=>{XA(()=>{A(...e)})},[a])}function Ct(){const A=nA(),a=Me(),[e]=qt(),i=o.useCallback(()=>Qt.check({permission:"album",request:ka()===1}).then(n=>n.status===0?(e({header:A.formatMessage({id:"albumPermissionTitle"}),message:A.formatMessage({id:"albumPermissionContent"}),buttons:[{role:"cancel",text:A.formatMessage({id:"later"})},{role:"confirm",text:A.formatMessage({id:"settings"}),handler(){IA.gotoSetting()}}]}),Promise.reject(new Error("no album auth"))):Promise.resolve()),[e,A]);return He(async(n,s)=>{var m,u;try{await i()}catch(b){return}const r=A.formatMessage({id:"save_failed"});a.loading();try{await n(),a.success(A.formatMessage({id:"save_success"})),(m=s==null?void 0:s.onSuccess)==null||m.call(s)}catch(b){a.error(r),(u=s==null?void 0:s.onFail)==null||u.call(s)}},[A,a,i])}function VA(A){const a=o.useRef(A);return a.current=A,a}function Ri(){const A=UA(),a=()=>{const i=ZA.addListener("close",async n=>{var s,r;if(ZA.closeReceived().catch(()=>{}),n.from===0&&((r=(s=n.photos)==null?void 0:s[0])!=null&&r.source)){let m,u=new Promise((y,h)=>{m=h});const b=JA.addListener("cancel",()=>{m==null||m(new Error("cancel"))});try{JA.showLoading({message:"",timeout:1e4,cancelText:sA("cancel")});const y=n.photos[0],h=await Promise.race([je.fileToBase64({file:y.source}),u]);if(!h.data)throw new Error("fail");const _=`data:image/${h.suffix||"jpeg"};base64,${await Promise.race([await ya(`data:image/jpeg;base64,${h.data}`,"jpg"),u])}`,f=ot.getState();f.setImage(y),f.setMattedImage(_),A.push("/tools/recolor",{animated:!1}),setTimeout(()=>{JA.dismiss(),i.remove(),ZA.close({animationTo:"right"})},300)}catch(y){(y==null?void 0:y.message)==="cancel"?GA.resume():(JA.showText({message:sA("network_error_info")}),GA.resume())}finally{b.remove()}}else i.remove(),ZA.close({animationTo:"left"})});ZA.open({count:[1,1],maxWidth:1536,fd:{count:[1,1],fullFace:80,faceAngle:30},selfPortraits:!1,needControlBack:!0,report:{page_id:"album_page_view",project:"change_color"}})},e=async i=>{try{jA.loading();const n=await te(i),s=await Si(n),{file:r}=await je.base64ToFile({suffix:"jpeg",data:ze(s)}),m=`data:image/jpeg;base64,${await ya(s,"jpg")}`,u=ot.getState();u.setImage({width:n.naturalWidth,height:n.naturalHeight,source:r}),u.setMattedImage(m),jA.hide(),A.push("/tools/recolor")}catch(n){jA.error(sA("network_error_info"))}};return i=>{i?e(i):a()}}function is(A){const a=o.useRef([]),e=o.useCallback(n=>s=>{s&&(a.current[n]=s)},[]),i=o.useCallback(()=>{a.current.splice(0,a.current.length)},[]);return o.useEffect(()=>{typeof A=="number"&&a.current.splice(A,A-a.current.length)},[A]),[a.current,e,i]}function Di(){const A=UA(),a=()=>{const i=ZA.addListener("close",async n=>{var s,r;if(ZA.closeReceived().catch(()=>{}),n.from===0&&((r=(s=n.photos)==null?void 0:s[0])!=null&&r.source))try{JA.showLoading();const m=n.photos[0],u=await je.fileToBase64({file:m.source});if(!u.data)throw new Error("fail");st.getState().setImage({...m,source:`data:image/${u.suffix||"jpeg"};base64,${u.data}`}),A.push("/tools/resize",{animated:!1}),setTimeout(()=>{JA.dismiss(),i.remove(),ZA.close({animationTo:"right"})},300)}catch(m){JA.showText({message:sA("network_error_info")})}else i.remove(),ZA.close({animationTo:"left"})});ZA.open({count:[1,1],maxWidth:1536,fd:{count:[1,1],fullFace:80,faceAngle:30},selfPortraits:!1,needControlBack:!0,report:{page_id:"album_page_view",project:"change_size"}})},e=async i=>{try{jA.loading();const n=await te(i),s=await Si(n);st.getState().setImage({width:n.naturalWidth,height:n.naturalHeight,source:s}),jA.hide(),A.push("/tools/resize")}catch(n){jA.error(sA("network_error_info"))}};return i=>{i?e(i):a()}}function qa(){const A=o.useRef();return o.useEffect(()=>()=>{A.current&&clearTimeout(A.current)},[]),A}const ns=A=>{const a=VA(A);o.useEffect(()=>()=>{a.current()},[])},lt=o.createContext({}),Li=o.createContext({add(){},remove(){}}),os=o.forwardRef((A,a)=>{const[e,i]=o.useState(null);return o.useImperativeHandle(a,()=>({render:i}),[]),e});function ss({element:A}){const a=o.useId(),e=o.useContext(Li),i=o.useRef(A);i.current=A;const n=o.useRef();return o.useEffect(()=>{const s={key:a,ref:r=>{r(i.current),n.current=r}};return e.add(s),()=>{e.remove(s)}},[e,a]),o.useEffect(()=>{var s;(s=n.current)==null||s.call(n,A)},[A]),null}function rs({children:A}){const[a,e]=o.useState([]),i=o.useMemo(()=>({add(n){const s={...n,element:t.jsx(os,{ref:r=>{r&&n.ref(r.render)}},n.key)};e(r=>{const m=r.slice(0),u=r.findIndex(b=>b.key===n.key);return u<0?m.push(s):m.splice(u,1,s),m})},remove(n){e(s=>{const r=s.findIndex(m=>m.key===n.key);if(r>=0){const m=s.slice(0);return m.splice(r,1),m}return s})}}),[]);return t.jsxs(Li.Provider,{value:i,children:[A,a.map(n=>n.element)]})}const Oi=o.createContext({onHardwareBackPress(){return()=>{}}});function cs({goBack:A,children:a}){const e=VA(A),i=o.useMemo(()=>[],[]),n=o.useMemo(()=>({onHardwareBackPress(s){return i.unshift(s),()=>{const r=i.indexOf(s);r>=0&&i.splice(r,1)}}}),[i,e]);return o.useEffect(()=>{let s;return/android/i.test(navigator.userAgent)&&(IA.setHardwareBackable({enable:!1}),s=IA.addListener("back",()=>{for(let r=0;r<i.length;r++)if(i[r]())return;e.current().then(r=>{r||IA.exit()})})),()=>{s==null||s.remove()}},[e,i]),t.jsx(Oi.Provider,{value:n,children:a})}const ls="button.modal-handle{top:12px;width:32px;background-color:#ccc}";function oa({position:A,onClose:a,...e}){const i=o.useContext(lt),{isOpen:n,backdropDismiss:s}=e,[r,m]=o.useState(!0);o.useEffect(()=>{n&&m(!1)},[n]);const u=(...c)=>{var d;(d=e.onDidDismiss)==null||d.apply(null,c),m(!0)},b=VA(a),y=o.useContext(Oi);o.useEffect(()=>n?y.onHardwareBackPress(()=>{var c;return s!==!1&&((c=b.current)==null||c.call(b)),!0}):()=>{},[n,s,y]);const h=o.useRef(),_=(...c)=>{var d;if((d=e.onDidPresent)==null||d.apply(null,c),!h.current){const l=g=>{h.current&&(g.stopImmediatePropagation(),g.stopPropagation())};window.addEventListener("appnavigate",l,{capture:!0}),h.current=l}},f=(...c)=>{var d;n&&(a==null||a()),(d=e.onWillDismiss)==null||d.apply(null,c),h.current&&(window.removeEventListener("appnavigate",h.current,{capture:!0}),h.current=void 0)};o.useEffect(()=>{n||h.current&&(window.removeEventListener("appnavigate",h.current,{capture:!0}),h.current=void 0)},[n]),o.useEffect(()=>()=>{h.current&&(window.removeEventListener("appnavigate",h.current,{capture:!0}),h.current=void 0)},[]);const v=o.useRef(null);return o.useEffect(()=>{if(e.handle){let c=null,d=null;const l=setTimeout(()=>{if(v.current){c=v.current;const g=c.shadowRoot;g&&(d=document.createElement("style"),d.innerHTML=ls,g.appendChild(d))}},150);return()=>{var g;clearTimeout(l),c&&d&&((g=c.shadowRoot)==null||g.removeChild(d))}}return()=>{}},[e.handle]),r?null:t.jsx(ss,{element:t.jsx(lt.Provider,{value:i,children:t.jsx(an,{...e,ref:v,className:N(e.className,{"modal-sheet":A==="bottom"}),onDidPresent:_,onWillDismiss:f,onDidDismiss:u})})})}const ds="_alert_8pnpc_1",us="_alert__container_8pnpc_8",_s="_alert__container_closeable_8pnpc_11",ms="_alert__title_8pnpc_14",gs="_alert__message_8pnpc_21",ps="_alert__actions__horizontal_8pnpc_28",hs="_alert__action_8pnpc_28",fs="_alert__action_gradient_8pnpc_48",bs="_alert__action_primary_8pnpc_53",ys="_alert__action_delete_8pnpc_58",ks="_close_8pnpc_61",vs="_close__icon_8pnpc_73",YA={alert:ds,alert__container:us,alert__container_closeable:_s,alert__title:ms,alert__message:gs,alert__actions__horizontal:ps,alert__action:hs,alert__action_gradient:fs,alert__action_primary:bs,alert__action_delete:ys,close:ks,close__icon:vs};function Gi({open:A,closeable:a=!0,closeIcon:e,title:i,message:n,actions:s,onClose:r,onClosed:m,direction:u="vertical"}){const b=o.useCallback(()=>{m==null||m()},[m]),y=_=>{const f=_.shadowRoot,v=Ua().addElement(f==null?void 0:f.querySelector("ion-backdrop")).fromTo("opacity","0.01","var(--backdrop-opacity)"),c=Ua().addElement(f==null?void 0:f.querySelector(".modal-wrapper")).keyframes([{offset:0,opacity:"0.01",transform:"scale(1.1)"},{offset:1,opacity:"1",transform:"scale(1)"}]);return Ua().addElement(_).easing("ease-out").duration(200).addAnimation([v,c])},h=_=>y(_).direction("reverse");return t.jsx(oa,{className:YA.alert,isOpen:A,backdropDismiss:a,enterAnimation:y,leaveAnimation:h,onClose:r,onDidDismiss:b,children:t.jsxs("div",{className:N(YA.alert__container,{[YA.alert__container_closeable]:!!e}),children:[i?t.jsx("div",{className:YA.alert__title,children:i}):null,n?t.jsx("div",{className:YA.alert__message,children:n}):null,s?t.jsx("div",{className:u==="horizontal"?YA.alert__actions__horizontal:YA.alert__actions,children:s.map((_,f)=>t.jsx("div",{className:N(YA.alert__action,{[YA.alert__action_gradient]:_.type==="gradient",[YA.alert__action_primary]:_.type==="primary",[YA.alert__action_sencondary]:_.type==="secondary",[YA.alert__action_delete]:_.type==="delete"},_.className),onClick:()=>{var c;const v=(c=_.handler)==null?void 0:c.call(_);v instanceof Promise?v.finally(()=>{r==null||r()}):r==null||r()},children:_.title},f))}):null," ",e?t.jsx("div",{className:YA.close,onClick:()=>r==null?void 0:r(),children:t.jsx("img",{className:YA.close__icon,src:typeof e=="string"?e:Cn})}):null]})})}const ws="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAAIVBMVEUAAAAzMzM0NDQ0NDQyMjI0NDQyMjIzMzMzMzM1NTUzMzM/f46oAAAACnRSTlMAzxBA34B/v59gONVwoQAAAHBJREFUSMdjGAWDAqg5JhBUwyS1ajlBRYqrVq0yIGgQUNEEggaBTSJs0ApiDBIiwqCFCqMGjRpEkUEMxBjERoxBjEQYxMBCjCJWqHWUhwCD4qhRo0YNqFHLCdc/FFVAmFUZYaNWMBAErCJBDKNgAAEASqiLQdjr9yAAAAAASUVORK5CYII=",Pt=o.createContext(null);function Ss({children:A}){const[a,e]=o.useState(()=>({content:null,onContentChange:i=>{e(n=>i&&i!==n.content?{...n,content:i}:n)}}));return o.useMemo(()=>t.jsx(Pt.Provider,{value:a,children:A}),[a,A])}const Ts="_header_d2y4k_1",xs="_toolbar_d2y4k_7",Cs="_icon_d2y4k_12",Ps="_icon_translucent_d2y4k_19",zs="_title_d2y4k_25",js="_title_large_d2y4k_33",Bs="_buttons_d2y4k_38",Re={header:Ts,toolbar:xs,icon:Cs,icon_translucent:Ps,title:zs,title_large:js,buttons:Bs};function se({className:A,backable:a=!1,backIcon:e,title:i,leftButtons:n,rightButtons:s,translucent:r=!1,clickToTop:m=!1,toolbarClassName:u,backIconClassName:b,onBack:y,...h}){const _=o.useContext(Pt),f=o.useMemo(()=>_==null?void 0:_.content,[_]),v=o.useRef(),c=UA(),[d,l]=o.useState(r?0:1),[g,p]=o.useState(!1),k=o.useMemo(()=>`rgba(255, 255, 255, ${d})`,[d]);o.useEffect(()=>{const P=setTimeout(()=>{var C;const x=(C=v.current)==null?void 0:C.shadowRoot;if(x){const T=x.querySelector(".toolbar-container");T&&T.style&&(T.style.contain="none",T.style.overflow="visible")}},0);return()=>{clearTimeout(P)}},[]);const S=o.useCallback(()=>{m&&f&&f.scrollToTop(300)},[m,f]);return t.jsx(tn,{...h,className:N(Re.header,{[Re.header_translucent]:r},A),"data-hidden":g,style:{"--ion-toolbar-background":k},onClick:S,children:t.jsxs(nn,{ref:v,className:N(Re.toolbar,u),children:[t.jsx(Ha,{slot:"start",children:a?t.jsx("div",{onClick:P=>{P.stopPropagation(),y?y():c.goBack()},children:t.jsx("img",{className:N(Re.icon,{[Re.icon_translucent]:r&&d<.6},b),src:e||ws})}):null}),n?t.jsx(Ha,{slot:"secondary",className:Re.buttons,children:n==null?void 0:n.map((P,x)=>{if(o.isValidElement(P))return P;const{icon:C,title:T,...B}=P;let w=null;return C&&(typeof C=="string"?w=t.jsx(jt,{slot:"end",icon:C}):w=C),t.jsxs(At,{...B,children:[w,T]},`app_bar_right_btn_${x}`)})}):null,i&&t.jsx(on,{className:Re.title,children:i}),t.jsx(Ha,{slot:"end",className:Re.buttons,children:s==null?void 0:s.map((P,x)=>{if(o.isValidElement(P))return P;const{icon:C,title:T,...B}=P;let w=null;return C&&(typeof C=="string"?w=t.jsx(jt,{slot:"end",icon:C}):w=C),t.jsxs(At,{...B,children:[w,T]},`app_bar_right_btn_${x}`)})})]})})}function Ms({time:A=300,children:a}){const[e,i]=o.useState(!1);return o.useEffect(()=>{const n=setTimeout(()=>{i(!0)},A);return()=>{clearTimeout(n)}},[A]),e?t.jsx(t.Fragment,{children:a}):null}function Is({className:A,...a}){return t.jsx("svg",{...a,className:N(A),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 64",fill:"none",width:"1em",height:"1em",children:t.jsxs("g",{id:"all_img_warning_no_content",children:[t.jsx("path",{id:"Rectangle 405",d:"M48.2918 26H15.7082C13.4356 26 11.358 27.284 10.3416 29.3168L5.63342 38.7334C5.21686 39.5665 5 40.4852 5 41.4166V53.0002C5 56.314 7.68629 59.0002 11 59.0002H53C56.3137 59.0002 59 56.314 59 53.0002V41.4166C59 40.4852 58.7831 39.5665 58.3666 38.7334L53.6583 29.3168C52.642 27.284 50.5644 26 48.2918 26Z",stroke:"currentColor",strokeWidth:"2.5",strokeLinejoin:"round"}),t.jsx("path",{id:"Vector",d:"M5 40H21C22.6569 40 23.9467 41.3895 24.554 42.931C25.7234 45.8993 28.6164 48 32 48C35.3837 48 38.2767 45.8993 39.4461 42.931C40.0533 41.3895 41.3432 40 43 40H59",stroke:"currentColor",strokeWidth:"2.5"}),t.jsx("path",{id:"Vector_2",d:"M32 3.66699V13.0003",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{id:"Vector_3",d:"M15.5039 10.5029L22.1072 17.1063",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{id:"Vector_4",d:"M41.8926 17.1063L48.4959 10.5029",stroke:"currentColor",strokeWidth:"2.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})}function Es({className:A,...a}){return t.jsxs("svg",{...a,className:N(A),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",width:"1em",height:"1em",children:[t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",fill:"transparent",strokeWidth:"6",d:"M93.75 38.987c-14.82-14.045-34.74-19.6-53.594-16.663M80.625 53.935a43.105 43.105 0 0 0-20.781-11.558M19.376 53.936a43.345 43.345 0 0 1 9.535-7.216M32.5 68.186a24.634 24.634 0 0 1 10.938-6.367"}),t.jsx("path",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",d:"M50 85.001a5.469 5.469 0 1 0 0-10.938 5.469 5.469 0 0 0 0 10.938Z"}),t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"6",d:"m84.999 84.999-70-70M6.25 38.986a64.162 64.162 0 0 1 7.656-6.212"})]})}const Ns="_loader_1sna1_1",Rs="_loader_top_1sna1_8",Ds="_loader_fullscreen_1sna1_12",Ls="_darkMode_1sna1_20",Os="_icon_empty_1sna1_23",Gs="_icon_offline_1sna1_24",Fs="_icon_loading_1sna1_25",Us="_title_1sna1_28",Hs="_content_1sna1_31",Ks="_description_1sna1_62",qs="_actions_1sna1_67",Qs="_action_1sna1_67",QA={loader:Ns,loader_top:Rs,loader_fullscreen:Ds,darkMode:Ls,icon_empty:Os,icon_offline:Gs,icon_loading:Fs,title:Us,content:Hs,description:Ks,actions:qs,action:Qs},Vs={loading:{icon:t.jsx(sn,{className:QA.icon_loading,name:"circular"}),title:"loading..."},error:{title:"Oops!",description:"Sorry, something went wrong. Please check your connection and try again later. If the issue persists, contact our support team. Thanks!",actions:[{type:"load",title:"Refresh"}]},empty:{icon:t.jsx(Is,{className:N(QA.icon,QA.icon_empty)}),title:"No data yet",actions:[{type:"load",title:"Refresh"}]},offline:{icon:t.jsx(Es,{className:N(QA.icon,QA.icon_offline)}),title:"Not connected to the internet.",description:"Please check your connection and tap Refresh.",actions:[{title:"Settings",onClick:()=>{IA.gotoSetting()}},{type:"load",title:"Refresh"}]}},Js=A=>({...Vs[A.status],...A});function wa(A){const{className:a,fullscreen:e,delay:i=300,position:n,status:s,icon:r,title:m,description:u,actions:b,onLoad:y,darkMode:h,renderTitle:_,...f}=Js(A),v=t.jsx("div",{...f,className:N(QA.loader,a,{[QA.loader_fullscreen]:e,[QA.loader_top]:n==="top",[QA.loader_center]:n==="center",[QA.darkMode]:h}),children:t.jsxs("div",{className:QA.content,children:[r,!_&&m?t.jsx("div",{className:QA.title,children:m}):null,_&&_(),u?t.jsx("div",{className:QA.description,children:u}):null,b&&b.length>0?t.jsx("div",{className:QA.actions,children:b==null?void 0:b.map((c,d)=>{let l=c.onClick;return l||c.type==="load"&&(l=y),t.jsx(At,{className:QA.action,onClick:l,children:c.title},d)})}):null]})});return i>0?t.jsx(Ms,{time:i,children:v}):v}class Ws extends o.PureComponent{constructor(e){super(e);Ye(this,"handleLoad",()=>{this.setState({hasError:!1})});this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,i){var n,s;(s=(n=window.Sentry)==null?void 0:n.captureException)==null||s.call(n,e)}render(){return this.state.hasError?t.jsx(wa,{fullscreen:!0,status:"error",onLoad:this.handleLoad}):this.props.children}}function Ys(){var s,r,m;const A=Jo(),[a,e]=o.useState(A),[,i]=wt();o.useEffect(()=>{A&&e(A)},[A]);const n=o.useMemo(()=>!!A&&!!a,[A,a]);return t.jsx(Gi,{...a,direction:(s=a==null?void 0:a.direction)!=null?s:"vertical",closeable:(r=a==null?void 0:a.closeable)!=null?r:!1,closeIcon:(m=a==null?void 0:a.closeIcon)!=null?m:!1,open:n,onClose:()=>{i()},onClosed:()=>{e(void 0)}})}const Zs="_loading_qjfqy_1",Xs="_loading_progress_qjfqy_4",$s="_loading__content_qjfqy_4",Ar="_loading__spinner_qjfqy_8",er="_loading__icon_qjfqy_12",ar="_loading__icon_css_qjfqy_50",tr="_loading__progress_qjfqy_102",ir="_loading__message_qjfqy_111",nr="_loading__cancel_qjfqy_116",Pe={loading:Zs,loading_progress:Xs,loading__content:$s,loading__spinner:Ar,loading__icon:er,loading__icon_css:ar,"l20-1":"_l20-1_qjfqy_1","l20-2":"_l20-2_qjfqy_1",loading__progress:tr,loading__message:ir,loading__cancel:nr,"spinner-rotate":"_spinner-rotate_qjfqy_1"};function or(){const A=nA(),a=Bi(),{hide:e}=Me(),[i,n]=o.useState(a),s=o.useRef(i);s.current=i;const[r,m]=o.useState(!1),[u,b]=o.useState(-1);o.useEffect(()=>{let c;const{progress:d=-1,estimate:l=0,onProgress:g}=i||{};if(i&&d>=0&&l>0){b(d);const p=Date.now();c=setInterval(()=>{const k=Date.now(),S=d+Math.min(Math.floor((k-p)/l*(100-d)),99);g==null||g(S),b(S),S>=99&&(clearInterval(c),c=void 0)},500)}else b(-1);return()=>{c&&clearInterval(c)}},[i]);const y=o.useMemo(()=>!!a&&a.type==="loading"&&!!i,[a,i]),h=o.useRef({delayTimer:0,toast:a,loading:i}),_=o.useCallback(()=>{n(void 0)},[]);o.useEffect(()=>{!a&&h.current.delayTimer&&(clearTimeout(h.current.delayTimer),h.current.delayTimer=0),h.current.toast=a},[a]),o.useEffect(()=>{h.current.loading=i},[i]),o.useEffect(()=>{let c;if(a&&a.type==="loading"){const{delay:d=300}=a;d===0||h.current.loading?(h.current.delayTimer&&(clearTimeout(h.current.delayTimer),h.current.delayTimer=0),n(a)):h.current.delayTimer||(c=setTimeout(()=>{c=void 0,n(h.current.toast)},d))}return()=>{c&&clearTimeout(c)}},[a]),o.useEffect(()=>{const{current:c}=h;return()=>{const{delayTimer:d}=c;d&&(clearTimeout(d),c.delayTimer=0)}},[]),o.useEffect(()=>{var d,l;let c=0;return i?c=setTimeout(()=>{c=void 0,m(!0)},(l=(d=s.current)==null?void 0:d.timeout)!=null?l:1e4):m(!1),()=>{c&&clearTimeout(c)}},[i]);const[f,v]=o.useState(()=>!!ua.getState().cached[da]);return o.useInsertionEffect(()=>{y&&(ua.getState().load(da),v(!!ua.getState().cached[da]))},[y]),t.jsx(t.Fragment,{children:t.jsx(rn,{className:N(Pe.loading,{[Pe.loading_progress]:u>=0}),spinner:"crescent",animated:a&&a.type!=="loading"?!1:void 0,isOpen:y,onDidDismiss:_,children:t.jsxs("div",{className:N(Pe.loading__content),children:[t.jsxs("div",{className:N(Pe.loading__spinner),children:[t.jsx("div",{className:N(Pe.loading__icon,{[Pe.loading__icon_css]:!f})}),u>=0?t.jsx("div",{className:Pe.loading__progress,children:`${u}%`}):null]}),i!=null&&i.message?t.jsx("div",{className:Pe.loading__message,children:i==null?void 0:i.message}):null,y&&r&&(a==null?void 0:a.closeable)!==!1?t.jsx("div",{className:Pe.loading__cancel,onClick:()=>{var c;e(),(c=a==null?void 0:a.onClose)==null||c.call(a)},children:A.formatMessage({id:"close"})}):null]})})})}const sr="_toast_1sixt_1",rr="_toast_icon_1sixt_8",Dt={toast:sr,toast_icon:rr},cr="div.toast-wrapper{top:50%!important;box-sizing:border-box;-webkit-backdrop-filter:blur(1.5rem);backdrop-filter:blur(1.5rem);transform:translateY(-50%)}div.toast-container{flex-direction:column;justify-content:center}ion-icon.toast-icon{margin-left:0;-webkit-margin-start:0;margin-inline-start:0;margin-bottom:.5rem;font-size:4rem}div.toast-content{flex:0!important;max-width:7.5rem;padding:0;font-size:.875rem;line-height:1.0625rem;text-align:center;color:#fff}",lr="div.toast-wrapper{top:50%!important}div.toast-content{padding:8px 12px}div.toast-message{text-align:center}",dr={error:'data:image/svg+xml;utf8,<svg width="64" height="65" viewBox="0 0 64 65" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="32" cy="32.5" r="28" stroke="white" stroke-width="3"/><path d="M24 24.5L40.0001 40.5001" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 40.5L40.0001 24.4999" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',success:'data:image/svg+xml;utf8,<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="32" cy="32" r="28" stroke="white" stroke-width="3"/><path d="M19 31.3913L25.1099 39.221C26.6453 41.1886 29.5879 41.2851 31.2489 39.4222L45 24" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>'};function ur(){const A=o.useRef(null),a=Bi(),[e,i]=o.useState(a),n=e&&e.type==="loading",s=o.useMemo(()=>dr[(e==null?void 0:e.type)||""],[e==null?void 0:e.type]);return o.useEffect(()=>{i(a)},[a]),o.useEffect(()=>{var u;let r=null;const m=(u=A.current)==null?void 0:u.shadowRoot;return e&&m&&(r=document.createElement("style"),s?r.innerHTML=cr:r.innerHTML=lr,m.appendChild(r)),()=>{m&&r&&r.parentNode===(m!=null?m:null)&&m.removeChild(r)}},[e,s]),t.jsx(cn,{ref:A,animated:n?!1:void 0,duration:n?0:3e3,position:"middle",icon:s,message:e==null?void 0:e.message,className:N(Dt.toast,{[Dt.toast_icon]:!!s}),isOpen:!!e&&!n,onDidDismiss:()=>{i(void 0)}})}const _r="_image_lhacg_1",mr="_image__holder_lhacg_24",gr="_image_loading_lhacg_41",pr="_image_holder_lhacg_41",hr="_image_failed_lhacg_42",fr="_image_transition_lhacg_49",br="_image__watermark_lhacg_52",Ge={image:_r,image__holder:mr,image_loading:gr,image_holder:pr,image_failed:hr,image_transition:fr,image__watermark:br},Qa=new Map,yr=A=>{A.preventDefault(),A.stopPropagation()},kr="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0icmVkIiBmaWxsLW9wYWNpdHk9IjAiIC8+Cjwvc3ZnPgo=",vr={color:"#e0e0e0"};function Le({transition:A=!0,className:a,style:e,fit:i="cover",url:n,lazy:s=!0,holder:r=vr,watermark:m=!1,onFail:u,onLoad:b,...y}){const h=o.useRef(null),{ref:_,inView:f}=ln({initialInView:!s,triggerOnce:!0,rootMargin:"300px 0px 300px 0px",skip:!s}),v=o.useMemo(()=>f||!s,[f,s]),[c,d]=o.useState(()=>({failed:!1,loading:!(v&&Qa.get(n))}));o.useEffect(()=>{if(c.failed&&!ia.getState().connected){const{remove:x}=ut.addListener("networkStatusChange",({connected:C})=>{C&&d(T=>T.failed?{failed:!1,loading:!1}:T)});return()=>{x()}}return()=>{}},[c.failed]);const l=o.useMemo(()=>c.failed?"":n&&/^https:\/\/[^/]+\.pixocial\.com\//.test(n)&&!/\.(avif|gif|webp)$/.test(n)?`https://image-resizing.pixocial.com/cdn-cgi/image/quality=75,format=webp/${n}`:"",[n,c.failed]),g=o.useMemo(()=>{let x=e;return i&&(x={...x,objectFit:i}),x},[e,i]);o.useEffect(()=>{const x=n,{current:C}=h;d(T=>{const w=!(C&&C.src===x&&!!Qa.get(x));return T.failed||w!==T.loading?{failed:!1,loading:w}:T})},[n]);const p=o.useCallback(()=>{d({failed:!0,loading:!1}),u==null||u()},[u]),k=o.useCallback(x=>{var T;const C=(T=x==null?void 0:x.currentTarget)==null?void 0:T.src;C&&Qa.set(C,!0),d({failed:!1,loading:!1}),b==null||b()},[b]);o.useEffect(()=>{const x=h.current;if(ka()===0&&Qo()==="14.4"&&!c.loading&&!c.failed&&x){const{opacity:C}=x.style;x.style.opacity="1";let T=setTimeout(()=>{T=void 0,x.style.opacity=C},0);return()=>{T&&clearTimeout(T)}}},[c]);const S=t.jsx("img",{ref:h,decoding:"async",loading:s?"lazy":"eager",src:c.failed?kr:n,onError:p,onLoad:c.failed?void 0:k,onContextMenu:yr}),P=(r==null?void 0:r.color)||(r==null?void 0:r.image);return t.jsxs("picture",{...y,ref:v?void 0:_,className:N(Ge.image,{[Ge.image_holder]:P,[Ge.image_failed]:c.failed,[Ge.image_loading]:c.loading,[Ge.image_transition]:A},a),style:g,children:[v?t.jsxs(t.Fragment,{children:[l?t.jsx("source",{srcSet:l,type:"image/webp"}):null,S]}):null,P?t.jsx("div",{className:Ge.image__holder,style:{backgroundColor:r==null?void 0:r.color,backgroundImage:r.image?`url('${r.image}')`:void 0}}):null,m?t.jsx("div",{className:Ge.image__watermark}):null]})}const wr="_icon_wauya_1",Sr={icon:wr},xA=o.forwardRef(({className:A,url:a,...e},i)=>t.jsx("div",{...e,ref:i,className:N(Sr.icon,A),style:{backgroundImage:`url(${a})`}}));function Tr(){const A=nA(),a=UA(),[e,i]=o.useState(!1),[n,s]=o.useState(null);return o.useEffect(()=>{const r=()=>{a.getActive().then(u=>{var b;((b=u==null?void 0:u.component)==null?void 0:b.pathname)==="/"&&(s(y=>(y==null?void 0:y.type)===0?y:{type:1,title:"",text:A.formatMessage({id:"updateMessage"}),actions:[{type:"primary",title:A.formatMessage({id:"updateComfirmButton"}),handler(){i(!1),IA.restart()}},{type:"secondary",title:A.formatMessage({id:"updateCancelButton"}),handler(){i(!1)}}]}),i(!0))})},{remove:m}=IA.addListener("update",r);return()=>{typeof m=="function"&&m()}},[a]),t.jsx(Gi,{open:e,closeable:!1,title:n==null?void 0:n.title,message:t.jsx("div",{style:{whiteSpace:"pre-wrap"},children:n==null?void 0:n.text}),actions:n==null?void 0:n.actions})}const xr="_pagination_mi3wh_1",Cr="_dot_mi3wh_15",Pr="_dot_active_mi3wh_25",Va={pagination:xr,dot:Cr,dot_active:Pr};function zr({className:A,size:a,active:e,...i}){return t.jsx("div",{...i,className:N(Va.pagination,A),children:Array.from({length:a}).map((n,s)=>t.jsx("div",{className:N(Va.dot,{[Va.dot_active]:s===e})},s))})}const jr="_swipeableTabs_1ircn_1",Br="_swipeableTabs__header_1ircn_7",Mr="_swipeableTabs__title_1ircn_12",Ir="_swipeableTabs__title_active_1ircn_27",Er="_swipeableTabs__content_1ircn_31",Nr="_swipeableTabs__tabPane_1ircn_37",Ve={swipeableTabs:jr,swipeableTabs__header:Br,swipeableTabs__title:Mr,swipeableTabs__title_active:Ir,swipeableTabs__content:Er,swipeableTabs__tabPane:Nr},Rr=[],Fi=o.forwardRef((A,a)=>{const{className:e,active:i,items:n=Rr,onChange:s,...r}=A,[m,u]=o.useState();o.useImperativeHandle(a,()=>m,[m]);const b=o.useRef(null),[y,h]=is(n.length),_=o.useMemo(()=>{const c=n.findIndex(d=>d.key===i);return c<0?0:c},[i,n]);o.useEffect(()=>{m&&m.activeIndex!==_&&m.slideTo(_)},[_]),o.useEffect(()=>{const c=setTimeout(()=>{const d=b.current,l=y==null?void 0:y[_];if(!d||!l)return;const g=l.offsetLeft-(d.offsetWidth-l.offsetWidth)/2;g>=0&&g<=d.scrollWidth-d.offsetWidth?d.scrollTo({top:0,left:g,behavior:"smooth"}):g<0?d.scrollTo({top:0,left:0,behavior:"smooth"}):d.scrollTo({top:0,left:d.scrollWidth-d.offsetWidth,behavior:"smooth"})},16);return()=>{clearTimeout(c)}},[_]);const f=()=>n.map((c,d)=>t.jsx("div",{ref:h(d),className:N(Ve.swipeableTabs__title,{[Ve.swipeableTabs__title_active]:c.key===i}),onClick:()=>{s==null||s(c.key,d)},children:c.label},c.key)),v=()=>t.jsx("div",{ref:b,className:Ve.swipeableTabs__header,children:f()});return t.jsxs("div",{...r,className:N(Ve.swipeableTabs,e),children:[v(),t.jsx(Ca,{className:Ve.swipeableTabs__content,direction:"horizontal",slidesPerView:1,onInit:c=>{u(c),_!==c.activeIndex&&c.slideTo(_,0)},onActiveIndexChange:c=>{const d=n[c.activeIndex];d&&(s==null||s(d.key,c.activeIndex))},children:n.map(c=>t.jsx(Pa,{className:Ve.swipeableTabs__tabPane,children:c.children},c.key))})]})}),Dr="_tabs_7ew68_1",Lr="_header_7ew68_19",Or="_tabBarSkelton_7ew68_33",Gr="_tabBarSegment_7ew68_34",Fr="_tabBar__btn_7ew68_53",Ur="_tabBar__label_7ew68_74",Hr="_tabPane_7ew68_82",Kr="_tabPane_active_7ew68_87",Sa={tabs:Dr,header:Lr,tabBarSkelton:Or,tabBarSegment:Gr,tabBar__btn:Fr,tabBar__label:Ur,tabPane:Hr,tabPane_active:Kr};Sa.tabBarSkelton,Array.from({length:6}).map((A,a)=>t.jsx("div",{className:Sa.tabBar__btn,children:t.jsx(dn,{animated:!0,style:{width:50}})},a));o.memo(({active:A,children:a})=>{const[e,i]=o.useState(A);return o.useEffect(()=>{A&&i(!0)},[A]),e?t.jsx("div",{className:N(Sa.tabPane,{[Sa.tabPane_active]:A}),children:typeof a=="function"?a(A):a}):null});const qr="_app_1pmgk_1",Qr="_navigator_1pmgk_1",Vr="_app_loaded_1pmgk_4",Ja={app:qr,navigator:Qr,app_loaded:Vr},Jr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFUAAABUCAMAAADEd4vNAAAAM1BMVEUAAAAoKCgmJiYoKCgmJiYmJiYgICAmJiYlJSUmJiYnJycqKiomJiYnJycmJiYmJiYmJib8vM1fAAAAEHRSTlMAQN8g74AQn5BgvzDPcFCvgqSuxAAAAeBJREFUWMPtmNFuwyAMRcGGQCBJ/f9fuykt9UpgbW6zaZNyHitxMAWMHXNycvK7DLO/Mx5mJfnC5SDpKA/YA0NV0lsytle8VIz2CbmntNEJjgvc2vQo7xKGzdpJ3oe4inQjxbSP0QbI4aUmPqxfELxJUmPfDtWY7L4L1gHSxOZTW++HU2kGjuct5XC9TNbz/3SxcV6Ys51DiW3iMrZap33R6qI1Sg73vfpkSCKQNdY3hsNdO5BAVrJmC9NNEASyToNpwdP1ggpgVemGYdUKZCWV8ui9X7jSIlZNQXMqP433aRxoLYZMralmzErlZXTtyRJiLaOX3kCLWKn7SDg2Kw6wxk6S1KrDA1b7TT4frsMAq1mJnVdgxe22Tt2N1lqGdltTNxzN+Jfd1mBWpM1tJ/+I9fLCP5B2W+mF3Zp2W52e9O4NgW9BlhYM3wKvp6ezkxGwOs3OnWxOgFVsLxMupSNBrKlk7c4TQZBV5lYdTlkng6yOTTEU7+S10gCsdTmQF+/9yJuSH7BKOrLKUCZ+IgWrt5bWkmBWJXCj38Ot6s3bvhS3KhRmm5ntGHXtT60sx5H7nRGOM0qUowhGsXIUbH4g2PDDnfzxXx2UIQiM9ntbODiB0b50S7YobE5OTk7+KR/cB9REHcc3XwAAAABJRU5ErkJggg==",Wr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAOVBMVEUAAAAzMzMzMzM0NDQzMzM0NDQ0NDQzMzMzMzMwMDAyMjJAQEAzMzMzMzM0NDQzMzMyMjI1NTUzMzNMERRqAAAAEnRSTlMA798gkDBAv58QcBDPr4BQYGD6Nkc1AAABMklEQVQ4y5WV27aFIAhFUcFL98P/f+yRRu6GWlHroYeawCIVodbocbaG2Uxh8fAgj5k6ZSLdgYE7BQe9RmSRxc0lgEQrWhZhakm3f4iVP4p7cJOYzGU1F4WlljQDXGiQL1RXP2N7Z8b9OrK9oyZP6Q1L3KnWHR5h2foADxoy4EEUmOMtVoiw588x7hl1R9qoJhVmd5tNk4b63HiSpwVVVhwsklsVZkq6W3V0Y55hKla1f2Clq6SjKfcFzAy6BPuAfjFg1WU9Vn+CmflPR1fZMPh2CZYvC5uMPHWrRnK/24TC+Hdbm8pxUJIWwr85hu483PRQ3pz/M70eGRJ3z1KVRx9vbSzHfmgGLmQ3iqu3voziRqkM+JVGgNFt1YDvq3UKHq5Fsb6M8PnqWsIkTdsZfVP6H8FCFlBmEwwaAAAAAElFTkSuQmCC",Yr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAV1BMVEUAAAAzMzMzMzMzMzM0NDQzMzM0NDQ0NDQ1NTUzMzMzMzMzMzM0NDQwMDAzMzP////l5eVNTU2ZmZlAQEDy8vJZWVnZ2dmzs7OmpqaMjIyAgIDMzMxzc3NmdD/4AAAADnRSTlMA79+QQL8gEDDPr5+AMNLTz0gAAAExSURBVDjLnZXbkoMgDEAJN/GyofVat/3/71x1qgEKaPc8ZZgzkDCQMJ9CiooDIpSqkSyDFIAOUJuUqPADpSNiITCKKEJTc0zAg40NYBJuzkwCzOnp5OqjIo4n8L02gaeI9/F4Abmp6oqqtuozQjd429Zp89Xb1s02faVDb609LqxgMmneV3NyMmiS5m0xb51zXypr3mmhYiWVO/86tYcmcqeq0VK93RiaCIzi3pI7LXE/oIejznZh3sLHGr7Q550ACWu+7R74CfA9pGM3s8WAklXo8FzdR9REdTxWKjxuYsNkePErU/TFFvDpPiMm0Hdx3LGLqPWiyvhzDjHffBjaNo++/rm/bxlMw4mpL7e3H68V8px5vRX/o8ETOjY2UhPJ1P4wEvnR1agSFotXQgZH/wHRF0w9AoTdYwAAAABJRU5ErkJggg==",Zr="data:image/png;base64,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",Ui="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAABpCAMAAAAOXP0IAAAAXVBMVEUAAAA3Nzc0NDQ0NDQzMzMzMzMzMzMyMjI0NDQzMzMzMzMzMzMyMjI4ODgzMzP19fVkZGTd3d2UlJRLS0utra2goKBYWFjp6emIiIg/Pz/R0dHFxcV8fHxwcHC4uLga4aEvAAAADnRSTlMAIIC/v9+rZEDvkFAwED9uvpYAAAFKSURBVGje7dZZboQwFERRm2bqycYQZsj+lxkpaalovsxQSHTe3cBR8YGfkiRJkj62ZxKs664XOWlo1hcusAKzrWAhxKcSs73US4p2kCIf6G4Qd9RtF+nmIYW7SBeRDpSq3k3qSprUOvvewJIyO6s8TMrYUv31FyDaptwglgSKL9WgyNLgQHGlrABFlgwotgSKLYGiS6BYUvWSQDUkqa2ttYWZUgVHMmPnfleAGhkSAuUKpgSqLKvz3xEiiSTSx0l57ZpDpGz2NFYZSxrtG9VbW5Kk1r0oLCwoEm4LSBlHAkWXQNElUHQJFF0CxZZAfbMlUEwJFF0CRZd6i6jSYI+SyrlUsaRqBnWG99/LpzXt+e+IE0nxYVKwi5R4SHoXSauDPl+sfNLRZijSyqvHVur6UIi56qKVf0l8XbvnkqqFPfWqlCRJkiRJ/6UfHLYM61fFpgwAAAAASUVORK5CYII=",Hi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAABpCAMAAAAOXP0IAAAANlBMVEUAAAA1NTUzMzMzMzM0NDQzMzMzMzMzMzMzMzMzMzMyMjIzMzMyMjIzMzMzMzMzMzMzMzMzMzOHvxeQAAAAEXRSTlMAEL/vQN+AIJ9wYJCPMK/PUBitodAAAAJ/SURBVGje7ZjddqsgEIVlGH7ERMP7v+xZckxRMEGY8ablu2m7msU47M0e4tDpdDq/FJyll+CGu3mC/89ihjsRD/+DEjesr+chMEq/48FYIq6Pq0DLVgL0HH4yN4UQFh/MWyC1VtXrbyNnHTNv68/bxkkdOhHrnxOnQNIfmd47xlvJqaQO4I92nLuHkNRRLjG74dk46yPJ+dn21DILFMGtV7VVprUUF8tRwYwxjZiiLQeea6/+bXamaMtRuE+jSfBET47UYmdGwLsE8lbs0kK9GKLng0AYzBi7ozv7XCC3Twtr7hAIxtdr4hcIsjomTQu5dmfIsyHFxrQ42E8K5uhRIjEjmO2oWdJsyHHJRQhjsCJFoJzwv8dboPHQHWE25Cyh2Rg9h6PmWATaP/e4FyjXkB49cUDILVgxeSBNnw0RE0rpMXzUpnlryLPh/LGNT5kJ0ZPxHAJ5S5VON9YXiLeEs+6BcF5zHubLeQsCsrXlF1g+7bIUA2tbZcvQ2ypjKsefr6cx/rRvBiujr10rVTudfDNjZSnwrdQOevRVUL68E6z+rDSF7E6nOB35m+J3+lT7fdM30XKjne5xn/aazelTKemQPj7K91mjTq+hin8e2vPIQmZDRJsh0enlFzofv5uYhrHxvDT4HDn+VFboXHhFdLrU4uosdySn26TONy8DIf4Aq0IbW52uXDneCkkyNwlUng5Lnh8XTDFfuiYfF5I1g77yzZE/Uj3oZbhMknsqX2m1aJziUDfowTTft8aa24tCwiXSFE5CLlBjU/rC0xBeWtvyEBtPXsm3MJWvGpAK1Iix0kuL39ScC9HDiFNxNtzNawKwoxg6nU6n0+n8Yf4B+JbLlJ9H2qEAAAAASUVORK5CYII=",Xr="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/home_pic_banner-zjFFjERJ.png",$r="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/home_pic_banner_as-2rOb0JXi.png",Ac="_history_12t5z_1",ec="_body_12t5z_13",ac="_container_12t5z_17",tc="_main_12t5z_25",ic="_main__before_12t5z_35",nc="_main__header_12t5z_40",oc="_main__title_12t5z_45",sc="_main__subtitle_12t5z_53",rc="_main__logo_12t5z_60",cc="_main__banner_12t5z_66",lc="_main__after_12t5z_76",dc="_main__gradient_12t5z_82",uc="_main__try_12t5z_91",_c="_main__tryIcon_12t5z_110",mc="_more_12t5z_115",gc="_more__space_before_12t5z_126",pc="_more__space_inner_12t5z_127",hc="_more__space_after_12t5z_128",fc="_more__content_12t5z_143",bc="_feature_12t5z_152",yc="_feature__icon_12t5z_173",kc="_feature__title_12t5z_178",vc="_agreement_12t5z_184",wc="_agreement__icon_12t5z_190",Sc="_agreement__message_12t5z_199",Tc="_confirm__message_12t5z_208",lA={history:Ac,body:ec,container:ac,main:tc,main__before:ic,main__header:nc,main__title:oc,main__subtitle:sc,main__logo:rc,main__banner:cc,main__after:lc,main__gradient:dc,main__try:uc,main__tryIcon:_c,more:mc,more__space_before:gc,more__space_inner:pc,more__space_after:hc,more__content:fc,feature:bc,feature__icon:yc,feature__title:kc,agreement:vc,agreement__icon:wc,agreement__message:Sc,confirm__message:Tc},xc=fe(()=>{const A=nA(),a=UA(),e=oe();BA(()=>{uA()?e.event("snapid_page_event",{page_id:"home_page_view"}):mA()&&e.event("func_enter_snapid_headshot")},[e]),BA(()=>(IA.setSwipeBackEnabled({enable:!0}),()=>{IA.setSwipeBackEnabled({enable:!1})}),[]),BA(()=>{EA.antiScreenshot({open:!1})},[]);const[i,n]=Pn(g=>[g.agreed,g.setAgreed]),s=g=>{const p=g.target;p&&p.tagName==="A"&&p.href&&(g.stopPropagation(),g.preventDefault(),wi(p.href))},r=A.formatMessage({id:"agreement"},{termsOfService:`<a href="https://snapid.ai/terms-of-service">${A.formatMessage({id:"termsOfService"})}</a>`,privacyPolicy:`<a href="https://snapid.ai/privacy-policy">${A.formatMessage({id:"privacy_policy"})}</a>`}),[m]=wt(),u=g=>{XA(()=>{i?g():m({message:t.jsx("div",{className:lA.confirm__message,dangerouslySetInnerHTML:{__html:r},onClick:s}),actions:[{type:"secondary",title:A.formatMessage({id:"cancel"})},{type:"primary",title:A.formatMessage({id:"agree"}),handler(){n(!0),g()}}]})})},b=qe(g=>g.empty),y=t.jsx(se,{leftButtons:b?[]:[t.jsx("div",{className:N(lA.history,"touchable-opacity"),onClick:()=>{a.push("/task")},children:A.formatMessage({id:"history"})},"history")],translucent:!0}),h=qo(),_=t.jsxs("div",{className:lA.main,children:[t.jsx("div",{className:lA.main__before}),t.jsxs("div",{className:lA.main__header,onClick:()=>{var g,p;Vo()===0&&jA.text((p=(g=ne.getState().data)==null?void 0:g.miniAppVersion)!=null?p:"")},children:[t.jsx("div",{className:N(lA.main__title,"poppins-extrabold"),children:A.formatMessage({id:"homeTitle"})}),t.jsxs("div",{className:lA.main__subtitle,children:[t.jsx("div",{children:A.formatMessage({id:"homeCaption"})}),t.jsx(xA,{className:lA.main__logo,url:Zr})]})]}),t.jsx(xA,{className:lA.main__banner,url:h?$r:Xr}),t.jsx("div",{className:lA.main__after}),t.jsx("div",{className:lA.main__gradient}),t.jsxs("div",{className:lA.main__try,onClick:()=>{uA()?e.event("snapid_page_button_clk",{clk:"create",project:"id_photo"}):mA()&&e.event("snapid_headshot_page_click",{name:"create"}),u(()=>{const g=setTimeout(()=>{jA.loading()},300);Ea.getState().initiateIfNeed().then(()=>{clearTimeout(g),jA.hide(),a.push("/paper")}).catch(()=>{clearTimeout(g),jA.error(sA("failed"))})})},children:[t.jsx(xA,{className:lA.main__tryIcon,url:Jr}),t.jsx("span",{children:A.formatMessage({id:"getStarted"})})]})]}),f=t.jsxs("div",{className:lA.agreement,onClick:()=>n(!i),children:[t.jsx(xA,{className:lA.agreement__icon,url:i?Yr:Wr}),t.jsx("div",{className:lA.agreement__message,dangerouslySetInnerHTML:{__html:r},onClick:s})]}),v=Ri(),c=Di(),d=[{icon:Hi,title:A.formatMessage({id:"colorChange"}),onClick:()=>{uA()?e.event("snapid_page_button_clk",{clk:"create",project:"change_color"}):mA()&&e.event("snapid_headshot_page_click",{name:"change_color"}),u(v)}},{icon:Ui,title:A.formatMessage({id:"photoResize"}),onClick:()=>{uA()?e.event("snapid_page_button_clk",{clk:"create",project:"change_size"}):mA()&&e.event("snapid_headshot_page_click",{name:"change_size"}),u(c)}}],l=t.jsxs("div",{className:lA.more,children:[t.jsx("div",{className:lA.more__space_before}),t.jsx("div",{className:lA.more__content,children:d.map((g,p)=>t.jsxs("div",{className:N(lA.feature,"touchable-opacity"),onClick:g.onClick,children:[t.jsx(xA,{className:lA.feature__icon,url:g.icon}),t.jsx("div",{className:lA.feature__title,children:g.title})]},p))}),t.jsx("div",{className:lA.more__space_inner}),f,t.jsx("div",{className:lA.more__space_after})]});return t.jsxs(t.Fragment,{children:[y,t.jsx(ie,{fullscreen:!0,className:lA.body,children:t.jsxs("div",{className:lA.container,children:[_,l]})})]})}),Cc="_content_1ss9p_1",Pc="_tab_1ss9p_4",Lt={content:Cc,tab:Pc},zc="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABUCAMAAAArteDzAAAAM1BMVEUAAAAzMzMyMjIzMzMzMzMyMjIzMzM1NTUyMjIzMzMzMzMyMjIzMzMzMzM1NTUwMDAzMzOIZg/eAAAAEHRSTlMAv99/nxDPIHCvkF/vUDAwvbif8gAAAKtJREFUWMPt09EKwyAMheHYxqq17c77P+3EyspGe7O4Mdj5QPDqRwMRIiIioq8IqkF6UyBJbw5wjDLK6A9GvbrtNbol9WIxA8jP0bwAozVaE0c0opht359q9YiOKKZgHGqq1T3amq6O1D4B3aO63+3aj6dyXHt1p2pzNO0iHqJ0sy6oliwdbQOK4SZdhawavdAficO5ybBXAZeCvMvjkjespzuXViEiIiL6mDsxXAzCpd862wAAAABJRU5ErkJggg==",jc="_tips_l13je_1",Bc="_container_l13je_15",Mc="_photoBox_l13je_20",Ic="_photoHead_l13je_29",Ec="_photoTime_l13je_37",Nc="_photoDownload_l13je_42",Rc="_photoDownloadIcon_l13je_51",Dc="_photoList_l13je_55",Lc="_photoItem_l13je_64",Oc="_photoBorder_l13je_74",Gc="_printerButton_l13je_77",Fc="_printerImage_l13je_90",Uc="_printerPopover_l13je_95",Hc="_pritingTipsTitle_l13je_119",Kc="_pritingTipsContent_l13je_125",ve={tips:jc,container:Bc,photoBox:Mc,photoHead:Ic,photoTime:Ec,photoDownload:Nc,photoDownloadIcon:Rc,photoList:Dc,photoItem:Lc,photoBorder:Oc,printerButton:Gc,printerImage:Fc,printerPopover:Uc,pritingTipsTitle:Hc,pritingTipsContent:Kc};function qc(){const A=nA(),a=UA(),{searchParams:e}=Qe(),i=(e==null?void 0:e.get("source"))||"",{loading:n,list:s,initiateIfNeed:r}=qe();o.useEffect(()=>{r()},[r]);const m=oe();BA(()=>{uA()&&m.event("snapid_page_event",{page_id:"generated_page_view"})},[m]);const u=Ct(),b=async h=>{var _;uA()?m.event("snapid_page_button_clk",{name:(_=h.report)==null?void 0:_.id_name,...h.report,clk:"save",project:{paper:"id_photo",recolor:"change_color",resize:"change_size"}[h.type]||"id_photo"}):mA()&&m.event("edit_save",{prf_func_saved:"snapid"}),u(async()=>{var f;await Promise.allSettled((f=h.photos)==null?void 0:f.map(async v=>{await ZA.save({type:2,data:v.url})}))})};let y;return n?y=null:(s==null?void 0:s.length)>0?y=t.jsxs(t.Fragment,{children:[t.jsx("div",{className:ve.tips,dangerouslySetInnerHTML:{__html:A.formatMessage({id:"loginSaveTip1"})}}),t.jsx("div",{className:ve.container,children:s.map((h,_)=>{var f;return t.jsxs("div",{"data-index":_,className:ve.photoBox,children:[t.jsxs("div",{className:ve.photoHead,children:[t.jsx("div",{className:ve.photoTime,children:Bo(h.createdTime)}),t.jsx("div",{className:N(ve.photoDownload,"touchable-opacity"),onClick:()=>{b(h)},children:t.jsx(xA,{className:ve.photoDownloadIcon,url:zc})})]}),t.jsx("div",{className:ve.photoList,children:(f=h==null?void 0:h.photos)==null?void 0:f.map((v,c)=>{var g;const d=c===0&&((g=h.params)==null?void 0:g.background)==="white",l=80*(window.innerWidth/390);return t.jsx(Le,{className:N(ve.photoItem,d&&c===0&&ve.photoBorder),style:{width:v.aspectRatio?l*v.aspectRatio:"auto",height:l},url:v.url,onClick:()=>{a.push(`/task/detail?source=${i}&id=${h.id}&index=${c}`)}},c)})})]},h.id)})})]}):y=t.jsx(wa,{fullscreen:!0,status:"empty",renderTitle:()=>t.jsx("div",{style:{textAlign:"center"},dangerouslySetInnerHTML:{__html:A.formatMessage({id:"idTaskEmpty"})}}),actions:[{type:"load",title:A.formatMessage({id:"taskExplore"}),onClick(){a.popToRoot()}}]}),t.jsx(t.Fragment,{children:y})}const Qc=fe(()=>{const{searchParams:A}=Qe(),e=o.useMemo(()=>A==null?void 0:A.get("source"),[A])==="set",i=UA(),n=nA();return o.useEffect(()=>{const s=setTimeout(()=>{e||i.removeToRoot("/task")},300);return()=>{clearTimeout(s)}},[i,e]),BA(()=>{EA.antiScreenshot({open:!1})},[]),t.jsxs(Ke,{children:[t.jsx(se,{className:Lt.appBar,clickToTop:!0,backable:!0,title:n.formatMessage({id:"history"}),onBack:()=>e?i.pop():i.popTo("/")}),t.jsx(ie,{fullscreen:!0,scrollEvents:!0,className:Lt.content,children:t.jsx(qc,{})})]})}),Vc="_backIcon_15uc4_1",Jc="_body_15uc4_5",Wc="_container_15uc4_8",Yc="_previewer_15uc4_22",Zc="_previewer__item_15uc4_26",Xc="_previewer__item_arrow_15uc4_36",$c="_blink_15uc4_1",Al="_previewer__photo_15uc4_60",el="_previewer__photo_white_15uc4_66",al="_previewer__name_15uc4_69",tl="_footer_15uc4_76",il="_footer__header_15uc4_79",nl="_footer__content_15uc4_101",ol="_save_15uc4_106",sl="_action_15uc4_119",rl="_action__btn_15uc4_129",cl="_action__icon_15uc4_139",ll="_action__title_15uc4_143",yA={backIcon:Vc,body:Jc,container:Wc,previewer:Yc,previewer__item:Zc,previewer__item_arrow:Xc,"swiper-arrow-blink":"_swiper-arrow-blink_15uc4_1",blink:$c,previewer__photo:Al,previewer__photo_white:el,previewer__name:al,footer:tl,footer__header:il,footer__content:nl,save:ol,action:sl,action__btn:rl,action__icon:cl,action__title:ll},dl=fe(()=>{var c,d;const A=nA(),a=Qe(),e=((c=a.searchParams)==null?void 0:c.get("id"))||"",i=Number(((d=a.searchParams)==null?void 0:d.get("index"))||"0"),n=oe(),s=qe(l=>l.list.find(g=>g.id===e)),[r,m]=o.useState(i),[u,b]=o.useState(()=>{var l;return((l=s==null?void 0:s.photos.length)!=null?l:0)>1}),y=t.jsx(Ca,{className:yA.previewer,initialSlide:i,onSlideChange:l=>{l.activeIndex!==r&&(m(l.activeIndex),b(!1))},children:s==null?void 0:s.photos.map((l,g)=>{var p;return t.jsxs(Pa,{className:N(yA.previewer__item,{[yA.previewer__item_arrow]:g===r&&u}),children:[t.jsx("img",{className:N(yA.previewer__photo,{[yA.previewer__photo_white]:g===0&&((p=s.params)==null?void 0:p.background)==="white"}),src:l.url,onClick:k=>k.stopPropagation()}),t.jsx("div",{className:yA.previewer__name,children:l.name})]},g)})}),h=Ct(),_=Ri(),f=Di(),v=t.jsxs("div",{className:yA.footer,children:[t.jsx("div",{className:N(yA.save,"touchable-opacity"),onClick:()=>{var g,p,k;const l=s==null?void 0:s.photos[r];s&&l&&(uA()?n.event("snapid_page_button_clk",{name:(g=s.report)==null?void 0:g.id_name,...s.report,clk:"save",project:{paper:"id_photo",recolor:"change_color",resize:"change_size"}[s.type]||"id_photo",background:((p=pA.find(S=>S.name===l.background))==null?void 0:p.label)||((k=s.report)==null?void 0:k.background)}):mA()&&n.event("edit_save",{prf_func_saved:"snapid"}),h(async()=>{var S;await ZA.save({type:2,data:(S=l.url)!=null?S:""})}))},children:A.formatMessage({id:"save"})}),t.jsx("div",{className:yA.footer__header,children:A.formatMessage({id:"more"})}),t.jsxs("div",{className:yA.footer__content,children:[t.jsxs("div",{className:N(yA.action,"touchable-opacity"),onClick:()=>{var g,p;let l=(g=s==null?void 0:s.photos)==null?void 0:g[r];(l==null?void 0:l.isPoster)===!0&&(l=(p=s==null?void 0:s.photos)==null?void 0:p[0]),l&&l.url&&_(l.url)},children:[t.jsx("div",{className:yA.action__btn,children:t.jsx(xA,{className:yA.action__icon,url:Hi})}),t.jsx("div",{className:yA.action__title,children:A.formatMessage({id:"colorChange"})})]}),t.jsxs("div",{className:N(yA.action,"touchable-opacity"),onClick:()=>{var g,p;let l=(g=s==null?void 0:s.photos)==null?void 0:g[r];(l==null?void 0:l.isPoster)===!0&&(l=(p=s==null?void 0:s.photos)==null?void 0:p[0]),l&&l.url&&f(l.url)},children:[t.jsx("div",{className:yA.action__btn,children:t.jsx(xA,{className:yA.action__icon,url:Ui})}),t.jsx("div",{className:yA.action__title,children:A.formatMessage({id:"photoResize"})})]})]})]});return t.jsxs(t.Fragment,{children:[t.jsx(se,{className:yA.appBar,backable:!0,title:A.formatMessage({id:"history"})}),t.jsx(ie,{fullscreen:!0,scrollEvents:!0,className:yA.body,children:t.jsxs("div",{className:yA.container,children:[y,v]})})]})}),ul="data:image/png;base64,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",_l="data:image/png;base64,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",ml="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_1_bad-Dg1sc4yk.png",gl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_1_good-C1Que2YY.png",pl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_2_bad-DiOzAkx4.png",hl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_2_good-5gWzPZyQ.png",fl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_3_bad-Dn0Df1JR.png",bl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_3_good-B5XpScic.png",yl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_4_bad-CxQ1BlqD.png",kl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_4_good-DSZ7MI7t.png",vl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_5_bad-Ddx085kr.png",wl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_5_good-CAqiO6dP.png",Sl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_6_bad-D4tRpKha.png",Tl="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_pic_guide_6_good-BSGDDQfe.png",xl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABJCAMAAACpb5DQAAAAbFBMVEUAAAAW3UIY30MY3UQW3UIW3UIX3kMd30kW3UIW3kMW3kIX3EIW3UIX3UIW30IX3EMW3EEb30UW3UL///+L7qHi++cz4Vrx/fNC42Zt6omo8rht6YnU+du29cRt6ohC42Uk305X5nen87iZ8KwEMzFVAAAAEnRSTlMA3yBA74C/EJDPn2CvcH9QoDAyFzX8AAAB1UlEQVRYw73YaXqDIBCA4QEF4tIkHTTGLG3S3v+O9R8to7KM6XeA9wGUCIF/yrw3jZpqDoXJRop9W+KvSq2KDGU3ITRRJ1lm7xRqNZLJOGofRb1RhlJNkJEao6okbziu8m3N2WFCu0XGaEyqMvOOFJiYkAGHJZkKXZzZtZhV7TsKM9t7exSzK3gL7RIm8CLmvJgSXazJaR6kyUpzh6S5kCYrxBtSzUPcgxOY1PPrPqJXmbHUnZ3qHzNzq5OdqW5mbiLDsb2/TwBkjmNv6CXhkOPYC3odQOU4/Qm9FLSbOFjH749xzcFq5qE9uvt9THRQAPlGn3rvRfGc4YwzlYB+l7lX7uw5NArdLJXOA3HCUG+dRBy75CBdo2/rSyfnfCw5JX1qj96TTn3YQQEV0sfmpFgHNRxxTRo9drEWFK5KXZyDytv9dHnjHDws/B59DmkOSgARlsKOcAcIKiU4WK98Ra7ECX0hy6B0CTli/XB0jXWwDhxGrpEOytBp5HmzQxd29Gbno21PbPwhye1PtWA452y56cmffxdR4HXMc9rX3df4N0j+nZY9O21edu/n/xNBk5HT0xJCNYI3HJdUIsAoA3HJRjAZV3EUs8ou5/86pcs/SKtWlB+SGB6wvEuJRQAAAABJRU5ErkJggg==",Cl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABJCAMAAACpb5DQAAAAZlBMVEUAAAD/U1P/VFT/VFT/U1P/U1P/VFT/UFD/U1P/U1P/U1P/UlL/UlL/VFT/U1P/VFT/UlL/U1P/VVX/U1P/////lJT/9fX/fn7/1NT/v7//qan/Y2P/6en/39//tLT/n5//np7/iYkMFucGAAAAE3RSTlMA3yBA77+AEJDPn2B/r1Bwb7Awl9FNGQAAAbpJREFUWMO9mMl2wyAMRQEzxFOS9tmZkzb//5OdFjoxBmzQ6d3Zi3skMQnEP+He29Z+024rly2pul4DhDa2yrBsNGaQzSqX68jiu1pVqCFVt0i1J00kqqRGGSyiVmXhEHof82ywgk24ygarqF2gPBIrkSrk4TC5GkRJdj2yaKaeDpl0kzWKbKqyQhPSxSZi5sRUIIqSMyjCeJUuDcmgEBOp0OV2OB0x4Xg63C/hkBr4PIYfRrww/v48BwdOwuM4DGQizx9HeOhQqT8GMk09w+dcboHMxoFM3q9nIDcJn+uBTFPP4QofSWPmhUQm/9tHiS3SprQHW2GRNqU9sKJH2pT2oImsjzHtIWohkTalPZBCI21Ke6AFYowpDyEQ5UyeM6KwRcRWI75Rq3nmkRENz8x+Y1trbKufbT/i2iEDDcRzZs+OhtRwniJCl59rMtgcncMn7WMus3AzcrnPn/23C2ZQXN0IW3/E17HxhKT4u1rhSvpsxdn5ExaZWDGhQRY9232N6wbJeqctzs449nu/z275S8RORFEL0zNKpGhlWTiEsjKhsU4sQ7WyUENUjZy1bHLe66zRL5LeRixfTMkt8w31ZLgAAAAASUVORK5CYII=",Pl="_modal_vtgys_1",zl="_container_vtgys_5",jl="_body_vtgys_14",Bl="_tips_vtgys_20",Ml="_tips__title_vtgys_26",Il="_tips__icon_vtgys_34",El="_tips__content_vtgys_41",Nl="_tips__swiper_vtgys_44",Rl="_tips__slide_vtgys_49",Dl="_tips__pagination_vtgys_52",Ll="_tip__title_vtgys_63",Ol="_tip__content_vtgys_73",Gl="_tip__demo_vtgys_78",Fl="_tip__pic_vtgys_83",Ul="_tip__badge_vtgys_88",Hl="_specifications_vtgys_95",Kl="_specifications__title_vtgys_103",ql="_specifications__icon_vtgys_112",Ql="_specifications__container_vtgys_118",Vl="_specifications__contentContainer_vtgys_125",Jl="_specifications__subtitle_vtgys_134",Wl="_specifications__size_vtgys_141",Yl="_specifications__sizeDivider_vtgys_146",Zl="_specifications__content_vtgys_125",Xl="_specifications__content_scrollable_vtgys_158",$l="_specifications__space_vtgys_163",Ad="_specifications__preview_vtgys_166",ed="_confirm_vtgys_177",ad="_link_vtgys_189",td="_appBar_vtgys_196",tA={modal:Pl,container:zl,body:jl,tips:Bl,tips__title:Ml,tips__icon:Il,tips__content:El,tips__swiper:Nl,tips__slide:Rl,tips__pagination:Dl,tip__title:Ll,tip__content:Ol,tip__demo:Gl,tip__pic:Fl,tip__badge:Ul,specifications:Hl,specifications__title:Kl,specifications__icon:ql,specifications__container:Ql,specifications__contentContainer:Vl,specifications__subtitle:Jl,specifications__size:Wl,specifications__sizeDivider:Yl,specifications__content:Zl,specifications__content_scrollable:Xl,specifications__space:$l,specifications__preview:Ad,confirm:ed,link:ad,appBar:td},id={delay:5*1e3,stopOnLastSlide:!1,disableOnInteraction:!1,reverseDirection:!1,waitForTransition:!0};function Ki(){const[A,a]=o.useState(!1),[e]=wt(),i=o.useCallback(()=>{NA.getState(),a(!0)},[e]),n=o.useCallback(()=>a(!1),[]);return[o.useMemo(()=>A?t.jsx(nd,{open:A,onClose:n}):null,[A,n]),i,n]}function nd({open:A,onClose:a}){var l;const e=nA(),i=UA(),{sizeItem:n,setPhotoType:s,setPhotoPosition:r,setOriginImg:m,sizeDisplay:u}=NA(),[b,y]=o.useState(0),h=[{title:e.formatMessage({id:"idPhotoTip1"}),bad:ml,good:gl},{title:e.formatMessage({id:"idPhotoTip2"}),bad:pl,good:hl},{title:e.formatMessage({id:"idPhotoTip3"}),bad:fl,good:bl},{title:e.formatMessage({id:"idPhotoTip4"}),bad:yl,good:kl},{title:e.formatMessage({id:"idPhotoTip5"}),bad:vl,good:wl},{title:e.formatMessage({id:"idPhotoTip6"}),bad:Sl,good:Tl}],_=async()=>{let g;const p=async k=>{var S,P,x;if(GA.closeReceived().catch(()=>{}),(k==null?void 0:k.from)===0&&k.photo){let C,T=new Promise((w,I)=>{C=I});const B=JA.addListener("cancel",()=>{C==null||C(new Error("cancel"))});try{JA.showLoading({message:"",timeout:1e4,cancelText:e.formatMessage({id:"cancel"})}),s((S=k.type)!=null?S:0),r((P=k.position)!=null?P:0);const w=await Promise.race([je.fileToBase64({file:k.photo.source}),T]);if(!w.data)throw new Error("no data");let I=`data:image/${w.suffix||"jpeg"};base64,${w.data}`;I=await Promise.race([ya(I,"jpg"),T]),I=`data:image/jpeg;base64,${I}`,m(I),a(),i.push("/IDPhoto/adjust",{animated:!1}),setTimeout(()=>{JA.dismiss(),g==null||g.remove(),GA.close({animationTo:"right"})},300)}catch(w){(w==null?void 0:w.message)==="cancel"?GA.resume():(JA.showText({message:sA("network_error_info")}),GA.resume())}finally{B.remove()}}else(x=g==null?void 0:g.remove)==null||x.call(g),GA.close({animationTo:"left"})};try{g=await GA.addListener("close",p)}catch(k){console.log(k)}},[f]=qt(),v=()=>Promise.all([Qt.check({permission:"camera",request:!1})]).then(g=>g.find(p=>p.status===0)?(f({header:e.formatMessage({id:"albumPermissionTitle"}),message:e.formatMessage({id:"albumPermissionContent"}),buttons:[{role:"cancel",text:e.formatMessage({id:"later"})},{role:"confirm",text:e.formatMessage({id:"settings"}),handler(){IA.gotoSetting()}}]}),Promise.reject(new Error("no album auth"))):Promise.resolve()),c=async()=>{var g,p;try{await v(),a(),_(),GA.open({maxWidth:1536,fd:!0,needControlBack:!0,report:{page_id:"takephoto_page_view",project:"id_photo",name:(g=n==null?void 0:n.name)!=null?g:"",size_value:(p=n==null?void 0:n.sizeValue)!=null?p:""}})}catch(k){console.log(k)}},d=o.useRef(null);return o.useEffect(()=>{if(A){let g=null;const p=S=>{S.stopPropagation()},k=setTimeout(()=>{g=d.current,g==null||g.addEventListener("touchmove",p),g==null||g.addEventListener("pointermove",p)},100);return()=>{clearTimeout(k),g==null||g.removeEventListener("touchmove",p),g==null||g.removeEventListener("pointermove",p)}}return()=>{}},[A]),t.jsx(oa,{className:tA.modal,isOpen:A,position:"bottom",handle:!0,initialBreakpoint:1,breakpoints:[0,1],onClose:a,children:t.jsxs("div",{className:tA.container,children:[t.jsxs("div",{className:tA.body,children:[t.jsxs("div",{className:tA.tips,children:[t.jsxs("div",{className:tA.tips__title,children:[t.jsx(xA,{className:tA.tips__icon,url:ul}),t.jsx("div",{children:e.formatMessage({id:"shootingTips"})})]}),t.jsxs("div",{className:tA.tips__content,children:[t.jsx(Ca,{className:tA.tips__swiper,slidesPerView:1,initialSlide:b,autoplay:id,modules:[un],onActiveIndexChange:g=>{y(g.activeIndex)},children:h.map((g,p)=>t.jsxs(Pa,{className:tA.tips__slide,children:[t.jsx("div",{className:tA.tip__title,dangerouslySetInnerHTML:{__html:g.title}}),t.jsxs("div",{className:tA.tip__content,children:[t.jsxs("div",{className:tA.tip__demo,children:[t.jsx(xA,{className:tA.tip__pic,url:g.good}),t.jsx(xA,{className:tA.tip__badge,url:xl})]}),t.jsxs("div",{className:tA.tip__demo,children:[t.jsx(xA,{className:tA.tip__pic,url:g.bad}),t.jsx(xA,{className:tA.tip__badge,url:Cl})]})]})]},p))}),t.jsx(zr,{className:tA.tips__pagination,active:b,size:h.length})]})]}),t.jsxs("div",{className:tA.specifications,children:[t.jsxs("div",{className:tA.specifications__title,children:[t.jsx(xA,{className:tA.specifications__icon,url:_l}),t.jsx("div",{children:e.formatMessage({id:"specifications"})})]}),t.jsxs("div",{className:tA.specifications__container,children:[t.jsxs("div",{className:tA.specifications__contentContainer,children:[t.jsx("div",{className:tA.specifications__subtitle,children:(n==null?void 0:n.name)===e.formatMessage({id:"custom_size"})?e.formatMessage({id:"custom_size"}):e.formatMessage({id:"printSize"})}),t.jsx("div",{className:N(tA.specifications__content,tA.specifications__size),children:(u!=null?u:"").split("|").map((g,p)=>p>=1?t.jsxs(o.Fragment,{children:[t.jsx("span",{className:tA.specifications__sizeDivider}),t.jsx("span",{children:g},p)]},p):t.jsx("span",{children:g},p))}),t.jsx("div",{className:tA.specifications__space}),(n==null?void 0:n.specialNotes)&&t.jsxs(t.Fragment,{children:[t.jsx("div",{className:tA.specifications__subtitle,children:e.formatMessage({id:"specialNotes"})}),t.jsx("div",{ref:d,className:N(tA.specifications__content,tA.specifications__content_scrollable),dangerouslySetInnerHTML:{__html:(l=n.specialNotes)!=null?l:""}})]})]}),n!=null&&n.previewLink?t.jsx("img",{className:tA.specifications__preview,src:n.previewLink}):null]})]})]}),t.jsxs("div",{className:tA.footer,children:[t.jsx("div",{className:tA.confirm,onClick:c,children:e.formatMessage({id:"confirm"})}),n!=null&&n.link?t.jsx("div",{className:tA.link,onClick:()=>{n!=null&&n.link&&wi(n.link)},children:e.formatMessage({id:"preview_link"})}):null]})]})})}const _a=[{key:"north_america",countries:["CA","CR","CU","DO","SV","GL","GT","HT","HN","JM","MX","NI","PA","US"]},{key:"south_america",countries:["AR","BO","BR","CL","CO","EC","FK","GF","GY","PY","PE","SR","UY","VE"]},{key:"europe",countries:["AL","AD","AT","BY","BE","BA","BG","HR","CZ","DK","EE","FO","FI","FR","DE","GI","GR","HU","IS","IE","IM","IT","XK","LV","LI","LT","LU","MT","MD","MC","ME","NL","MK","NO","PL","PT","RO","RU","SM","RS","SK","SI","ES","SJ","SE","CH","TR","UA","GB","VA"]},{key:"asia",countries:["AF","AM","AZ","BH","BD","BT","BN","KH","CN","CY","GE","IN","ID","IR","IQ","IL","JP","JO","KZ","KW","KG","LA","LB","MO","MY","MV","MN","MM","NP","KP","OM","PK","PS","PH","QA","SA","SG","KR","LK","SY","TW","TJ","TH","TL","TM","AE","UZ","VN","YE"]},{key:"oceania",countries:["AS","AU","CK","FJ","PF","GU","KI","MH","FM","NR","NC","NZ","NU","NF","MP","PW","PG","PN","WS","SB","TK","TO","TV","UM","VU","WF"]},{key:"africa",countries:["DZ","AO","BJ","BW","BF","BI","CV","CM","CF","TD","KM","CD","CG","CI","DJ","EG","GQ","ER","SZ","ET","GA","GM","GH","GN","GW","KE","LS","LR","LY","MG","MW","ML","MR","MU","YT","MA","MZ","NA","NE","NG","RE","RW","SH","ST","SN","SC","SL","SO","ZA","SS","SD","TZ","TG","TN","UG","EH","ZM","ZW"]}],zt=[[{key:"anguilla",code:"AI",icon:"🇦🇮"},{key:"antigua_barbuda",code:"AG",icon:"🇦🇬"},{key:"bahamas",code:"BS",icon:"🇧🇸"},{key:"barbados",code:"BB",icon:"🇧🇧"},{key:"belize",code:"BZ",icon:"🇧🇿"},{key:"bermuda",code:"BM",icon:"🇧🇲"},{key:"canada",code:"CA",icon:"🇨🇦"},{key:"costa_rica",code:"CR",icon:"🇨🇷"},{key:"cuba",code:"CU",icon:"🇨🇺"},{key:"dominica",code:"DM",icon:"🇩🇲"},{key:"dominican_republic",code:"DO",icon:"🇩🇴"},{key:"el_salvador",code:"SV",icon:"🇸🇻"},{key:"greenland",code:"GL",icon:"🇬🇱"},{key:"grenada",code:"GD",icon:"🇬🇩"},{key:"guatemala",code:"GT",icon:"🇬🇹"},{key:"haiti",code:"HT",icon:"🇭🇹"},{key:"honduras",code:"HN",icon:"🇭🇳"},{key:"jamaica",code:"JM",icon:"🇯🇲"},{key:"mexico",code:"MX",icon:"🇲🇽"},{key:"montserrat",code:"MS",icon:"🇲🇸"},{key:"nicaragua",code:"NI",icon:"🇳🇮"},{key:"panama",code:"PA",icon:"🇵🇦"},{key:"puerto_rico",code:"PR",icon:"🇵🇷"},{key:"st_kitts_nevis",code:"KN",icon:"🇰🇳"},{key:"st_lucia",code:"LC",icon:"🇱🇨"},{key:"st_pierre_miquelon",code:"PM",icon:"🇵🇲"},{key:"st_vincent_grenadines",code:"VC",icon:"🇻🇨"},{key:"trinidad_tobago",code:"TT",icon:"🇹🇹"},{key:"turks_caicos_islands",code:"TC",icon:"🇹🇨"},{key:"united_states",code:"US",icon:"🇺🇸"},{key:"british_virgin_islands",code:"VG",icon:"🇻🇬"},{key:"us_virgin_islands",code:"VI",icon:"🇻🇮"}],[{key:"albania",code:"AL",icon:"🇦🇱"},{key:"andorra",code:"AD",icon:"🇦🇩"},{key:"austria",code:"AT",icon:"🇦🇹"},{key:"belarus",code:"BY",icon:"🇧🇾"},{key:"belgium",code:"BE",icon:"🇧🇪"},{key:"bosnia_herzegovina",code:"BA",icon:"🇧🇦"},{key:"bulgaria",code:"BG",icon:"🇧🇬"},{key:"croatia",code:"HR",icon:"🇭🇷"},{key:"cyprus",code:"CY",icon:"🇨🇾"},{key:"czech_republic",code:"CZ",icon:"🇨🇿"},{key:"denmark",code:"DK",icon:"🇩🇰"},{key:"estonia",code:"EE",icon:"🇪🇪"},{key:"faroe_islands",code:"FO",icon:"🇫🇴"},{key:"finland",code:"FI",icon:"🇫🇮"},{key:"france",code:"FR",icon:"🇫🇷"},{key:"germany",code:"DE",icon:"🇩🇪"},{key:"gibraltar",code:"GI",icon:"🇬🇮"},{key:"greece",code:"GR",icon:"🇬🇷"},{key:"guernsey",code:"GG",icon:"🇬🇬"},{key:"holy_see_vatican_city",code:"VA",icon:"🇻🇦"},{key:"hungary",code:"HU",icon:"🇭🇺"},{key:"iceland",code:"IS",icon:"🇮🇸"},{key:"ireland",code:"IE",icon:"🇮🇪"},{key:"isle_of_man",code:"IM",icon:"🇮🇲"},{key:"italy",code:"IT",icon:"🇮🇹"},{key:"jersey",code:"JE",icon:"🇯🇪"},{key:"latvia",code:"LV",icon:"🇱🇻"},{key:"liechtenstein",code:"LI",icon:"🇱🇮"},{key:"lithuania",code:"LT",icon:"🇱🇹"},{key:"luxembourg",code:"LU",icon:"🇱🇺"},{key:"malta",code:"MT",icon:"🇲🇹"},{key:"moldova",code:"MD",icon:"🇲🇩"},{key:"monaco",code:"MC",icon:"🇲🇨"},{key:"montenegro",code:"ME",icon:"🇲🇪"},{key:"netherlands",code:"NL",icon:"🇳🇱"},{key:"north_macedonia",code:"MK",icon:"🇲🇰"},{key:"norway",code:"NO",icon:"🇳🇴"},{key:"poland",code:"PL",icon:"🇵🇱"},{key:"portugal",code:"PT",icon:"🇵🇹"},{key:"romania",code:"RO",icon:"🇷🇴"},{key:"russia",code:"RU",icon:"🇷🇺"},{key:"san_marino",code:"SM",icon:"🇸🇲"},{key:"serbia",code:"RS",icon:"🇷🇸"},{key:"slovakia",code:"SK",icon:"🇸🇰"},{key:"slovenia",code:"SI",icon:"🇸🇮"},{key:"spain",code:"ES",icon:"🇪🇸"},{key:"svalbard_jan_mayen",code:"SJ",icon:"🇸🇯"},{key:"sweden",code:"SE",icon:"🇸🇪"},{key:"switzerland",code:"CH",icon:"🇨🇭"},{key:"ukraine",code:"UA",icon:"🇺🇦"},{key:"united_kingdom",code:"GB",icon:"🇬🇧"}],[{key:"afghanistan",code:"AF",icon:"🇦🇫"},{key:"armenia",code:"AM",icon:"🇦🇲"},{key:"azerbaijan",code:"AZ",icon:"🇦🇿"},{key:"bahrain",code:"BH",icon:"🇧🇭"},{key:"bangladesh",code:"BD",icon:"🇧🇩"},{key:"bhutan",code:"BT",icon:"🇧🇹"},{key:"brunei",code:"BN",icon:"🇧🇳"},{key:"cambodia",code:"KH",icon:"🇰🇭"},{key:"china",code:"CN",icon:"🇨🇳"},{key:"christmas_island",code:"CX",icon:"🇨🇽"},{key:"cocos_keeling_islands",code:"CC",icon:"🇨🇨"},{key:"georgia",code:"GE",icon:"🇬🇪"},{key:"hong_kong_sar_china",code:"HK",icon:"🇭🇰"},{key:"india",code:"IN",icon:"🇮🇳"},{key:"indonesia",code:"ID",icon:"🇮🇩"},{key:"iran",code:"IR",icon:"🇮🇷"},{key:"iraq",code:"IQ",icon:"🇮🇶"},{key:"israel",code:"IL",icon:"🇮🇱"},{key:"japan",code:"JP",icon:"🇯🇵"},{key:"jordan",code:"JO",icon:"🇯🇴"},{key:"kazakhstan",code:"KZ",icon:"🇰🇿"},{key:"north_korea",code:"KP",icon:"🇰🇵"},{key:"south_korea",code:"KR",icon:"🇰🇷"},{key:"kuwait",code:"KW",icon:"🇰🇼"},{key:"kyrgyzstan",code:"KG",icon:"🇰🇬"},{key:"laos",code:"LA",icon:"🇱🇦"},{key:"lebanon",code:"LB",icon:"🇱🇧"},{key:"macau_sar_china",code:"MO",icon:"🇲🇴"},{key:"malaysia",code:"MY",icon:"🇲🇾"},{key:"maldives",code:"MV",icon:"🇲🇻"},{key:"mongolia",code:"MN",icon:"🇲🇳"},{key:"myanmar_burma",code:"MM",icon:"🇲🇲"},{key:"nepal",code:"NP",icon:"🇳🇵"},{key:"oman",code:"OM",icon:"🇴🇲"},{key:"pakistan",code:"PK",icon:"🇵🇰"},{key:"palestinian_territories",code:"PS",icon:"🇵🇸"},{key:"philippines",code:"PH",icon:"🇵🇭"},{key:"qatar",code:"QA",icon:"🇶🇦"},{key:"saudi_arabia",code:"SA",icon:"🇸🇦"},{key:"singapore",code:"SG",icon:"🇸🇬"},{key:"sri_lanka",code:"LK",icon:"🇱🇰"},{key:"syria",code:"SY",icon:"🇸🇾"},{key:"taiwan",code:"TW",icon:"🇹🇼"},{key:"tajikistan",code:"TJ",icon:"🇹🇯"},{key:"thailand",code:"TH",icon:"🇹🇭"},{key:"timorleste",code:"TL",icon:"🇹🇱"},{key:"turkey",code:"TR",icon:"🇹🇷"},{key:"turkmenistan",code:"TM",icon:"🇹🇲"},{key:"united_arab_emirates",code:"AE",icon:"🇦🇪"},{key:"uzbekistan",code:"UZ",icon:"🇺🇿"},{key:"vietnam",code:"VN",icon:"🇻🇳"},{key:"yemen",code:"YE",icon:"🇾🇪"}],[{key:"algeria",code:"DZ",icon:"🇩🇿"},{key:"angola",code:"AO",icon:"🇦🇴"},{key:"benin",code:"BJ",icon:"🇧🇯"},{key:"botswana",code:"BW",icon:"🇧🇼"},{key:"burkina_faso",code:"BF",icon:"🇧🇫"},{key:"burundi",code:"BI",icon:"🇧🇮"},{key:"cabo_verde",code:"CV",icon:"🇨🇻"},{key:"cameroon",code:"CM",icon:"🇨🇲"},{key:"central_african_republic",code:"CF",icon:"🇨🇫"},{key:"chad",code:"TD",icon:"🇹🇩"},{key:"comoros",code:"KM",icon:"🇰🇲"},{key:"congo_kinshasa",code:"CD",icon:"🇨🇩"},{key:"congo_brazzaville",code:"CG",icon:"🇨🇬"},{key:"cte_divoire",code:"CI",icon:"🇨🇮"},{key:"djibouti",code:"DJ",icon:"🇩🇯"},{key:"egypt",code:"EG",icon:"🇪🇬"},{key:"equatorial_guinea",code:"GQ",icon:"🇬🇶"},{key:"eritrea",code:"ER",icon:"🇪🇷"},{key:"eswatini",code:"SZ",icon:"🇸🇿"},{key:"ethiopia",code:"ET",icon:"🇪🇹"},{key:"gabon",code:"GA",icon:"🇬🇦"},{key:"gambia",code:"GM",icon:"🇬🇲"},{key:"ghana",code:"GH",icon:"🇬🇭"},{key:"guinea",code:"GN",icon:"🇬🇳"},{key:"guineabissau",code:"GW",icon:"🇬🇼"},{key:"kenya",code:"KE",icon:"🇰🇪"},{key:"lesotho",code:"LS",icon:"🇱🇸"},{key:"liberia",code:"LR",icon:"🇱🇷"},{key:"libya",code:"LY",icon:"🇱🇾"},{key:"madagascar",code:"MG",icon:"🇲🇬"},{key:"malawi",code:"MW",icon:"🇲🇼"},{key:"mali",code:"ML",icon:"🇲🇱"},{key:"mauritania",code:"MR",icon:"🇲🇷"},{key:"mauritius",code:"MU",icon:"🇲🇺"},{key:"mayotte",code:"YT",icon:"🇾🇹"},{key:"mozambique",code:"MZ",icon:"🇲🇿"},{key:"namibia",code:"NA",icon:"🇳🇦"},{key:"niger",code:"NE",icon:"🇳🇪"},{key:"nigeria",code:"NG",icon:"🇳🇬"},{key:"runion",code:"RE",icon:"🇷🇪"},{key:"rwanda",code:"RW",icon:"🇷🇼"},{key:"so_tom_prncipe",code:"ST",icon:"🇸🇹"},{key:"senegal",code:"SN",icon:"🇸🇳"},{key:"seychelles",code:"SC",icon:"🇸🇨"},{key:"sierra_leone",code:"SL",icon:"🇸🇱"},{key:"somalia",code:"SO",icon:"🇸🇴"},{key:"south_africa",code:"ZA",icon:"🇿🇦"},{key:"south_sudan",code:"SS",icon:"🇸🇸"},{key:"sudan",code:"SD",icon:"🇸🇩"},{key:"tanzania",code:"TZ",icon:"🇹🇿"},{key:"togo",code:"TG",icon:"🇹🇬"},{key:"tunisia",code:"TN",icon:"🇹🇳"},{key:"uganda",code:"UG",icon:"🇺🇬"},{key:"zambia",code:"ZM",icon:"🇿🇲"},{key:"zimbabwe",code:"ZW",icon:"🇿🇼"}],[{key:"argentina",code:"AR",icon:"🇦🇷"},{key:"bolivia",code:"BO",icon:"🇧🇴"},{key:"brazil",code:"BR",icon:"🇧🇷"},{key:"chile",code:"CL",icon:"🇨🇱"},{key:"colombia",code:"CO",icon:"🇨🇴"},{key:"ecuador",code:"EC",icon:"🇪🇨"},{key:"falkland_islands",code:"FK",icon:"🇫🇰"},{key:"guyana",code:"GY",icon:"🇬🇾"},{key:"paraguay",code:"PY",icon:"🇵🇾"},{key:"peru",code:"PE",icon:"🇵🇪"},{key:"suriname",code:"SR",icon:"🇸🇷"},{key:"trinidad_tobago",code:"TT",icon:"🇹🇹"},{key:"uruguay",code:"UY",icon:"🇺🇾"},{key:"venezuela",code:"VE",icon:"🇻🇪"}],[{key:"american_samoa",code:"AS",icon:"🇦🇸"},{key:"australia",code:"AU",icon:"🇦🇺"},{key:"cook_islands",code:"CK",icon:"🇨🇰"},{key:"fiji",code:"FJ",icon:"🇫🇯"},{key:"french_polynesia",code:"PF",icon:"🇵🇫"},{key:"guam",code:"GU",icon:"🇬🇺"},{key:"kiribati",code:"KI",icon:"🇰🇮"},{key:"marshall_islands",code:"MH",icon:"🇲🇭"},{key:"micronesia",code:"FM",icon:"🇫🇲"},{key:"nauru",code:"NR",icon:"🇳🇷"},{key:"new_caledonia",code:"NC",icon:"🇳🇨"},{key:"new_zealand",code:"NZ",icon:"🇳🇿"},{key:"niue",code:"NU",icon:"🇳🇺"},{key:"norfolk_island",code:"NF",icon:"🇳🇫"},{key:"northern_mariana_islands",code:"MP",icon:"🇲🇵"},{key:"palau",code:"PW",icon:"🇵🇼"},{key:"papua_new_guinea",code:"PG",icon:"🇵🇬"},{key:"pitcairn_islands",code:"PN",icon:"🇵🇳"},{key:"samoa",code:"WS",icon:"🇼🇸"},{key:"solomon_islands",code:"SB",icon:"🇸🇧"},{key:"tokelau",code:"TK",icon:"🇹🇰"},{key:"tonga",code:"TO",icon:"🇹🇴"},{key:"tuvalu",code:"TV",icon:"🇹🇻"},{key:"vanuatu",code:"VU",icon:"🇻🇺"},{key:"wallis_futuna",code:"WF",icon:"🇼🇫"}]].flat(),od=zt.reduce((A,a)=>(A[a.code]=a,A),{}),sd=_a.reduce((A,a)=>(A[a.key]=a.countries.map(e=>od[e]).filter(Boolean),A),{}),rd="_modal_1a3l4_1",cd="_container_1a3l4_5",ld="_header_1a3l4_10",dd="_closeBtn_1a3l4_17",ud="_searchBox_1a3l4_24",_d="_searchIcon_1a3l4_43",md="_tabs_1a3l4_52",gd="_tabs_hidden_1a3l4_57",pd="_countryContainer_1a3l4_60",hd="_countryList_1a3l4_64",fd="_countryGroup_1a3l4_69",bd="_countryItem_1a3l4_77",yd="_active_1a3l4_89",kd="_noDataTips_1a3l4_103",LA={modal:rd,container:cd,header:ld,closeBtn:dd,searchBox:ud,searchIcon:_d,tabs:md,tabs_hidden:gd,countryContainer:pd,countryList:hd,countryGroup:fd,countryItem:bd,active:yd,noDataTips:kd},vd=A=>{var v;const a=nA(),[e,i]=o.useState(""),[n]=Ia(),s=o.useMemo(()=>zt.filter(c=>a.formatMessage({id:c.key}).toLowerCase().includes(e.toLowerCase())),[a,e]),r=o.useMemo(()=>!!e,[e]),m=r&&s.length===0,u=o.useCallback(()=>t.jsxs("div",{className:LA.searchBox,children:[t.jsx("i",{className:LA.searchIcon}),t.jsx("input",{type:"text",placeholder:a.formatMessage({id:"search_for_a_country_or_region"}),onChange:c=>i(c.target.value)})]}),[a]),b=((v=_a.find(c=>c.countries.includes(n)))==null?void 0:v.key)||_a[0].key,[y,h]=o.useState(b),_=c=>{h(c)},f=c=>{A.onChange(c)};return t.jsxs("div",{className:LA.container,children:[t.jsxs("div",{className:LA.header,children:[t.jsx(u,{}),t.jsx("div",{className:LA.closeBtn,onClick:A.onClose})]}),t.jsxs("div",{className:LA.countryContainer,children:[t.jsx(Fi,{className:N(LA.tabs,{[LA.tabs_hidden]:r}),active:y,items:_a.map(c=>{const d=sd[c.key];return{key:c.key,label:a.formatMessage({id:c.key}),children:t.jsx("div",{className:LA.countryList,children:d.map(l=>t.jsx("div",{className:N(LA.countryItem,n.toLowerCase()===(l==null?void 0:l.code.toLowerCase())&&LA.active),onClick:()=>f(l.code),children:a.formatMessage({id:l==null?void 0:l.key})},l==null?void 0:l.key))})}}),onChange:_}),m&&t.jsx("p",{className:LA.noDataTips,children:a.formatMessage({id:"no_search_results_found_please_try_a_different_keyword"})}),r?t.jsx("div",{className:LA.countryGroup,children:s.map((c,d)=>t.jsx("div",{className:N(LA.countryItem,n.toLowerCase()===(c==null?void 0:c.code.toLowerCase())&&LA.active),onClick:()=>f(c.code),children:a.formatMessage({id:c==null?void 0:c.key})},d))}):null]})]})},wd=({isOpen:A,onClose:a,onChange:e})=>t.jsx(oa,{isOpen:A,backdropDismiss:!0,className:LA.modal,position:"bottom",onClose:a,children:t.jsx(vd,{onClose:a,onChange:e})}),Sd="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAOVBMVEUAAACZmZmZmZmYmJiXl5eZmZmampqZmZmZmZmYmJifn5+bm5uZmZmZmZmYmJiampqampqfn5+ZmZlihg+8AAAAEnRSTlMA3++AIHC/YJAwEH+vUECfbyDKjd/6AAABQklEQVRIx91V25bDIAg0IpKbZpv//9gldk9pNJAmL3tO502ckVEU3X9gIATANHxEHjH69Q8+0nhGnwpb4Jdg8X+e9G7uAfq5ew5QpYe4ESKGV4CKJgaFv812eR+kEgwqv2/jIIqG77M7QPKHisj8QSkLK2JzPpw4OQWZJ7E1BE5Fz+n3JVzYpjNQrzeyS7IEuUpBegI5E9wPyRbg/qDYUbAFgT3JaBBH1ralTGld5zPB/O4a5RJZpZBdg1k14VwW3LdEn2063TlWKdzjSuHkprQQ17G9KbYjqq53NhPULwjOH9DiqhdklWJq+wauhqkkTeBem5FGlo74mfk+KK1yOrxEMtEqOqqW34JeU8S1SF7pH1giMYCicNi1H4pHnlAVYam/LOACWwo30vuniIUuCgVDQgCkoflYyF0BXBU4Ivct+AW8jBZSbwgQrgAAAABJRU5ErkJggg==",Td="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAAQlBMVEUAAAAAAADFxcUAAAAAAAAAAAAAAAAAAAAAAAD39/cAAAAAAAAAAAAAAAAAAACqqqq5ubm3t7cAAAAAAACrq6v///9TnUgfAAAAFXRSTlOAAM9wCF84eED3SBAgMCi/x8doGMBfL1JYAAABwElEQVRYw83Y2XKEIBCF4TM2iwI6W+b9XzWEqmQWWhulL/JfWjVfuYwI4LSeubtgiZCjmw0unTbCquKsx2d2jgJUK+AqVjtkRo+NaIwcJDE81QAlQkO0CJAJaCyYLSgSmqO4DiWPHfllDXLYmeOhEbsbOcjhQK6GFhwqfULRH4N8fIcM4WBk3qCAw4VXaEFH6QWiHojMHzSiq/EXitQHeVOgckL9pwT+Dp2n6Qq0HvemQDOqro/cgKrh5/gXqlyBLKqmR5FY5zGhyv5AESxUpNrhIZgMzdyteFRScUoXdhTA88q4Hw3cISabIUCSZAfeIEGUZAe4w0GWZCczAbIkO5mxkCXZycwNsiQ7IBBkSXaejCwN6Gp4Ov8Dar80vZtNOo//pvaHVHtF9F7au84wkmC8xsAGzaHWoe66Nfh/oW7OkNH4HEXNDyR3beetT/aFubICcc/tMk1noPU4aU9rjO9zKGpP/U6Gehx6mdWmHmjRmrDrLSH0FzU9t2nRWvipL0X1F8elZd9yPeluIOhvaXy0UAtDqWH/aBQpP5qmjag4kszIUGle3xpzZt9mXWQsXykyVEou2BshR2SDuz+Vum8mTii32NkxLgAAAABJRU5ErkJggg==",xd="_searchBar_80a07_1",Cd="_searchBar__icon_80a07_10",Pd="_searchBar__input_80a07_15",zd="_searchBar__clear_80a07_30",la={searchBar:xd,searchBar__icon:Cd,searchBar__input:Pd,searchBar__clear:zd},jd=o.forwardRef(({className:A,onBlur:a,onChange:e},i)=>{const n=nA(),s=o.useRef(null),[r,m]=o.useState("");return o.useImperativeHandle(i,()=>s.current),t.jsxs("div",{className:N(A,la.searchBar),children:[t.jsx(xA,{className:la.searchBar__icon,url:Sd}),t.jsx("input",{ref:s,className:la.searchBar__input,type:"text",value:r,placeholder:n.formatMessage({id:"search"}),onBlur:a,onChange:u=>{m(u.target.value),e(u.target.value)}}),r.length>0?t.jsx(xA,{className:la.searchBar__clear,url:Td,onClick:u=>{var b;u.preventDefault(),(b=s.current)==null||b.blur(),m(""),e("")}}):null]})}),Bd="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABwCAMAAADWkIWRAAAAllBMVEUAAAD/34D/3oP/3YH/34P/3oL/3oP/34ImJiZqX0P/3YL/34f/34D/3oL///8mJibk5OR3d3fkx3eSkpJBQUHx8fHJyck0NDRBPTHJsGtcVD2goKDx031cXFxPT080MSx3a0mtmWCSglTX19eurq5PSTetra2FhYWFd067u7ugjVpqamrXu3G7pWXWvHFqYEOTglS7pWa6XlHuAAAADXRSTlMAEM+QQN+fj9eYjyAg4sEwUQAABH5JREFUaN7tmmt3ojAQhlltu1eYJIQ7gogUL+129///uTUojhKFJOA5+6Hvh562euYhM0nmopbQ09dne3LNXn5aR335aj9IP7409mf2wzQThIHnH7kGy/plP1RP1ov9UM2tmf1QPVv2g/UJ+AR8Av4fAHsvojcbNS0gXYUgFC6ZPSEAzedwlkAwxqYERMI8Kq9AqPoo2DSAFdxVXBgBhu2jwmIsIIIBhWwcIIRBrcYAVqCgipkBcAHDbjIFvIOa8jdDwAZGEYYBMSgTmBEgB2WFzAQAGooNAAx0tNIHhKClN11AAXqKdQExaCrSAzDQVawFQA/xNTVYwjBg2dr3HCcZsZGsgRAExDmIdBeR1Wu/9LeUw4U+1AG4SV3nKLLeH43xjCal57RKAgTEWoCTg5xLEeI5XRFcxNIAkDiD8s8LYAYA4gyL4h7SjsHCUdDWDNAUWGsVAEGA9jnwVQCeCSCNMQSDwmOmDqhAGwA7DUABBoA8VQBgAOpaLwYBQKEOqA62fQQo7CJea112ofBOpgNYv2pdFQDQnNBXVQB3EKC6AmWAf7BPHgxYNElpow6IAbaagBpgpw7YiEzjih/KgEwrJ0cAXBUgvE+bSGsctDQH8LUAFGPcD8DmiesAfOXCC5cgRFUACQDluAAFADawexVAjR2CTsL5g0XFgBbY4+hV1yIrewqAACsK3R5NtWwJI7MmcKdSVuAG1QeknZzmbY9/+Vf/5ACRNgB9RK+iyV9Lsl1A4F1t0tA2BUTHpIaAVt5VaVoYAHAJGQLaIn5xAaW4ABMAywFqB0USSl3/+hRDZATAjQRuf1m6GTezW/YRtgFAZY8DpNV9QoJXkBkACZzI5sli3MwOCc3YiHYQnhvgOG0EAC9W2L96Z+s+DQDjOxpgs1PTnO1r161pBo3iyB4BGB6gvk863v8tnh+Ni1/ZpICoqVbLbeK6ybr0PDzAYwE44Qw6WayYFLBsbj0UB9hMCfidd0qYGtP8+IMW7WKpXyubfbqK0pEAVizb0aAr30NC1bJ4MwOk0SrGyWyA5YU8x8lxKaqA9OAVFKeJ56BwgpQBKi6YMoBtzo8uHnPbP8rBlSyZEiDd4IOXYsPT3pqIO+WaZogYBPzNj4l9ffSKK/ZPX39Zt/7Cj416AZvGMS7ezEFPecfPqQhrjo0EkNIX1JchdaUtKr10jajYfQALhes77X12j+DK9b3PMY3eAKTCfka6jg6QINmX3pwJQnoH8CFyr7zjS0FYkBspPyjl0YtwU3wbsLrXM5Uc0z6mfLQvNzyrWwB2HFSjpOHrIvFLQsg2OdiQnInwDLPRFSBsB9X3AorqbOUbQatkQNE/xSQ0uDZPevta2EmAUOSsPnnnG4HTtTfUd+ZpB1CoDdDIIQqe0jh71wFUeCOMES4hvgYwjMAUuhhdWM9to8GdCRW0Z2FmzbGfnFC09dGL9WQLjfaQPOLJbaGfljU/lZ7llAByKl7np68ivovSc7Tk2nJ2/LLjXNxz2bQALk7CXNgX+vX9G+ynBezh2/cnYfsf1sRXzNKe4TIAAAAASUVORK5CYII=",Md="_paperListItem_1by4l_1",Id="_paperListItem__icon_1by4l_10",Ed="_paperListItem__content_1by4l_17",Nd="_paperListItem__title_1by4l_24",Rd="_paperListItem__subtitle_1by4l_29",Xe={paperListItem:Md,paperListItem__icon:Id,paperListItem__content:Ed,paperListItem__title:Nd,paperListItem__subtitle:Rd},Ot=({obj:A,onClick:a,isSizeType:e=!1})=>{const i=oe();return t.jsxs("div",{className:Xe.paperListItem,onClick:async()=>{uA()&&i.event("snapid_page_button_clk",{clk:"choose",project:"id_photo",name:A.name,size_value:A.sizeValue}),a()},children:[t.jsx(xA,{className:Xe.paperListItem__icon,url:Bd}),t.jsxs("div",{className:Xe.paperListItem__content,children:[t.jsx("div",{className:Xe.paperListItem__title,children:e?A.sizeString:A.name}),e?null:t.jsx("div",{className:Xe.paperListItem__subtitle,children:A.sizeString})]}),t.jsx("div",{})]},A.key)},Dd="_content_p3iz8_1",Ld="_container_p3iz8_5",Od="_appBar_p3iz8_11",Gd="_setting_p3iz8_15",Fd="_header_p3iz8_19",Ud="_countryBtn_p3iz8_27",Hd="_active_p3iz8_50",Kd="_tabs_p3iz8_56",qd="_tabs_hidden_p3iz8_61",Qd="_searchBar_p3iz8_64",Vd="_searchResultContainer_p3iz8_69",Jd="_list_p3iz8_76",Wd="_noData_p3iz8_80",Yd="_actionSheet_p3iz8_87",Zd="_customSizeOption_p3iz8_109",Xd="_customSizeIcon_p3iz8_120",$d="_customSizeText_p3iz8_132",OA={content:Dd,container:Ld,appBar:Od,setting:Gd,header:Fd,countryBtn:Ud,active:Hd,tabs:Kd,tabs_hidden:qd,searchBar:Qd,searchResultContainer:Vd,list:Jd,noData:Wd,actionSheet:Yd,customSizeOption:Zd,customSizeIcon:Xd,customSizeText:$d},Wa=()=>{const A=nA(),{push:a}=UA(),e=()=>{a("/IDPhoto/customSize")};return t.jsxs("div",{className:OA.customSizeOption,onClick:e,children:[t.jsx("div",{className:OA.customSizeIcon}),t.jsx("div",{className:OA.customSizeText,children:A.formatMessage({id:"custom_size"})})]})},Au=fe(()=>{const A=o.useContext(Pt),a=UA(),e=oe();BA(()=>{uA()&&e.event("snapid_page_event",{page_id:"list_page_view",project:"id_photo"})},[e]);const i=Do(),n=o.useRef(null),s=nA(),{setWidth:r,setHeight:m,setPhotoName:u,setSizeItem:b,setSizeDisplay:y,setUnit:h}=NA(),[_,f]=o.useState(""),v=VA(_),[c,d]=o.useState("common"),[l,g]=o.useState([]),p=VA(D=>{const V=D.toLowerCase().trim();if(f(V),V){const U={},F=i.reduce((hA,H)=>{if(!H.data||H.data.length<1)return hA;const J=H.data.filter(Z=>{var Te;const{name:vA,sizeString:eA}=Z,aA=vA.toLowerCase().includes(V),$=V.replace("*","x").replace(/\s/g,""),rA=(Te=eA.match(/\d+/g))==null?void 0:Te.slice(0,2),wA=["x","*",""].some(DA=>(rA==null?void 0:rA.join(DA).includes($))||(rA==null?void 0:rA.reverse().join(DA).includes($))),MA=!!(aA||wA)&&!U[Z.key];return MA&&(U[Z.key]=!0),MA});return[...hA,...J]},[]);g(F)}}),k=o.useMemo(()=>_n(D=>{p.current(D)},300),[p]);o.useEffect(()=>{v.current&&p.current(v.current)},[i,v,p]);const[S,P]=Ki(),x=He(async D=>{const[V,U]=D.size;r(V),m(U),u(D.name),b(D),h("mm"),y(Ba({width:V,height:U,unit:kA.mm})),P()},[a,P]),[C,T]=Ia(),[B,w]=o.useState(!1),I=Me(),E=async D=>{try{I.loading(),T(D),await Ea.getState().initiate(),I.hide(),w(!1)}catch(V){I.error("Error.")}},W=o.useMemo(()=>{var D;return((D=zt.find(V=>V.code===C))==null?void 0:D.icon)||"🇺🇸"},[C]),[L,z]=o.useState(0);o.useEffect(()=>{const D=Aa.addListener("keyboardDidShow",U=>{console.log(">>",U),z(U.keyboardHeight)}),V=Aa.addListener("keyboardDidHide",()=>{z(0)});return()=>{D.remove(),V.remove()}},[]);const Y=o.useMemo(()=>i.map(D=>{const V=D.key==="size";return{key:D.key,label:D.name,children:t.jsxs("div",{className:OA.list,children:[V&&t.jsx(Wa,{}),D.data.map(U=>t.jsx(Ot,{obj:U,onClick:()=>x(U),isSizeType:V},U.key))]})}}),[i,x]);return t.jsxs(t.Fragment,{children:[t.jsx(se,{className:OA.appBar,backable:!0,title:s.formatMessage({id:"IDPhoto"})}),t.jsxs(ie,{ref:A==null?void 0:A.onContentChange,"data-safe-area-bottom":!0,fullscreen:!0,scrollEvents:!0,className:OA.content,children:[t.jsxs("div",{className:OA.container,children:[t.jsxs("div",{className:OA.header,children:[t.jsx(jd,{ref:n,className:OA.searchBar,onBlur:()=>{z(0)},onChange:k}),t.jsxs("div",{className:N(OA.countryBtn,B&&OA.active),onClick:()=>{w(!0)},children:[t.jsx("span",{children:W}),t.jsx("i",{})]})]}),t.jsx(Fi,{className:N(OA.tabs,{[OA.tabs_hidden]:_}),active:c,items:Y,onChange:d}),_&&t.jsx("div",{className:OA.searchResultContainer,children:l.length===0?t.jsx(t.Fragment,{children:t.jsxs("div",{className:OA.noData,onTouchStart:()=>{var D;(D=n.current)==null||D.blur()},children:[t.jsx("p",{style:{marginBottom:16},children:s.formatMessage({id:"no_results_found_please_try_a_different_keyword_or_customize_your_own_size"})}),t.jsx(Wa,{})]})}):t.jsxs("div",{className:OA.list,children:[l.map(D=>t.jsx(Ot,{obj:D,onClick:()=>x(D)},D.key)),t.jsx(Wa,{})]})}),t.jsx("div",{style:{height:L}}),t.jsx(wd,{isOpen:B,onClose:()=>w(!1),onChange:E})]}),S]})]})}),eu="_container_1whj2_1",au="_graduatedScaleGroup_1whj2_28",tu="_graduatedScaleItem_1whj2_34",iu="_isBold_1whj2_42",nu="_graduatedScalePointer_1whj2_47",ou="_graduatedScalePointerContainer_1whj2_68",su="_graduatedScalePointerBtn_1whj2_77",Fe={container:eu,graduatedScaleGroup:au,graduatedScaleItem:tu,isBold:iu,graduatedScalePointer:nu,graduatedScalePointerContainer:ou,graduatedScalePointerBtn:su},qi=90,Qi=10,Ya=qi*Qi;function ru(A,a){const{onChange:e,onTouchMove:i}=A,n=o.useMemo(()=>Array.from({length:qi+1}).map((_,f)=>f),[]),s=o.useRef(null),[r,m]=o.useState(0),u=o.useRef(),b=o.useMemo(()=>parseFloat((-r/10).toFixed(1)),[r]),y=o.useMemo(()=>Math.abs(b)>0,[b]),h=()=>{m(0)};return o.useImperativeHandle(a,()=>({reset:h})),o.useEffect(()=>{e==null||e(b)},[b,e]),t.jsxs("div",{className:N(Fe.container,A.className),onTouchStart:_=>{u.current=_.touches[0].clientX},onTouchMove:_=>{_.stopPropagation(),i==null||i();const f=_.touches[0].clientX,v=f-(u.current||0);u.current=f,m(c=>{const d=c+v,l=Ya/2;return d>l?l:d<-l?-l:d})},onTouchEnd:()=>{Math.abs(b)<.2&&h()},children:[t.jsx("div",{className:Fe.graduatedScaleGroup,ref:s,style:{width:Ya,marginLeft:-Ya/2,transform:`translateX(${r}px)`},children:n.map(_=>t.jsx("i",{style:{left:_*Qi},className:N(Fe.graduatedScaleItem,_%5===0&&Fe.isBold)},_))}),t.jsxs("div",{className:Fe.graduatedScalePointerContainer,children:[t.jsx("div",{style:{opacity:y?1:0},className:Fe.graduatedScalePointerBtn,onClick:()=>{var _;h(),(_=A.onReset)==null||_.call(A)},children:t.jsxs("span",{children:[b.toFixed(1),"°"]})}),t.jsx("div",{className:Fe.graduatedScalePointer})]})]})}const cu=o.forwardRef(ru),lu="_photoMask_jnrd9_1",du="_photoWrap_jnrd9_23",uu="_photoEdit_jnrd9_31",_u="_photoContainer_jnrd9_37",mu="_resetBtn_jnrd9_45",gu="_disabled_jnrd9_56",pu="_graduatedScale_jnrd9_59",Ue={photoMask:lu,photoWrap:du,photoEdit:uu,photoContainer:_u,resetBtn:mu,disabled:gu,graduatedScale:pu},dt=[11,64,25],Vi=o.forwardRef((A,a)=>{const{bgColor:e,photo:i,className:n,photoRatio:s=dt,idPhotoSize:r,allowWhiteEdge:m=!1,onFocus:u,onTouch:b,reset:y=!1,onReset:h}=A,_=VA(i),f=o.useRef(r),v=o.useRef(null),c=o.useRef(null),[d,l]=s,[g,p]=o.useState(!1),[k,S]=o.useState([]),[P,x]=o.useState([]),[C,T]=o.useState({x:0,y:0,scale:1,rotate:0}),[B,w]=o.useState({width:0,height:0}),[I,E]=o.useState(!1),[W,L]=o.useState([0,0]),[z,Y]=W,D=o.useRef(W),V=nA(),U=Me();o.useEffect(()=>{D.current=W},[W]);const F=o.useRef(null),[hA,H]=o.useState(!1),J=o.useMemo(()=>({topPoints:k,bottomPoints:P}),[k,P]),[Z,vA]=o.useState(!1),eA=VA(Z),aA=qa(),$=async j=>{const M=()=>{const R=j.getBoundingClientRect(),G=R.width,K=R.height;return G>150&&K>150?[G,K]:null};return new Promise(R=>{const G=M();G?R(G):aA.current=setTimeout(()=>{aA.current=void 0,$(j).then(K=>{R(K)})},100)})},rA=async j=>{const M=F.current;if(!M)return[0,0];const[R,G]=await $(M);if(R&&G){const K=R/G,[Q,PA]=j,TA=Q/PA;let gA=[0,0];if(K>TA){const cA=G;gA=[cA*TA,cA]}else{const cA=R,q=cA/TA;gA=[cA,q]}return gA}return[0,0]};o.useEffect(()=>{let j;return j=setTimeout(async()=>{j=void 0;const M=await rA(f.current);L(M),vA(!0)},0),()=>{j&&clearTimeout(j)}},[]),o.useEffect(()=>{eA.current&&rA(r).then(j=>{L(j)})},[r]);const RA=o.useCallback((j,M=0)=>{const[R,G]=D.current;if(!v.current)return{white:!1,info:j};const K=v.current.width,Q=v.current.height,PA=j.x+K*(1-j.scale)/2,TA=PA+K*j.scale,gA=j.y+Q*(1-j.scale)/2,cA=gA+Q*j.scale;if(PA>M||gA>M||TA+M<R||cA+M<G){const q={...j};return Q*j.scale>G&&K*j.scale>R?(Q*j.scale>G&&(gA>0&&(q.y=-(Q*(1-j.scale))/2),cA<G&&(q.y=G-Q*j.scale-Q*(1-j.scale)/2)),K*j.scale>R&&(PA>0&&(q.x=-(K*(1-j.scale))/2),TA<R&&(q.x=R-K*j.scale-K*(1-j.scale)/2))):(K/Q<R/G?q.scale=R/K*1.25:q.scale=G/Q*1.25,q.x=-(K*(1-q.scale))/2-(K*q.scale-R)/2,q.y=-(Q*(1-q.scale))/2-(Q*q.scale-G)/2,q.rotate=0),{white:!0,info:q}}else return{white:!1,info:j}},[]),wA=qa(),MA=o.useCallback((j,M=!1)=>{M&&v.current&&v.current.style&&(v.current.style.transition="transform 0.3s ease-in-out",wA.current=setTimeout(()=>{wA.current=void 0,v.current&&(v.current.style.transition="none")},300)),T(j)},[]),[Te,DA]=o.useState(!1),SA=qa(),CA=o.useCallback((j=!1)=>{var Ee;if(!J.topPoints.length||!J.bottomPoints.length)return;(Ee=Ie.current)==null||Ee.reset(),E(!1);const{topPoints:M,bottomPoints:R}=J,G={x:0,y:0,scale:1,rotate:0},K=6,Q=8,PA=M.reduce((le,de)=>{const[,Ne]=de,[,ue]=le;return Ne<ue?de:le},M[K]||[0,0]),TA=R.reduce((le,de)=>{const[,Ne]=de,[,ue]=le;return Ne>ue?de:le},R[Q]||[0,0]),gA=TA[1]-PA[1],q=Y*(l/100)/gA;G.scale=q;const AA=z/2,bA=Y*((d+l)/100),ye={x:AA,y:bA},[ce,Ce]=TA;if(B){const le=ce*q-(B.width*q-B.width)/2,de=Ce*q-(B.height*q-B.height)/2,Ne=ye.x-le,ue=ye.y-de;G.x=Ne,G.y=ue,MA(G,j),m?(DA(!0),u==null||u({hasWhite:!1})):SA.current=setTimeout(()=>{SA.current=void 0;const{info:We,white:Da}=RA(G);Da?(DA(!0),u==null||u({hasWhite:!0}),SA.current=setTimeout(()=>{SA.current=void 0,MA(We,j)},500)):(DA(!0),u==null||u({hasWhite:!1}))},0)}else DA(!0),u==null||u({hasWhite:!1})},[J,z,Y,d,l,B,MA,m,RA,u]),re=o.useCallback(async j=>{const M=await te(j);w({width:M.width,height:M.height}),v.current&&(v.current.width=M.width,v.current.height=M.height);const{bottomPoints:R=[],topPoints:G=[]}=await gt.detectSingleFace({source:ze(j),sourceType:"base64",topPoints:[27,28,29,30,31,32,33,34,35,36,37,38,39],bottomPoints:[8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]}).catch(()=>({sex:0,age:0,count:0,bottomPoints:[],topPoints:[]}));return{topPoints:Array.isArray(G)?G:[],bottomPoints:Array.isArray(R)?R:[]}},[]);o.useEffect(()=>{re(i).then(({topPoints:j,bottomPoints:M})=>{i===_.current&&(S(j),x(M),(!j.length||!M.length)&&WA.getState().present({message:V.formatMessage({id:"error2"}),actions:[{title:V.formatMessage({id:"ok"}),type:"primary"}]}))}).finally(()=>{i===_.current&&p(!0)})},[re,i]);const be=o.useRef(!1);o.useEffect(()=>{if(Z)if(g&&k.length&&P.length){const j=be.current;be.current=!0,CA(j)}else DA(!0)},[CA,k,P,Z,U,V]),o.useEffect(()=>{if(v.current&&v.current.style){let j=`translate(${C.x}px, ${C.y}px) scale(${C.scale}) rotate(${C.rotate}deg)`;hA&&(j+=" scaleX(-1)"),v.current.style.transform=j}},[C,hA]);const HA=o.useRef(),Oe=o.useCallback(()=>{var j;HA.current&&((j=HA.current)==null||j.destroy()),HA.current=new mn(c.current),HA.current.on("drag",M=>{T(R=>({...R,x:R.x+M.delta.deltaX,y:R.y+M.delta.deltaY}))}).on("pinch",M=>{T(R=>({...R,scale:R.scale*M.delta.scale}))}).on("touchend",()=>{E(!0),b==null||b(),T(M=>{if(m)return M;{const R=RA(M);return R.white?R.info:M}})})},[m,RA,b]),xe=o.useCallback(j=>{const M=document.createElement("canvas"),R=M.getContext("2d");if(!R||!v.current)return"";const G=2;M.width=z*G,M.height=Y*G,j&&(R.fillStyle=j,R.fillRect(0,0,M.width,M.height));const{width:K,height:Q}=v.current,{x:PA,y:TA,scale:gA,rotate:cA}=C,q=K*gA*G,AA=Q*gA*G,bA=-((gA-1)*K)/2+PA,ye=-((gA-1)*Q)/2+TA;R.save();const ce=Math.cos(cA*Math.PI/180),Ce=Math.sin(cA*Math.PI/180);return R.translate(q/2,AA/2),R.transform(ce,Ce,-Ce,ce,Math.floor(bA*G),Math.floor(ye*G)),R.translate(-(q/2),-(AA/2)),R.save(),hA&&(R.scale(-1,1),R.translate(-q,0)),R.drawImage(v.current,0,0,q,AA),M.toDataURL("image/png",1)},[hA,C,z,Y]),fA=o.useCallback(()=>{H(j=>!j)},[]),Ae=o.useCallback(()=>{},[]);o.useImperativeHandle(a,()=>({generate:xe,focus:CA,reversal:fA,clear:Ae}),[CA,xe,fA,Ae]);const Ie=o.useRef(null),KA=o.useCallback(()=>{I&&(h==null||h(),CA(!0))},[I,CA,h]),O=o.useCallback(j=>{T(M=>({...M,rotate:j}))},[]),X=()=>{MA({...C,rotate:0},!0)};return t.jsxs("div",{className:Ue.photoEdit,children:[t.jsx("div",{ref:F,className:N(Ue.photoWrap,n),children:z*Y>0&&t.jsxs("div",{ref:c,style:{width:z,height:Y,backgroundColor:e!=null?e:"rgba(251, 251, 251, 1)"},className:Ue.photoContainer,children:[t.jsx("img",{ref:v,style:{opacity:Te?1:0,transition:"opacity 1s ease-in-out"},src:i,crossOrigin:"anonymous",onLoad:Oe}),t.jsx("div",{className:Ue.photoMask,style:{backgroundPositionY:`${Y*(d/100)}px`,backgroundSize:`auto ${l}%`}})]})}),y&&t.jsx("div",{className:Ue.resetBtn,onClick:KA,children:t.jsx("span",{className:N({[Ue.disabled]:!I}),children:V.formatMessage({id:"reset"})})}),t.jsx(cu,{ref:Ie,className:Ue.graduatedScale,onChange:O,onReset:X,onTouchMove:()=>E(!0)})]})}),hu="_body_11151_1",fu="_container_11151_5",bu="_actionBar_11151_15",yu="_editContainer_11151_20",ku="_photoContainer_11151_30",vu="_selectContainer_11151_33",wu="_selectItem_11151_42",Su="_selectItemPhoto_11151_50",Tu="_active_11151_61",xu="_square_11151_76",Cu="_selectItemLabel_11151_79",Pu="_nextBtn_11151_84",zu="_resetBtn_11151_97",ju="_disabled_11151_106",Bu="_reversal_11151_109",Mu="_tips_11151_141",Iu="_appBar_11151_146",Eu="_switch_active_11151_169",_e={body:hu,container:fu,actionBar:bu,editContainer:yu,photoContainer:ku,selectContainer:vu,selectItem:wu,selectItemPhoto:Su,active:Tu,square:xu,selectItemLabel:Cu,nextBtn:Pu,resetBtn:zu,disabled:ju,reversal:Bu,tips:Mu,appBar:Iu,switch:"_switch_11151_149",switch_active:Eu},Nu=fe(()=>{const A=nA(),{sizeItem:a,originImg:e,setOriginImg:i,photoType:n,setPhotoType:s,photoPosition:r,setPhotoPosition:m,setSex:u,setAdjustImg:b}=NA(),y=a,[h,_]=o.useState(!0),f=UA(),[v,c]=o.useState(!1),d=o.useRef(null),[l,g]=o.useState(!1);BA(()=>{const w=we()<=0;return w&&EA.antiScreenshot({open:!0,antiBtnName:A.formatMessage({id:"ok"})}),()=>{w&&EA.antiScreenshot({open:!1})}},[]);const p=Me(),k=async()=>{try{let w;const I=async E=>{var W,L,z;if(GA.closeReceived().catch(()=>{}),(E==null?void 0:E.from)===0&&E.photo){let Y,D=new Promise((U,F)=>{Y=F});const V=JA.addListener("cancel",()=>{Y==null||Y(new Error("cancel"))});try{JA.showLoading({message:"",timeout:1e4,cancelText:A.formatMessage({id:"cancel"})}),s((W=E.type)!=null?W:0),m((L=E.position)!=null?L:0);const U=await Promise.race([je.fileToBase64({file:E.photo.source}),D]);if(!U.data)throw new Error("no data");let F=`data:image/${U.suffix||"jpeg"};base64,${U.data}`;F=await Promise.race([ya(F,"jpg"),D]),F=`data:image/jpeg;base64,${F}`,await te(F),i(F),_(!0),g(!1),(z=d.current)==null||z.clear(),setTimeout(()=>{JA.dismiss(),w==null||w.remove(),GA.close({animationTo:"right"})},300)}catch(U){(U==null?void 0:U.message)==="cancel"?GA.resume():(JA.showText({message:sA("network_error_info")}),GA.resume())}finally{V.remove()}}else w==null||w.remove(),f.popTo("/paper"),setTimeout(()=>{GA.close({animationTo:"left"})},100)};w=await GA.addListener("close",I)}catch(w){console.log(w)}},S=o.useCallback(()=>{v||c(!0)},[v]),P=o.useRef(!1),x=He(async()=>{var I;if(P.current)return;P.current=!0;const w=()=>{const{connected:E}=ia.getState();E?p.text(A.formatMessage({id:"error1"})):WA.getState().present({closeable:!0,title:A.formatMessage({id:"network_error"}),message:A.formatMessage({id:"network_error_message"}),actions:[{type:"primary",title:A.formatMessage({id:"network_retry"}),handler(){w()}},{title:A.formatMessage({id:"later"}),handler(){}}]})};try{p.loading();const E=((I=d.current)==null?void 0:I.generate())||"";if(!E)throw P.current=!1,new Error(A.formatMessage({id:"error1"}));const W=await gt.detectSingleFace({source:ze(E),sourceType:"base64"});if(W.count!==1){if(W.count>1)throw new Error(A.formatMessage({id:"error3"}));if(W.count===0)throw new Error(A.formatMessage({id:"error2"}));P.current=!1;return}p.hide(),u(W.sex===1?1:0),b(E),f.push(`/IDPhoto/make?sex=${W.sex===1?1:0}&beauty=0`),P.current=!1}catch(E){P.current=!1,(E==null?void 0:E.message)===A.formatMessage({id:"error1"})||(E==null?void 0:E.message)===A.formatMessage({id:"error2"})||(E==null?void 0:E.message)===A.formatMessage({id:"error3"})?p.error(E==null?void 0:E.message):w()}},[A,p,y,f,b,u]),C=()=>{var w,I;_(!1),k(),GA.open({fd:!0,maxWidth:1536,needControlBack:!0,position:r===0?0:1,animationFrom:"left",cameraSwitchTip:!1,report:{page_id:"takephoto_page_view",project:"id_photo",name:(w=y==null?void 0:y.name)!=null?w:"",size_value:(I=y==null?void 0:y.sizeValue)!=null?I:""}})},T=o.useRef(!1),B=o.useCallback(w=>{w.hasWhite?p.error(A.formatMessage({id:"whiteEdgeTips",defaultMessage:"Please provide a portrait photo with a wider range"})):T.current||(T.current=!0,p.text(A.formatMessage({id:"adjustTips"})))},[p,A]);return t.jsxs(Ke,{children:[t.jsx(se,{clickToTop:!0,className:_e.appBar,title:A.formatMessage({id:"adjust_title"}),backable:!0,onBack:C}),t.jsx(ie,{fullscreen:!0,className:_e.body,children:t.jsxs("div",{className:_e.container,children:[t.jsx("div",{className:_e.editContainer,children:h&&t.jsx(Vi,{ref:d,className:_e.photoContainer,photo:e||"",idPhotoSize:y.size,onTouch:S,photoRatio:y.ratio,reset:!0,allowWhiteEdge:!0,onFocus:B})}),t.jsxs("div",{className:_e.actionBar,children:[t.jsxs("div",{className:_e.reversal,children:[t.jsxs("span",{children:[A.formatMessage({id:"revert"}),":"]}),t.jsx("div",{className:N(_e.switch,{[_e.switch_active]:l}),onClick:()=>{var w;(w=d.current)==null||w.reversal(),g(I=>!I)}})]}),t.jsx("div",{className:_e.tips,children:A.formatMessage({id:"adjust_tips"})}),t.jsx("div",{className:_e.nextBtn,onClick:x,children:A.formatMessage({id:"next"})})]})]})})]})}),Ru="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_ar-DLmrkGfI.png",Du=Object.freeze(Object.defineProperty({__proto__:null,default:Ru},Symbol.toStringTag,{value:"Module"})),Lu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_de-Cn9PSCyR.png",Ou=Object.freeze(Object.defineProperty({__proto__:null,default:Lu},Symbol.toStringTag,{value:"Module"})),Gu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_en--2fHctwt.png",Fu=Object.freeze(Object.defineProperty({__proto__:null,default:Gu},Symbol.toStringTag,{value:"Module"})),Uu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_es-CT_1woc1.png",Hu=Object.freeze(Object.defineProperty({__proto__:null,default:Uu},Symbol.toStringTag,{value:"Module"})),Ku="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_fr-Bcp_4oH8.png",qu=Object.freeze(Object.defineProperty({__proto__:null,default:Ku},Symbol.toStringTag,{value:"Module"})),Qu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_id-CFFoAeDt.png",Vu=Object.freeze(Object.defineProperty({__proto__:null,default:Qu},Symbol.toStringTag,{value:"Module"})),Ju="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_it-6U-_eg39.png",Wu=Object.freeze(Object.defineProperty({__proto__:null,default:Ju},Symbol.toStringTag,{value:"Module"})),Yu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_ja-CGcw5mEN.png",Zu=Object.freeze(Object.defineProperty({__proto__:null,default:Yu},Symbol.toStringTag,{value:"Module"})),Xu="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_ko-CR0ujbyA.png",$u=Object.freeze(Object.defineProperty({__proto__:null,default:Xu},Symbol.toStringTag,{value:"Module"})),A_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_pt-ZD9T0t_H.png",e_=Object.freeze(Object.defineProperty({__proto__:null,default:A_},Symbol.toStringTag,{value:"Module"})),a_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_ru-DO6fFELs.png",t_=Object.freeze(Object.defineProperty({__proto__:null,default:a_},Symbol.toStringTag,{value:"Module"})),i_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_special_ja-O1qKWdzj.png",n_=Object.freeze(Object.defineProperty({__proto__:null,default:i_},Symbol.toStringTag,{value:"Module"})),o_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_special_th-CCWDVuUg.png",s_=Object.freeze(Object.defineProperty({__proto__:null,default:o_},Symbol.toStringTag,{value:"Module"})),r_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_th-BsOGJeHC.png",c_=Object.freeze(Object.defineProperty({__proto__:null,default:r_},Symbol.toStringTag,{value:"Module"})),l_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_tr-DL8MfxEu.png",d_=Object.freeze(Object.defineProperty({__proto__:null,default:l_},Symbol.toStringTag,{value:"Module"})),u_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_vi-n8hP5Z8E.png",__=Object.freeze(Object.defineProperty({__proto__:null,default:u_},Symbol.toStringTag,{value:"Module"})),m_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_zh-HK-Cr-Lkkjq.png",g_=Object.freeze(Object.defineProperty({__proto__:null,default:m_},Symbol.toStringTag,{value:"Module"})),p_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_zh-TW-hscEsmbc.png",h_=Object.freeze(Object.defineProperty({__proto__:null,default:p_},Symbol.toStringTag,{value:"Module"})),f_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/paper_poster_bottom_zh-TW-hscEsmbc.png",b_=Object.freeze(Object.defineProperty({__proto__:null,default:f_},Symbol.toStringTag,{value:"Module"})),y_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAANlBMVEUAAAD////////////////////////////////////////////////////////////////////xY8b8AAAAEXRSTlMAvxAg32BwQO+An6/PMJCPUFk9n3kAAAHoSURBVFjD7VfZjsMgDFyDOcKRbP7/Z1cEqdbGJibtaqVKnUc3nRkfCPP1wQfvAxN8ylAKwOaDeZrFw/4L4OwTNAF2ASk8T4MIUCLZuuPKbP1PEXw1j2rlvWObLlbFru3Nid9h9zhpysWDRqxGp4puhmc5PvWjn/0hs0zwaOYtTjG59lU2l604qq5kZ5vxpKmllny9FMPm50zul3OmzRNe2d6ED2ozGQTB7SKxZvmsfsx4OX/J2Jlj1vc+hGwK2qgND1hLjEUPRyCGw0UznDg3aEVVGFcIpVZ+u15/bklunCNDM/BUUEVCgYmD3Mwg6SWl5BRhVj0vJyz3x2txHpZrVwdFWmWnBpCIWC2SnHKUx4KI2MxnmajcJJK7UyisEZE0/hVR0fg50bx0vl3swWin0QmxF+1fBwNZVSL9KFBcJyLloB9anShTLfhoG4VIESarfp7IsbAiYY/iWemA7BTVL4Zt70DDDdHZ1y8G22l4GkiqsiXPL+cOq1+QZEm4su3SEdiVTdQc63jL4D5Xfa1RkVXBOr9o2ddXv0RN/Idl9O56rC/sKHoPwBd2xXyjMvK7C+v0orHuHfnx3DPVQ+zBlfh1WHpmxQKASM+s2y+2tBMUGs2VgxOLNy88jleAgpBTq9YHH7wNfgCsRSmnpXXHTAAAAABJRU5ErkJggg==",k_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/female_1-Dawuw1o6.png",v_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/female_2-3pdLdnV7.png",w_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/female_3-DCptaucN.png",S_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/female_4-tVVdMpMJ.png",T_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/female_5-D9aPv30x.png",x_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_5-D7HW3CG3.png",C_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_6-15FoOlIq.png",P_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_7-CTRejqke.png",z_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_8-DLFcLNP4.png",j_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_9-ByrZUoEp.png",B_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_1-MZfMAwRH.png",M_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_2-2VtCKvjD.png",I_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_3-CJv4tHRb.png",E_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_4-B9oA2btc.png",N_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_5-D7HW3CG3.png",R_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_6-15FoOlIq.png",D_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_7-CTRejqke.png",L_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_8-DLFcLNP4.png",O_="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/male_9-ByrZUoEp.png",ae={male:[{asset:pa},{asset:B_,key:"black_suit_shirt_tie"},{asset:M_,key:"grey_suit_shirt_tie"},{asset:I_,key:"black_suit_shirt"},{asset:E_,key:"grey_suit_shirt"},{asset:N_,key:"white_shirt"},{asset:R_,key:"white_round_collar_shirt"},{asset:D_,key:"black_round_collar_shirt"},{asset:L_,key:"white_v_shirt"},{asset:O_,key:"black_v_shirt"}],female:[{asset:pa},{asset:k_,key:"black_suit_shirt_tie"},{asset:v_,key:"black_suit_shirt"},{asset:w_,key:"grey_suit_shirt"},{asset:S_,key:"black_suit_collarless_shirt"},{asset:T_,key:"grey_suit_collarless_shirt"},{asset:x_,key:"white_shirt"},{asset:C_,key:"white_round_collar_shirt"},{asset:P_,key:"black_round_collar_shirt"},{asset:z_,key:"white_v_shirt"},{asset:j_,key:"black_v_shirt"}]},G_="_container_1vbfc_1",F_="_photoSpace_1vbfc_5",U_="_effectPhoto_1vbfc_20",H_="_beautyPhoto_1vbfc_21",K_="_aiEffectTips_1vbfc_42",q_="_aiEffectTipsContainer_1vbfc_49",Q_="_aiEffectTipsIcon_1vbfc_57",V_="_aiEffectTipsScroll_1vbfc_67",J_="_aiEffectTipsContent_1vbfc_72",W_="_notice_1vbfc_77",pe={container:G_,photoSpace:F_,effectPhoto:U_,beautyPhoto:H_,aiEffectTips:K_,aiEffectTipsContainer:q_,aiEffectTipsIcon:Q_,aiEffectTipsScroll:V_,aiEffectTipsContent:J_,"scroll-left":"_scroll-left_1vbfc_1",notice:W_},Y_="_operatingSpace_fbi0p_1",Z_="_operatingContent_fbi0p_9",X_="_operatingBtn_fbi0p_16",$_="_tabBar_fbi0p_31",Am="_tabItem_fbi0p_40",em="_active_fbi0p_47",am="_childTabBack_fbi0p_61",tm="_childTabContainer_fbi0p_67",im="_childTabBar_fbi0p_74",nm="_childTabItem_fbi0p_79",om="_backgroundTabItem_fbi0p_100",sm="_backgroundTabBar_fbi0p_132",rm="_skinTab_fbi0p_144",cm="_skinTabContainer_fbi0p_155",lm="_bangsTabItem_fbi0p_158",dm="_clothingTabItem_fbi0p_159",um="_bangsTabBar_fbi0p_186",_m="_clothingTabBar_fbi0p_187",mm="_clothingTabItemSwitch_fbi0p_202",_A={operatingSpace:Y_,operatingContent:Z_,operatingBtn:X_,tabBar:$_,tabItem:Am,active:em,childTabBack:am,childTabContainer:tm,childTabBar:im,childTabItem:nm,backgroundTabItem:om,backgroundTabBar:sm,skinTab:rm,skinTabContainer:cm,bangsTabItem:lm,clothingTabItem:dm,bangsTabBar:um,clothingTabBar:_m,clothingTabItemSwitch:mm};var dA=(A=>(A.background="background",A.skin="skin",A.outfit="outfit",A.bangs="bangs",A))(dA||{}),Ji=(A=>(A[A.female=0]="female",A[A.male=1]="male",A))(Ji||{});const gm=A=>t.jsx("div",{className:_A.backgroundTabBar,children:pA.map((a,e)=>{const i=A.value===e;return t.jsx("div",{style:{backgroundImage:`url(${a.bg})`},className:N(i&&_A.active,_A.backgroundTabItem),onClick:()=>A.onChange(A.activeTab,e)},a.name)})}),pm=A=>t.jsx("div",{className:_A.skinTabContainer,children:t.jsx(gn,{value:A.value,className:_A.skinTab,pin:!0,onIonInput:({detail:a})=>{A.onChange(A.activeTab,a.value)}})}),hm=A=>{const a=He(e=>A.onChange(A.activeTab,e),[A]);return t.jsx("div",{className:_A.bangsTabBar,children:$A.map((e,i)=>{const n=A.value===i;return t.jsx("div",{style:{backgroundImage:`url(${e.model})`},className:N(n&&_A.active,_A.bangsTabItem),onClick:()=>a(i)},i)})})},Gt=({sex:A,value:a,onChange:e,activeTab:i,onRefresh:n=()=>{},cache:s})=>{const r=o.useMemo(()=>s?Object.keys(s).some(f=>typeof a=="number"&&!!f.includes(`clothing_${a}-sex_${A}`)):!1,[s,a,A]),u=ae[A===0?"female":"male"],b=nA(),y=He(_=>{e(i,_)},[i,e]),h=He(()=>{n()},[n]);return t.jsx("div",{className:_A.clothingTabBar,children:u.map((_,f)=>{const v=a===f;return t.jsx("div",{style:{backgroundImage:`url(${_.asset})`},className:N(v&&_A.active,_A.clothingTabItem),onClick:()=>y(f),children:r&&v&&f!==0&&t.jsxs("div",{className:_A.clothingTabItemSwitch,onClick:h,children:[t.jsx("i",{}),t.jsx("p",{children:b.formatMessage({id:"try_again"})})]})},f)})})},fm=({onChange:A,value:a,refresh:e,onFinish:i,onChangeTab:n,cache:s})=>{const r=nA(),m=oe(),{sex:u,setSex:b}=NA(),[y,h]=o.useState(!1),_=o.useRef([{key:"background",title:r.formatMessage({id:"background"}),component:gm},{key:"skin",title:r.formatMessage({id:"skin_retouch"}),component:pm},{key:"outfit",title:r.formatMessage({id:"outfit"}),tabs:[{key:0,title:r.formatMessage({id:"female"}),component:Gt},{key:1,title:r.formatMessage({id:"male"}),component:Gt}]},{key:"bangs",title:r.formatMessage({id:"bangs"}),component:hm}]),{searchParams:f}=Qe(),v=(f==null?void 0:f.get("sex"))==="1"?1:0,[c,d]=o.useState("background"),[l,g]=o.useState(v),p=VA(l),[k,S]=o.useState("background"),P=o.useMemo(()=>{var H;return{tabs:_.current,content:(H=_.current.find(J=>J.key===c))==null?void 0:H.component}},[c]),x=o.useMemo(()=>{var J,Z;if(c!=="outfit")return;const H=_.current.find(vA=>vA.key==="outfit");return{tabs:H==null?void 0:H.tabs,content:(Z=(J=H==null?void 0:H.tabs)==null?void 0:J.find(vA=>vA.key===l))==null?void 0:Z.component}},[c,l]),[C,T]=o.useState(0);o.useEffect(()=>{n==null||n(c)},[n,c]);const B=o.useMemo(()=>!!x,[x]),w=r.formatMessage({id:B?"save":"next"}),I=(x==null?void 0:x.content)||P.content,E=o.useCallback(H=>{const J=Z=>{H.onChange(Z)};return t.jsx("div",{className:_A.tabBar,children:H.tabs.map(Z=>{const vA=Z.key===H.activeKey;return t.jsx("div",{className:N(_A.tabItem,vA&&_A.active),onClick:()=>J(Z.key),children:Z.title},Z.key)})})},[]),W=o.useCallback(H=>{const J=Z=>{H.onChange(Z)};return t.jsxs("div",{className:_A.childTabContainer,children:[t.jsx("div",{className:_A.childTabBack,onClick:H.onBack}),t.jsx("div",{className:_A.childTabBar,children:H.tabs.map(Z=>{const vA=Z.key===H.activeKey;return t.jsx("div",{className:N(_A.childTabItem,vA&&_A.active),onClick:()=>J(Z.key),children:Z.title},Z.key)})})]})},[]),L=o.useCallback(H=>{d(H);const J=_.current.find(Z=>Z.key===H);if(J!=null&&J.tabs&&J.tabs.length>0){T(a.outfit);return}S(H)},[a]),z=o.useMemo(()=>a[c],[a,c]),Y=o.useCallback(H=>{g(H)},[b,A]),D=o.useCallback(()=>{d(k||"background")},[k]),{photoName:V}=NA(),U=o.useCallback(()=>{a.outfit!==0&&y?WA.getState().present({message:r.formatMessage({id:"exitFirstMenuTips"}),direction:"horizontal",actions:[{title:r.formatMessage({id:"yes"}),type:"secondary",handler:()=>{D(),A("outfit",C),h(!1)}},{title:r.formatMessage({id:"no"}),type:"primary",handler:()=>{}}]}):D()},[r,D,a,C,A,V,y]),F=()=>{var H;if(B){if(mA()){const J=u!==l&&z===0?l:u;m.event("saved_snap_outfit",{outfit_material_name:z>0?(H=ae[J===1?"male":"female"][z])==null?void 0:H.key:void 0,outfit_gender:J==1?"male":"female"})}D(),h(!1)}else XA(()=>i==null?void 0:i())},hA=o.useCallback((H,J)=>{H==="outfit"&&p.current!==NA.getState().sex&&b(p.current),A(H,J),h(!0)},[A]);return t.jsxs("div",{className:_A.operatingSpace,children:[B?t.jsx(W,{activeKey:l,onBack:U,tabs:(x==null?void 0:x.tabs)||[],onChange:Y}):t.jsx(E,{activeKey:c,tabs:P.tabs||[],onChange:L}),t.jsx("div",{className:_A.operatingContent,children:I&&t.jsx(I,{sex:B?l:u,value:B&&u!==l&&z!==0?-1:z,onChange:hA,activeTab:c,onRefresh:e,cache:s},B?l:c)}),t.jsx("div",{className:_A.operatingBtn,onClick:F,children:w})]})},bm="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/loading-CrzXd7GW.png",ym="_container_1y24z_1",km="_box_1y24z_12",vm="_loadingAnimate_1y24z_17",wm="_loadingText_1y24z_27",Sm="_loadingDesc_1y24z_35",Tm="_btn_1y24z_43",Je={container:ym,box:km,loadingAnimate:vm,loadingText:wm,loadingDesc:Sm,btn:Tm};function xm({visible:A,time:a=35,onCancel:e,analyticData:i},n){const s=nA(),r=o.useCallback(()=>Math.floor(Math.random()*10)+30,[]),[m,u]=o.useState(!1),b=o.useRef(r()),y=o.useRef(r()),[h,_]=o.useState(y.current),f=o.useRef(),v=o.useRef(),c=()=>{f.current&&clearInterval(f.current),v.current&&clearInterval(v.current)};o.useEffect(()=>{a!==0&&A&&(b.current=r(),y.current=b.current,_(y.current))},[a,A,r]),o.useEffect(()=>{if(a===0){u(A);return}A?(u(!0),f.current=setInterval(()=>{y.current>1?(y.current--,_(y.current)):clearInterval(f.current)},1e3)):f&&(clearInterval(f.current),v.current=setInterval(()=>{y.current>0?(y.current--,_(y.current)):(clearInterval(v.current),u(!1))},50))},[A,a]);const d=()=>{WA.getState().present({message:s.formatMessage({id:"cancelGeneratorTips"}),direction:"horizontal",actions:[{title:s.formatMessage({id:"yes"}),type:"secondary",handler:()=>{e(),u(!1),c()}},{title:s.formatMessage({id:"no"}),type:"primary",handler:()=>{}}]})},[l,g]=o.useState(0),p=o.useMemo(()=>{const x=[s.formatMessage({id:"loadingText1"}),s.formatMessage({id:"loadingText2"}),s.formatMessage({id:"loadingText3"},{time:h}),s.formatMessage({id:"loadingText4"}),s.formatMessage({id:"loadingText5"}),s.formatMessage({id:"loadingText6"}),s.formatMessage({id:"loadingText7"}),s.formatMessage({id:"loadingText8"}),s.formatMessage({id:"loadingText9"})];return{group:x,text:l===2&&h===1?s.formatMessage({id:"almost_there_just_1_second_to_go"}):x[l]}},[s,h,l]),k=o.useRef();o.useEffect(()=>(k.current=setInterval(()=>{const x=Math.floor(Math.random()*p.group.length);g(x)},3e3),()=>clearInterval(k.current)),[p.group.length]),o.useImperativeHandle(n,()=>({cancel:()=>{c(),u(!1)}}));const S=o.useRef(null),P=o.useMemo(()=>{if(a===0)return 0;const x=b.current;return parseInt(`${(x-h)/x*100}`,10)},[h,a]);return Vt.createPortal(t.jsx(Jt,{in:m,timeout:300,nodeRef:S,classNames:"fade",unmountOnExit:!0,children:t.jsx("div",{ref:S,className:Je.container,children:t.jsxs("div",{className:Je.box,children:[t.jsx("div",{className:Je.loadingAnimate,style:{backgroundImage:`url(${bm})`}}),t.jsxs("div",{className:Je.loadingText,children:[s.formatMessage({id:"loading"}),"...",P,"%"]}),t.jsx("div",{className:Je.loadingDesc,children:p.text}),t.jsx("div",{className:Je.btn,onClick:d,children:s.formatMessage({id:"cancel"})})]})})}),document.querySelector(".app"))}const Cm=o.forwardRef(xm),ee={background:0,beauty:30,clothing:0,bang:0},$e=({isBeauty:A=!1,bangCode:a=ee.bang,clothesCode:e=ee.clothing,sex:i})=>`${A?"beauty-":""}${a!==void 0?`bang_${a}-`:""}${e!==void 0?`clothing_${e}-sex_${i}`:""}`,Ft=1,Pm=()=>{const A=nA(),a=()=>{WA.getState().present({title:A.formatMessage({id:"notice"}),message:t.jsxs("div",{className:pe.notice,children:[t.jsx("p",{dangerouslySetInnerHTML:{__html:A.formatMessage({id:'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.'})}}),t.jsx("p",{dangerouslySetInnerHTML:{__html:A.formatMessage({id:'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.'})}})]}),actions:[{type:"primary",title:A.formatMessage({id:"gotIt"})}]})};return t.jsx("div",{className:pe.aiEffectTipsContainer,onClick:a,children:t.jsxs("div",{className:pe.aiEffectTips,children:[t.jsx(xA,{className:pe.aiEffectTipsIcon,url:y_}),t.jsxs("div",{className:pe.aiEffectTipsScroll,children:[t.jsx("div",{className:pe.aiEffectTipsContent,dangerouslySetInnerHTML:{__html:`${A.formatMessage({id:'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.'})} ${A.formatMessage({id:'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.'})}`}}),t.jsx("div",{className:pe.aiEffectTipsContent,dangerouslySetInnerHTML:{__html:`${A.formatMessage({id:'1._generating_ai_outfits_may_"take_a_while"_due_to_the_large_volume_of_requests.'})}${A.formatMessage({id:'2._using_ai-generated_outfits_for_official_documents_may_increase_the_"risk_of_submission_rejection"._we_recommend_checking_the_official_website_for_detailed_document_requirements_before_proceeding.'})}`}})]})]})})},zm=A=>{const a=nA(),e=oe(),i=o.useRef(null),{original:n,size:s,sex:r,setSex:m}=A,u=Me(),[b,y]=o.useState(ee.background),[h,_]=o.useState(A.defaultBeauty||0),[f,v]=o.useState(ee.clothing),[c,d]=o.useState(ee.bang),[l,g]=o.useState(0),p=o.useMemo(()=>{const O=r===Ji.female?"female":"male";return{background:pA[b].bg,beauty:h/100,clothes:ae[O][f].key,bang:$A[c].style_id}},[b,h,f,c,r]),k=o.useMemo(()=>({[dA.background]:b,[dA.skin]:h,[dA.outfit]:f,[dA.bangs]:c}),[b,h,f,c]),S=o.useMemo(()=>pA.filter(O=>O.bg!==p.background&&O.isMain).slice(0,3),[p]),[P,x]=o.useState([n]),C=o.useRef({}),[T,B]=o.useState({}),w=o.useRef(),I=o.useRef(),E=o.useRef(),W=o.useCallback((O,X)=>{C.current[O]=X,B(j=>({...j,[O]:X}))},[]),L=o.useCallback(()=>{C.current={},B({})},[]);ns(()=>()=>{L(),w.current=[],I.current=[],E.current={}});const[z,Y]=o.useState({width:0,height:0}),[D,V]=o.useState(dA.background),U=D===dA.outfit,F=o.useRef(),hA=o.useRef(),[H,J]=o.useState(!1),[Z,vA]=o.useState(Date.now()),eA=()=>{vA(Date.now())},aA=o.useRef([f,r]);o.useEffect(()=>()=>{aA.current=[f,r]},[f,r]);const $=o.useRef(),rA=VA(()=>{hA.current===dA.outfit&&(v(aA.current[0]),m(aA.current[1]))}),RA=o.useRef(c);o.useEffect(()=>()=>{RA.current=c},[c]);const wA=VA(()=>{hA.current===dA.bangs&&d(RA.current)}),MA=VA(()=>{rA.current(),wA.current()}),Te=VA(()=>{var O,X;vA(0),J(!1),(O=i.current)==null||O.cancel(),(X=$.current)==null||X.call($)}),DA=o.useRef(!1);o.useEffect(()=>{var R;if(!n||z.width===0||z.height===0)return;const O=$e({isBeauty:!1,bangCode:c,clothesCode:f,sex:r}),X=$e({isBeauty:!0,bangCode:c,clothesCode:f,sex:r}),j=C.current[O],M=C.current[X];if(c===ee.bang&&f===ee.clothing){M?(x([n,M]),w.current=I.current):(async()=>{try{const K=await Bt(n);I.current=K,w.current=K;let Q=await ca(n,Ft);Q=await sa(Q,K),W(X,Q),x([n,Q])}catch(K){u.error((K==null?void 0:K.message)||a.formatMessage({id:"network_error_info"}))}})(),F.current={clothing:f,bang:c};return}if(j&&M&&!Z)x([j,M]),F.current={clothing:f,bang:c},f===ee.clothing?w.current=I.current:w.current=((R=E.current)==null?void 0:R[`${r}-${f}`])||[];else{const G=async()=>{var K,Q,PA,TA,gA;try{let cA=!1;const q=()=>{MA.current()};DA.current=!0,u.loading({onClose(){cA=!0,q(),DA.current=!1}});let AA=n,bA=!1;const ye=C.current[$e({bangCode:c,sex:r})],ce=C.current[$e({clothesCode:f,sex:r})],Ce=async()=>{var le;if(p.clothes!==void 0){u.hide(),J(!0),g(0);let de="",Ne=Date.now();try{de=await Go({base64:AA,gender:r,width:parseInt((z.width*1.5).toString(),10),height:parseInt((z.height*1.5).toString(),10),style:p.clothes});const{result:ue,clear:We,time:Da}=Uo(de);Da.then(({remainTime:Oa})=>{g(Oa)}).catch(()=>{}),$.current=We;const La=await ue,Yi=await Bt(La);if(mA()){const Oa=Date.now();e.event("func_use_snap_outfit_result",{outfit_is_success:"1",outfit_time:String(Oa-Ne),outfit_material_name:p.clothes,outfit_gender:r===0?"female":"male"})}return E.current={...E.current,[`${r}-${f}`]:Yi},AA=La,La}catch(ue){if(mA()){const We=Date.now();e.event("func_use_snap_outfit_result",{outfit_is_success:"0",outfit_time:String(We-Ne),outfit_material_name:p.clothes,outfit_gender:r===0?"female":"male"})}throw(ue==null?void 0:ue.message)==="stop"?(J(!1),(le=i.current)==null||le.cancel(),MA.current(),Fo(de),new Error("stop")):new Error(a.formatMessage({id:"generateFailed"}))}}throw new Error(a.formatMessage({id:"generateFailed"}))};if(c!==ee.bang&&f!==ee.clothing)if(Z||!ce||(((K=E.current)==null?void 0:K[`${r}-${f}`])||[]).length===0)AA=await Ce(),w.current=((Q=E.current)==null?void 0:Q[`${r}-${f}`])||[],W($e({clothesCode:f,sex:r}),AA),p.bang!==void 0&&(AA=await Ka(AA,p.bang)),bA=!0;else{if(AA=ce,w.current=((PA=E.current)==null?void 0:PA[`${r}-${f}`])||[],p.bang!==void 0&&(AA=await Ka(AA,p.bang),cA))return;bA=!0}else if(c!==ee.bang){if(ye)AA=ye,bA=!0;else if(p.bang!==void 0){if(AA=await Ka(AA,p.bang),cA)return;bA=!0}w.current=I.current}else f!==ee.clothing&&(ce&&!Z?AA=ce:AA=await Ce(),w.current=((TA=E.current)==null?void 0:TA[`${r}-${f}`])||[],bA=!0);if(!bA){u.hide(),J(!1);return}const Ee=await ca(AA,Ft);W(O,await sa(AA,w.current)),W(X,await sa(Ee,w.current)),x([C.current[O],C.current[X]]),await te(AA),u.hide(),J(!1),F.current={clothing:f,bang:c}}catch(cA){if((cA==null?void 0:cA.message)==="stop")return;u.hide(),J(!1),(gA=i.current)==null||gA.cancel(),WA.getState().present({message:a.formatMessage({id:"failed_to_generate"}),actions:[{title:a.formatMessage({id:"try_again"}),type:"primary",handler(){setTimeout(()=>{XA(G,{onCancel(){MA.current()}})},100)}},{title:a.formatMessage({id:"cancel"}),type:"secondary",handler(){MA.current()}}]})}finally{vA(0),DA.current=!1}};G()}},[e,c,f,p.clothes,p.bang,n,u,a,z,r,Z,rA,W]),o.useEffect(()=>{hA.current=D},[D]);const SA=o.useMemo(()=>{if(!P)return{common:"",beauty:""};const[O,X]=P;return{common:O||"",beauty:X||""}},[P]),CA=o.useRef(null);o.useEffect(()=>{if(!s)return()=>{};let O;return O=setTimeout(()=>{O=void 0;const X=CA.current;if(!X)return;const j=parseInt(window.getComputedStyle(X).paddingLeft,10)||0;let M=X.clientWidth-j*2,R=X.clientHeight-j*2;const G=(K,Q)=>{if(K&&Q){const PA=K/Q,[TA,gA]=s,cA=TA/gA;let q=[0,0];if(PA>cA){const AA=Q;q=[AA*cA,AA]}else{const AA=K,bA=AA/cA;q=[AA,bA]}Y({width:q[0],height:q[1]})}};M<100?(M=X.clientWidth-j*2,R=X.clientHeight-j*2,O=setTimeout(()=>{O=void 0,G(M,R)},200)):G(M,R)},200),()=>{O&&clearTimeout(O)}},[s]);const re=o.useCallback((O,X)=>{const M={[dA.background]:y,[dA.bangs]:d,[dA.outfit]:v,[dA.skin]:_}[O];if(!M)throw new Error("TabKey is error.");M(X)},[]),{onFinish:be,size:HA}=A,Oe=o.useCallback(async O=>{const X=document.createElement("canvas"),j=X.getContext("2d"),M=await te(O.bg||p.background),R=await te(O.img);let[G,K]=HA.map(Q=>Q*(A.unit==="mm"?20:10));G<700&&A.unit==="mm"&&(G=700,K=G*(HA[1]/HA[0])),X.width=G,X.height=K,j&&(j.drawImage(M,0,0,M.width,M.height,0,0,G,K),j.drawImage(R,0,0,G,K));try{const Q=X.toDataURL("image/png",1);return p.beauty===0||O.hasBeauty?Q:`data:image/png;base64,${await ca(Q,p.beauty,"jpg")}`}catch(Q){throw new Error((Q==null?void 0:Q.message)||a.formatMessage({id:"network_error_info"}))}},[p.background,p.beauty,a,HA,A.unit]),{photoName:xe,sizeItem:fA}=NA(),Ae=o.useRef(!1),Ie=o.useCallback(async()=>{var O,X,j,M,R,G,K,Q,PA,TA,gA,cA;if(!Ae.current){Ae.current=!0,uA()?e.event("snapid_page_button_clk",{clk:"edit",project:"id_photo",name:(O=fA==null?void 0:fA.name)!=null?O:"",size_value:(X=fA==null?void 0:fA.sizeValue)!=null?X:"",beauty_slide:k[dA.skin],background:pA[k[dA.background]].label,bang_style:(j=$A.find((q,AA)=>{var bA;return AA===(((bA=F.current)==null?void 0:bA.bang)||k[dA.bangs])}))==null?void 0:j.label,outfit:(R=ae[r===1?"male":"female"][((M=F.current)==null?void 0:M.clothing)||k[dA.outfit]])==null?void 0:R.key}):mA()&&e.event("func_use_snapid_headshot",{id_name:(G=fA==null?void 0:fA.name)!=null?G:"",size:(K=fA==null?void 0:fA.sizeValue)!=null?K:"",background:pA[k[dA.background]].name,bangs:(Q=$A.find((q,AA)=>{var bA;return AA===(((bA=F.current)==null?void 0:bA.bang)||k[dA.bangs])}))==null?void 0:Q.id,beauty_slide:String(k[dA.skin]),outfit_material_name:(TA=ae[r===1?"male":"female"][((PA=F.current)==null?void 0:PA.clothing)||k[dA.outfit]])==null?void 0:TA.key,outfit_gender:r===1?"male":"female"}),u.loading();try{const[q]=P,AA=await ca(q,p.beauty,"jpg"),bA=AA?`data:image/png;base64,${AA}`:q,ye=await sa(bA,w.current),ce=await Oe({img:ye,hasBeauty:!0}),Ce=await Promise.all(S.map(async Ee=>await Oe({img:ye,bg:Ee.bg,hasBeauty:!0})));await(be==null?void 0:be(ce,S.map(Ee=>Ee.name),Ce,{background:k[dA.background],beauty:k[dA.skin],clothing:((gA=F.current)==null?void 0:gA.clothing)||k[dA.outfit],bang:((cA=F.current)==null?void 0:cA.bang)||k[dA.bangs]},p)),Ae.current=!1,u.hide()}catch(q){u.error((q==null?void 0:q.message)||a.formatMessage({id:"network_error_info"})),Ae.current=!1}}},[u,e,be,P,Oe,p,k,S,a]),KA=VA(Ie);return BA(()=>{const O=EA.addListener("click",()=>{!DA.current&&!Ae.current&&!WA.getState().current&&XA(()=>{u.loading(),setTimeout(()=>{KA.current()},1e3)})});return()=>{O.remove()}},[]),t.jsxs("div",{style:A.style,className:N(pe.container,A.className),children:[U&&t.jsx(Pm,{}),t.jsx("div",{ref:CA,className:pe.photoSpace,children:t.jsxs("div",{style:{width:z.width,height:z.height,backgroundImage:`url(${p.background})`},children:[t.jsx("div",{className:pe.effectPhoto,style:{backgroundImage:`url(${SA.common})`}}),t.jsx("div",{className:pe.beautyPhoto,style:{backgroundImage:`url(${SA.beauty})`,opacity:SA.beauty?p.beauty:0}})]})}),t.jsx(fm,{onChange:re,value:k,refresh:eA,onFinish:Ie,cache:T,onChangeTab:O=>V(O)}),t.jsx(Cm,{ref:i,visible:H,time:l,onCancel:Te.current,analyticData:{id_name:xe,outfit:`${r===1?"男性":"女性"}${k[dA.outfit]}`}})]})},jm="_content_fk2oc_1",Bm={content:jm},Ut=Object.assign({"../../../assets/images/paper_poster_bottom_ar.png":Du,"../../../assets/images/paper_poster_bottom_de.png":Ou,"../../../assets/images/paper_poster_bottom_en.png":Fu,"../../../assets/images/paper_poster_bottom_es.png":Hu,"../../../assets/images/paper_poster_bottom_fr.png":qu,"../../../assets/images/paper_poster_bottom_id.png":Vu,"../../../assets/images/paper_poster_bottom_it.png":Wu,"../../../assets/images/paper_poster_bottom_ja.png":Zu,"../../../assets/images/paper_poster_bottom_ko.png":$u,"../../../assets/images/paper_poster_bottom_pt.png":e_,"../../../assets/images/paper_poster_bottom_ru.png":t_,"../../../assets/images/paper_poster_bottom_special_ja.png":n_,"../../../assets/images/paper_poster_bottom_special_th.png":s_,"../../../assets/images/paper_poster_bottom_th.png":c_,"../../../assets/images/paper_poster_bottom_tr.png":d_,"../../../assets/images/paper_poster_bottom_vi.png":__,"../../../assets/images/paper_poster_bottom_zh-HK.png":g_,"../../../assets/images/paper_poster_bottom_zh-Hant.png":h_,"../../../assets/images/paper_poster_bottom_zh-TW.png":b_}),Mm=fe(()=>{const{searchParams:A}=Qe(),a=parseInt((A==null?void 0:A.get("beauty"))||"0",10),e=nA(),{photoName:i,adjustImg:n,sizeItem:s,sizeDisplay:r,sex:m,setSex:u,setPoster:b,setMoreColors:y,setMoreColorPhotos:h,setEffectImg:_,reversal:f,setAnalynicData:v,poster:c,unit:d}=NA(),l=s,{size:g}=l;BA(()=>{const T=we()<=0;return T&&EA.antiScreenshot({open:!0,antiBtnName:e.formatMessage({id:"ok"})}),()=>{T&&EA.antiScreenshot({open:!1})}},[]);const p=o.useCallback(({ctx:T,img:B,x:w,y:I,imgWidth:E,imgHeight:W,unit:L,hCount:z,vCount:Y,gap:D})=>{const[V,U]=g;for(let F=0;F<z*Y;F++)T.drawImage(B,0,0,E,W,w+(L*V+D)*(F%z),I+(L*U+D)*Math.floor(F/z),L*V,L*U),F/z<1&&ra(T,w+(L*V+D)*(F%z),I,V,U,"top",l.originSizeInfo),Math.floor(F/z)===Y-1&&ra(T,w+(L*V+D)*(F%z),I+(L*U+D)*Math.floor(F/z),V,U,"bottom",l.originSizeInfo),F%z===0&&ra(T,w,I+(L*U+D)*Math.floor(F/z),V,U,"left",l.originSizeInfo),(F+1)%z===0&&ra(T,w+(L*V+D)*(F%z),I+(L*U+D)*Math.floor(F/z),V,U,"right",l.originSizeInfo)},[g,l.originSizeInfo]),[k]=Ia(),S=o.useCallback(async T=>{var E;const[B,w]=g,I={JP:{width:890,height:1270},TH:{width:1016,height:1524},global:{width:1270,height:1778}};try{const W=await te(T),L=document.createElement("canvas"),z=L.getContext("2d");if(!z)throw new Error("canvas context is null");const Y=k.toUpperCase(),D=I[Y]||I.global;L.width=D.width,L.height=D.height,z.rect(0,0,L.width,L.height),z.fillStyle="#F0F6FF",z.fill(),z.fillStyle="#000000",z.textAlign="center",z.textBaseline="top";let U={JP:"ja",TH:"th",global:"en"}[Y];U||(U=window.lang),Y==="JP"?(z.font="500 27px sans-serif",z.fillText(`${e.formatMessage({id:"size"})}: ${r}`,L.width/2,21)):Y==="KR"?(z.font="500 38px sans-serif",z.fillText(`${e.formatMessage({id:"size"})}: ${r}`,L.width/2,31)):Y==="TH"?(z.font="500 38px sans-serif",z.fillText(`${e.formatMessage({id:"size"})}: ${r}`,L.width/2,31)):(z.font="500 38px sans-serif",z.fillText(`${e.formatMessage({id:"size"})}: ${r}`,L.width/2,27));const F=10,hA=26;let H;Y==="JP"?H=150:Y==="KR"?H=265:Y==="TH"?H=188:H=265;let[J,Z]=l.count||[];(!J||!Z)&&(J=Math.floor((L.width-100)/(F*B+hA)),Z=Math.floor((L.height-H-76-40)/(F*w+hA)));const vA=(L.width-(F*B*J+(J-1)*hA))/2,eA=(L.height-H-(F*w*Z+(Z-1)*hA))/2;p({ctx:z,img:W,x:vA,y:eA,imgWidth:W.width,imgHeight:W.height,unit:F,hCount:J,vCount:Z,gap:hA});let aA=U;Y!=="JP"&&Y!=="TH"&&(U==="ja"||U==="th")&&(aA=`special_${U}`);const $=await te((E=Ut[`../../../assets/images/paper_poster_bottom_${aA}.png`]||{default:Ut["../../../assets/images/paper_poster_bottom_en.png"]})==null?void 0:E.default);Y==="JP"?z.drawImage($,0,0,$.width,$.height,0,1094,890,176):Y==="TH"?z.drawImage($,0,0,$.width,$.height,0,1273,1016,251):z.drawImage($,0,0,$.width,$.height,0,1527,1270,251);const rA=L.toDataURL("image/jpeg",1);return b({width:L.width,height:L.height,source:rA}),rA}catch(W){return console.log(W),""}},[g,p,l,b,k,r]),P=UA(),x=oe();BA(()=>{uA()?x.event("snapid_page_event",{page_id:"edit_page_view",project:"id_photo"}):mA()&&(x.event("func_enter_snap_headshot"),x.event("edit_enter",{edit_enter_from:"snapid"}))},[x]);const C=o.useCallback(async(T,B,w,I,E)=>{v(I),await S(T),_(T),y(B),h(w);try{await Tt.getState().initiateIfNeed()}catch(W){throw new Error(sA("network_error_info"))}P.push(`/IDPhoto/pay?hasClothes=${I.clothing!==0?1:0}&isWhite=${I.background===0?1:0}&from=make`)},[v,m,f,l,x,S,P,_,y,h]);return t.jsxs(Ke,{children:[t.jsx(se,{title:e.formatMessage({id:"edit_title"}),backable:!0}),t.jsx(ie,{className:Bm.content,children:t.jsx(zm,{unit:d,original:n,size:g,sex:m,setSex:u,onFinish:C,defaultBeauty:a})})]})}),Im="_mask_1wwka_1",Em="_swiper_1wwka_12",Nm="_swiperContainer_1wwka_15",Rm="_swiperSlide_1wwka_22",Dm="_photo_1wwka_40",Lm="_photoContainer_1wwka_45",Om="_arrow_1wwka_52",Gm="_blink_1wwka_1",Fm="_size_1wwka_76",Um="_shareBtn_1wwka_83",Hm="_saveBtn_1wwka_84",Km="_btnGroup_1wwka_92",me={mask:Im,swiper:Em,swiperContainer:Nm,swiperSlide:Rm,photo:Dm,photoContainer:Lm,arrow:Om,blink:Gm,size:Fm,shareBtn:Um,saveBtn:Hm,btnGroup:Km},qm=[pn],Qm={prev:{shadow:!1,opacity:0,scale:.8,translate:["-100%",0,0]},next:{shadow:!1,opacity:0,scale:.8,translate:["100%",0,0]}};function Vm(A){const{hasBtn:a=!0}=A,e=nA(),i=o.useRef(null),[n,s]=o.useState(0),r=Ct(),m=o.useMemo(()=>A.photos[n],[A.photos,n]),u=()=>{var c;(c=A==null?void 0:A.onShare)==null||c.call(A,A.photos[n].url)},b=async()=>{var c;(c=A==null?void 0:A.onSave)==null||c.call(A,A.photos[n].url),r(async()=>{await ZA.save({type:2,data:m.url})})},[y,h]=o.useState(!0),_=o.useMemo(()=>y&&A.photos.length>1,[y,A.photos]);o.useEffect(()=>{A.visible&&h(!0)},[A.visible]);const f=o.useRef(null);return Vt.createPortal(t.jsx(Jt,{in:A.visible,timeout:300,nodeRef:f,classNames:"bounce",unmountOnExit:!0,children:t.jsxs("div",{ref:f,className:me.mask,onClick:A.onClose,children:[t.jsxs("div",{className:me.swiperContainer,onClick:c=>c.stopPropagation(),children:[t.jsx(Ca,{ref:i,initialSlide:A.index,effect:"creative",creativeEffect:Qm,modules:qm,className:me.swiper,onSlideChange:c=>{s(c.activeIndex),h(!1)},children:A.photos.map((c,d)=>t.jsxs(Pa,{className:me.swiperSlide,onClick:A.onClose,children:[t.jsx("div",{className:N(me.photoContainer,_?me.arrow:""),children:t.jsx(Le,{className:me.photo,url:c.url,onClick:l=>l.stopPropagation()})}),t.jsx("div",{className:me.size,onClick:l=>l.stopPropagation(),children:c.name})]},c.url))}),a&&t.jsxs("div",{className:me.btnGroup,children:[t.jsx("div",{className:me.shareBtn,onClick:u,children:e.formatMessage({id:"share"})}),t.jsx("div",{className:me.saveBtn,onClick:()=>b(),children:e.formatMessage({id:"save"})})]})]}),A.renderFooter?A.renderFooter():null]})}),document.querySelector(".app"))}const Jm="_content_1idof_1",Wm="_appBar_1idof_4",Ym="_wrap_1idof_7",Zm="_defaultCard_1idof_13",Xm="_defaultCardBody_1idof_18",$m="_defaultCardPrice_1idof_23",Ag="_defaultCardInfo_1idof_31",eg="_defaultCardInfoItem_1idof_38",ag="_defaultCardPhotoList_1idof_55",tg="_defaultCardPhotoItem_1idof_60",ig="_defaultCardPhotoItem_wrapper_1idof_67",ng="_defaultCardHeader_1idof_72",og="_SelectableCard_1idof_80",sg="_SelectableCardHeader_1idof_84",rg="_SelectableCardHeaderRight_1idof_94",cg="_SelectableCardPrice_1idof_98",lg="_SelectableCardCheckBox_1idof_105",dg="_normal_1idof_112",ug="_active_1idof_115",_g="_SelectableCardBody_1idof_118",mg="_SelectableCardList_1idof_121",gg="_SelectableCardColor_1idof_126",pg="_SelectableCardPhotoBox_1idof_132",hg="_SelectableCardPhotoSize_1idof_138",fg="_footer_1idof_150",bg="_purchaseBox_1idof_159",yg="_purchasePrice_1idof_167",kg="_purchasePriceLabel_1idof_172",vg="_purchaseBtn_1idof_177",wg="_safeBottom_1idof_188",oA={content:Jm,appBar:Wm,wrap:Ym,defaultCard:Zm,defaultCardBody:Xm,defaultCardPrice:$m,defaultCardInfo:Ag,defaultCardInfoItem:eg,defaultCardPhotoList:ag,defaultCardPhotoItem:tg,defaultCardPhotoItem_wrapper:ig,defaultCardHeader:ng,SelectableCard:og,SelectableCardHeader:sg,SelectableCardHeaderRight:rg,SelectableCardPrice:cg,SelectableCardCheckBox:lg,normal:dg,active:ug,SelectableCardBody:_g,SelectableCardList:mg,SelectableCardColor:gg,SelectableCardPhotoBox:pg,SelectableCardPhotoSize:hg,footer:fg,purchaseBox:bg,purchasePrice:yg,purchasePriceLabel:kg,purchaseBtn:vg,safeBottom:wg},Sg=fe(()=>{var J,Z,vA;const A=nA(),a=Me(),e=UA(),{searchParams:i}=Qe(),n=((J=i==null?void 0:i.get("hasClothes"))==null?void 0:J.toString())==="1",s=((Z=i==null?void 0:i.get("isWhite"))==null?void 0:Z.toString())==="1",{effectImg:r,poster:m,moreColors:u,moreColorPhotos:b,sizeItem:y,sizeDisplay:h,analynicData:_,reversal:f,photoName:v,sex:c}=NA(),d=y,l=Zo(),g=Xo(),p=l[0],k=o.useMemo(()=>g.get(p),[p,g]),S=l[1],P=o.useMemo(()=>g.get(S),[S,g]),x=Tt(eA=>eA.extra),[C,T]=o.useState(p),B=o.useMemo(()=>g.get(C),[C,g]),w=oe();BA(()=>{if(uA())w.event("snapid_page_event",{page_id:"payment_page_view",project:"id_photo"});else if(mA()){const eA=pA[(_==null?void 0:_.background)||0],aA=$A.find((rA,RA)=>RA===_.bang),$=ae[c===1?"male":"female"][_==null?void 0:_.clothing];w.event("w_subscription_enter",{source_module:"p_homepage",source_0:"snap_id",source_1:`${d.name?`${d.name},`:""}${d.sizeValue},${eA?`${eA.name},`:""}${typeof(_==null?void 0:_.beauty)=="number"?`${_.beauty},`:""}${aA!=null&&aA.id?`${aA.id},`:""}${$!=null&&$.key?`${$.key},`:""}${u.length>0?u.join(","):""}`.replace(/,$/,""),sale_status:"normal"})}},[w,d,c,_]),BA(()=>{const eA=we()<=0;return eA&&EA.antiScreenshot({open:!0}),()=>{eA&&EA.antiScreenshot({open:!1})}},[]);const I=async()=>{var RA;const eA=[r,(RA=m==null?void 0:m.source)!=null?RA:""];C===S&&b.forEach(wA=>{eA.push(wA)});const aA=[];for(const wA of eA){const MA=await je.base64ToFile({suffix:"jpeg",data:ze(wA)});aA.push(MA.file)}const{data:$}=await _t.batchUpload({storageType:1,data:aA.map(wA=>({type:0,data:wA}))});if(!$.every(wA=>wA.code===0))throw new Error(A.formatMessage({id:"network_error_info"}));return $.map(wA=>wA.url)},[E]=Ia(),W=async()=>{var eA,aA,$,rA,RA,wA,MA,Te,DA;if(uA())w.event("snapid_page_button_clk",{clk:"purchase",project:"id_photo",name:d.name,size_value:d.sizeValue,beauty_slide:typeof(_==null?void 0:_.beauty)=="number"?_.beauty:void 0,background:pA[(_==null?void 0:_.background)||0].label,bang_style:(eA=$A.find((SA,CA)=>CA===_.bang))==null?void 0:eA.label,outfit:(aA=ae[c===1?"male":"female"][_==null?void 0:_.clothing])==null?void 0:aA.key});else if(mA()){const SA=pA[(_==null?void 0:_.background)||0],CA=$A.find((be,HA)=>HA===_.bang),re=ae[c===1?"male":"female"][_==null?void 0:_.clothing];w.event("w_subscription_click",{source_module:"p_homepage",source_0:"snap_id",source_1:`${d.name?`${d.name},`:""}${d.sizeValue},${SA?`${SA.name},`:""}${typeof(_==null?void 0:_.beauty)=="number"?`${_.beauty},`:""}${CA!=null&&CA.id?`${CA.id},`:""}${re!=null&&re.key?`${re.key},`:""}${C===S&&u.length>0?u.join(","):""}`.replace(/,$/,""),sale_status:"normal",duration:"times_1",SKU:C})}a.loading();try{we()>0&&await ba();const SA=await I(),[CA,re,...be]=SA;let HA=A.formatMessage({id:"posterSize"});E==="TH"?HA=A.formatMessage({id:"THPosterSize"}):E==="JP"&&(HA=A.formatMessage({id:"JPPosterSize"}));const Oe={type:"paper",paper:d,params:{isMirror:f?1:0,background:pA[(_==null?void 0:_.background)||0].name,beauty:typeof(_==null?void 0:_.beauty)=="number"?_.beauty:void 0,bang:($=$A.find((KA,O)=>O===_.bang))==null?void 0:$.style_id,hasOutfit:n?1:0},photos:[{name:h,url:CA,aspectRatio:d.size[0]/d.size[1],background:pA[(_==null?void 0:_.background)||0].name},{name:HA,url:re,isPoster:!0,aspectRatio:m?m.width/m.height:void 0}].concat(C===S?be.slice(0,3).map((KA,O)=>{var X;return{name:h,url:KA,aspectRatio:d.size[0]/d.size[1],background:(X=u[O])!=null?X:""}}):[]),sku:C,product:va(),report:mA()?{id_name:v,size:d.sizeValue,background:pA[(_==null?void 0:_.background)||0].name,bangs:(rA=$A.find((KA,O)=>O===_.bang))==null?void 0:rA.id,beauty_slide:typeof(_==null?void 0:_.beauty)=="number"?String(_.beauty):void 0,outfit_material_name:(RA=ae[c===1?"male":"female"][_==null?void 0:_.clothing])==null?void 0:RA.key,outfit_gender:c===1?"male":"female"}:{edit_type:"common",name:v,size_value:d.sizeValue,is_mirror:f?1:0,beauty_slide:typeof(_==null?void 0:_.beauty)=="number"?_.beauty:void 0,background:pA[(_==null?void 0:_.background)||0].label,bang_style:(wA=$A.find((KA,O)=>O===_.bang))==null?void 0:wA.label,outfit:(MA=ae[c===1?"male":"female"][_==null?void 0:_.clothing])==null?void 0:MA.key}},{data:xe}=await zi({urls:SA,goods_id:va(),ext:Oe});if(!xe.task_id)throw new Error(A.formatMessage({id:"network_error_info"}));let fA="",Ae="";const Ie=we()>0;if(!Ie){try{({orderID:fA,transactionID:Ae}=await aa.purchase({sku:C}))}catch(KA){throw new Error(A.formatMessage({id:"paymentFailure"}))}if(!fA)throw new Error(A.formatMessage({id:"paymentFailure"}))}try{await kt({task_id:xe.task_id,aw_trans_id:fA,voucher_ids:Pi()})}catch(KA){throw fA&&xe.task_id&&vt.getState().add(fA,xe.task_id),KA}if(await qe.getState().initiate().catch(()=>{}),ba(),uA())w.event("snapid_consume",{project:"id_photo",id_name:v,size_value:d.sizeValue,beauty_slide:typeof(_==null?void 0:_.beauty)=="number"?_.beauty:void 0,background:pA[(_==null?void 0:_.background)||0].label,bang_style:(Te=$A.find((KA,O)=>O===_.bang))==null?void 0:Te.label,outfit:(DA=ae[c===1?"male":"female"][_==null?void 0:_.clothing])==null?void 0:DA.key,more_background:C===S?1:0,more_size:0,amount:!Ie&&B?B.localPrice:0,order_id:Ae,sku_id:C});else if(mA()){const KA=pA[(_==null?void 0:_.background)||0],O=$A.find((j,M)=>M===_.bang),X=ae[c===1?"male":"female"][_==null?void 0:_.clothing];w.event("w_subscription_success",{source_module:"p_homepage",source_0:"snap_id",source_1:`${d.name?`${d.name},`:""}${d.sizeValue},${KA?`${KA.name},`:""}${typeof(_==null?void 0:_.beauty)=="number"?`${_.beauty},`:""}${O!=null&&O.id?`${O.id},`:""}${X!=null&&X.key?`${X.key},`:""}${C===S&&u.length>0?u.join(","):""}`.replace(/,$/,""),sale_status:"normal",order_id:Ae,duration:"times_1",SKU:C})}a.hide(),e.push("/task?key=id",{animated:!1})}catch(SA){a.error((SA==null?void 0:SA.message)||A.formatMessage({id:"paymentFailure"}))}},L=o.useMemo(()=>({width:(d==null?void 0:d.size[0])||0,height:(d==null?void 0:d.size[1])||0}),[d]),[z,Y]=o.useState([]),[D,V]=o.useState(0),U=o.useMemo(()=>{let eA=A.formatMessage({id:"posterSize"});return E==="TH"?eA=A.formatMessage({id:"THPosterSize"}):E==="JP"&&(eA=A.formatMessage({id:"JPPosterSize"})),eA},[A,E]),F=t.jsxs("div",{className:oA.defaultCard,children:[t.jsx("div",{className:oA.defaultCardHeader,children:A.formatMessage({id:"results"})}),t.jsxs("div",{className:oA.defaultCardBody,children:[t.jsx("div",{className:oA.defaultCardPrice,children:k?k.localPrice:""}),t.jsxs("div",{className:oA.defaultCardInfo,children:[t.jsx("div",{className:oA.defaultCardInfoItem,children:A.formatMessage({id:"digitalPhoto"})}),t.jsx("div",{className:oA.defaultCardInfoItem,children:A.formatMessage({id:"digitalPhotoTips"})})]}),t.jsxs("div",{className:oA.defaultCardPhotoList,children:[t.jsx("div",{className:N(oA.defaultCardPhotoItem,oA.defaultCardPhotoItem_wrapper),children:t.jsx(Le,{style:(()=>{const eA=.*****************window.innerWidth,aA=212/390*window.innerWidth,$=eA/aA,rA=L.width/L.height;return{border:s?"1px solid rgba(0, 0, 0, 0.1)":void 0,height:$>rA?aA:eA/rA,width:$>rA?aA*rA:eA}})(),fit:"cover",url:r,onClick:eA=>{var aA;eA.stopPropagation(),V(0),Y([{name:h,url:r},{name:U,url:(aA=m==null?void 0:m.source)!=null?aA:""}])}})}),t.jsx(Le,{url:(vA=m==null?void 0:m.source)!=null?vA:"",fit:"contain",className:oA.defaultCardPhotoItem,onClick:eA=>{var aA;eA.stopPropagation(),V(1),Y([{name:h,url:r},{name:U,url:(aA=m==null?void 0:m.source)!=null?aA:""}])}})]})]})]}),hA=t.jsxs("div",{className:oA.SelectableCard,onClick:()=>{T(C===p?S:p)},children:[t.jsxs("div",{className:oA.SelectableCardHeader,children:[t.jsx("span",{children:A.formatMessage({id:"moreColors"})}),t.jsxs("div",{className:oA.SelectableCardHeaderRight,children:[t.jsx("span",{className:oA.SelectableCardPrice,children:k&&P?x:""}),t.jsx("div",{className:N(oA.SelectableCardCheckBox,C===S?oA.active:oA.normal)})]})]}),t.jsx("div",{className:oA.SelectableCardBody,children:t.jsx("div",{className:oA.SelectableCardList,children:b.map((eA,aA)=>t.jsx(Le,{style:{width:100/390*window.innerWidth,height:100/390*window.innerWidth/(L.width/L.height)},url:eA,onClick:$=>{$.stopPropagation(),V(aA),Y(b.map(rA=>({name:h,url:rA})))}},aA))})})]}),H=t.jsxs("div",{className:oA.footer,children:[t.jsxs("div",{className:oA.purchaseBox,children:[t.jsxs("div",{children:[t.jsxs("span",{className:oA.purchasePriceLabel,children:[A.formatMessage({id:"total"}),":"]}),t.jsx("span",{className:oA.purchasePrice,children:B?B.localPrice:""})]}),t.jsx("div",{className:oA.purchaseBtn,onClick:()=>{XA(W)},children:A.formatMessage({id:"purchase",defaultMessage:"Purchase"})})]}),t.jsx("div",{className:oA.safeBottom})]});return t.jsxs(Ke,{children:[t.jsx(se,{className:oA.appBar,clickToTop:!0,title:A.formatMessage({id:"payment",defaultMessage:"Payment"}),backable:!0,onBack:()=>{e.goBack()}}),t.jsx(ie,{className:oA.content,children:t.jsxs("div",{className:oA.wrap,children:[F,hA]})}),H,t.jsx(Vm,{photos:z,index:D,visible:z.length>0,onClose:()=>Y([]),hasBtn:!1})]})}),Tg="_content_1mls0_1",xg="_appBar_1mls0_4",Cg="_container_1mls0_7",Pg="_image_1mls0_13",zg="_imageContainer_1mls0_18",jg="_sizeInfo_1mls0_24",Bg="_switchBtn_1mls0_34",Mg="_footer_1mls0_44",Ig="_purchaseBox_1mls0_53",Eg="_purchasePrice_1mls0_61",Ng="_purchasePriceLabel_1mls0_66",Rg="_purchaseBtn_1mls0_71",Dg="_safeBottom_1mls0_82",qA={content:Tg,appBar:xg,container:Cg,image:Pg,imageContainer:zg,sizeInfo:jg,switchBtn:Bg,footer:Mg,purchaseBox:Ig,purchasePrice:Eg,purchasePriceLabel:Ng,purchaseBtn:Rg,safeBottom:Dg},Lg=fe(()=>{const A=nA(),a=Me(),e=UA(),i=Yo(),{searchParams:n}=Qe(),s=(n==null?void 0:n.get("originPath"))||"",r=(n==null?void 0:n.get("resultPath"))||"",m=(n==null?void 0:n.get("from"))||"",u=m==="size",b=(n==null?void 0:n.get("size"))||"",y=(n==null?void 0:n.get("color"))||"",h=(n==null?void 0:n.get("colorLabel"))||"",_=m==="color"?"change_color":"change_size",f=u,{sizeDisplay:v}=NA(),c=o.useMemo(()=>b?b.split("x").map(P=>parseFloat(P)||0):[],[b]),d=oe();BA(()=>{uA()?d.event("snapid_page_event",{page_id:"payment_page_view",project:u?"change_size":"change_color"}):mA()&&d.event("w_subscription_enter",{source_module:"p_homepage",source_0:"snap_id",source_1:u?"change_size":"change_color",sale_status:"normal"})},[d,u]);const l=o.useCallback(async()=>{const{data:P}=await _t.batchUpload({storageType:1,data:[{type:0,data:r}]});if(!P.every(C=>C.code===0))throw new Error(A.formatMessage({id:"network_error_info"}));return P.map(C=>C.url)},[A,r]);BA(()=>(we()<=0&&EA.antiScreenshot({open:!0,antiBtnName:A.formatMessage({id:"ok"})}),()=>{EA.antiScreenshot({open:!1})}),[]);const g=He(async()=>{var P;uA()?d.event("snapid_page_button_clk",{clk:"purchase",project:u?"change_size":"change_color",size_value:b.replace("x","*")||void 0,background:h||void 0}):mA()&&d.event("w_subscription_click",{source_module:"p_homepage",source_0:"snap_id",source_1:u?"change_size":"change_color",sale_status:"normal",duration:"times_1",SKU:(i==null?void 0:i.productIdentifier)||""}),a.loading();try{we()>0&&await ba();const x=await l(),[C]=x;let T={};uA()?T=u?{size_value:c.join("*")}:{background:h||void 0}:mA()&&(T=u?{size:c.join("*")}:{background:y||void 0});const B={type:u?"resize":"recolor",params:u?{size:c}:{background:y},photos:[{name:u?v:sA("background_updated"),url:C,aspectRatio:c.length>=2?c[0]/c[1]:void 0}],sku:(i==null?void 0:i.productIdentifier)||"",product:va(),report:T},{data:w}=await zi({urls:x,goods_id:va(),ext:B});if(!(w!=null&&w.task_id))throw new Error(A.formatMessage({id:"network_error_info"}));let I="",E="";const W=we()>0;if(!W)try{if({orderID:I,transactionID:E}=await aa.purchase({sku:(P=i==null?void 0:i.productIdentifier)!=null?P:""}),!I)throw new Error(A.formatMessage({id:"paymentFailure"}))}catch(L){throw new Error(A.formatMessage({id:"paymentFailure"}))}try{await kt({task_id:w.task_id,aw_trans_id:I,voucher_ids:Pi()})}catch(L){throw I&&w.task_id&&vt.getState().add(I,w.task_id),L}await qe.getState().initiate().catch(()=>{}),ba(),uA()?d.event("snapid_consume",{project:u?"change_size":"change_color",size_value:u?c.join("*"):void 0,background:u?void 0:h,amount:!W&&i?i.localPrice:0,sku_id:(i==null?void 0:i.productIdentifier)||"",order_id:I}):mA()&&d.event("w_subscription_success",{source_module:"p_homepage",source_0:"snap_id",source_1:u?"change_size":"change_color",sale_status:"normal",order_id:E,duration:"times_1",SKU:(i==null?void 0:i.productIdentifier)||""}),a.hide();try{mA()&&d.event("edit_save",{prf_func_saved:"snapid"}),await ZA.save({type:0,data:r}),a.success(A.formatMessage({id:"save_success"})),e.popToRoot()}catch(L){a.error(A.formatMessage({id:"save_failed"})),e.push(`/task?key=id&source=${_}`)}}catch(x){a.error((x==null?void 0:x.message)||A.formatMessage({id:"paymentFailure"}))}},[l,e]),[p,k]=o.useState(!0),S=o.useCallback(()=>{k(P=>!P)},[]);return t.jsxs(Ke,{children:[t.jsx(se,{className:qA.appBar,clickToTop:!0,title:A.formatMessage({id:"payment",defaultMessage:"Payment"}),backable:!0}),t.jsx(ie,{className:qA.content,children:t.jsxs("div",{className:qA.container,children:[t.jsx("div",{className:qA.imageContainer,children:t.jsx(Le,{fit:"contain",className:qA.image,url:ea.convertFileSrc(s),style:{opacity:p?0:1,width:window.innerWidth/390*330,height:window.innerWidth/390*423}})}),t.jsxs("div",{className:qA.imageContainer,children:[t.jsx(Le,{fit:"contain",className:qA.image,style:{width:window.innerWidth/390*330,height:c.length>0?window.innerWidth/390*330*(c[1]/c[0]):window.innerWidth/390*423,opacity:p?1:0},url:ea.convertFileSrc(r)}),v&&f&&t.jsx("div",{className:qA.sizeInfo,style:{opacity:p?1:0},children:v})]}),t.jsx("div",{className:qA.switchBtn,onTouchStart:S,onTouchEnd:S})]})}),t.jsxs("div",{className:qA.footer,children:[t.jsxs("div",{className:qA.purchaseBox,children:[t.jsxs("div",{children:[t.jsxs("span",{className:qA.purchasePriceLabel,children:[A.formatMessage({id:"total"}),":"]}),t.jsx("span",{className:qA.purchasePrice,children:i?i.localPrice:""})]}),t.jsx("div",{className:qA.purchaseBtn,onClick:g,children:A.formatMessage({id:"purchase",defaultMessage:"Purchase"})})]}),t.jsx("div",{className:qA.safeBottom})]})]})}),Og="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/4-5SsHiQdV.jpg",Gg="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/5-rASn5MYP.jpg",Fg="https://static.fe.pixocial.com/miniapp.snapid.ai/assets/6-Jte6Q4K3.jpeg",Ug="_content_1hcvw_1",Hg="_appbar_1hcvw_5",Kg="_container_1hcvw_9",qg="_btn_1hcvw_22",Qg="_disabled_1hcvw_34",Vg="_controlTitle_1hcvw_38",Jg="_controlItem_1hcvw_44",Wg="_controlBox_1hcvw_47",Yg="_positionBox_1hcvw_50",Zg="_photoBox_1hcvw_59",Xg="_photoStaff_1hcvw_66",$g="_photoStaffArrow_1hcvw_75",Ap="_photoStaffText_1hcvw_101",ep="_typeSegment_1hcvw_122",ap="_sizeContainer_1hcvw_145",tp="_sizeBox_1hcvw_150",ip="_sizeItem_1hcvw_154",np="_sizeTitle_1hcvw_157",op="_sizeInput_1hcvw_162",sp="_sizeSelect_1hcvw_177",rp="_sizeSelectLabel_1hcvw_180",cp="_active_1hcvw_193",lp="_sizeSelectBox_1hcvw_206",dp="_sizeErrorMessage_1hcvw_227",up="_modalContent_1hcvw_232",iA={content:Ug,appbar:Hg,container:Kg,btn:qg,disabled:Qg,controlTitle:Vg,controlItem:Jg,controlBox:Wg,positionBox:Yg,photoBox:Zg,photoStaff:Xg,photoStaffArrow:$g,photoStaffText:Ap,typeSegment:ep,sizeContainer:ap,sizeBox:tp,sizeItem:ip,sizeTitle:np,sizeInput:op,sizeSelect:sp,sizeSelectLabel:rp,active:cp,sizeSelectBox:lp,"select-box-slide-down":"_select-box-slide-down_1hcvw_1",sizeErrorMessage:dp,modalContent:up},he={closer:{url:Og,radio:[9,76,15]},medium:{url:Gg,radio:[11,64,25]},farther:{url:Fg,radio:[8,49,43]}},_p=({type:A,onChange:a})=>{const e=nA(),i=o.useMemo(()=>he[A],[A]),[n=0,s=0]=i.radio;return t.jsxs("section",{className:iA.controlItem,children:[t.jsx("div",{className:iA.controlTitle,children:e.formatMessage({id:"head_position"})}),t.jsxs("div",{className:iA.positionBox,children:[t.jsxs("div",{className:iA.photoBox,children:[t.jsx(Le,{fit:"contain",className:iA.photoImage,url:i.url}),t.jsxs("div",{className:iA.photoStaff,style:{top:`${n}%`,height:`${s}%`},children:[t.jsx("div",{className:iA.photoStaffArrow}),t.jsxs("div",{className:iA.photoStaffText,children:[s,"%"]})]})]}),t.jsx(hn,{className:iA.typeSegment,value:A,onIonChange:r=>a(r.detail.value),children:Object.keys(he).map(r=>t.jsx(fn,{value:r,children:t.jsx(bn,{children:e.formatMessage({id:r})})},r))})]})]})},mp=({value:A,onChange:a,onError:e})=>{const i=nA(),[n,s]=o.useState(!1),r=o.useMemo(()=>[{label:i.formatMessage({id:"mm"}),value:kA.mm},{label:i.formatMessage({id:"inch"}),value:kA.inch},{label:i.formatMessage({id:"px"}),value:kA.px}],[i]),m=o.useMemo(()=>{var c;return(c=r.find(d=>d.value===A.unit))==null?void 0:c.label},[r,A]),u=c=>{var l,g,p,k,S,P;const d={...A};A.unit===kA.inch&&c!==kA.inch&&(((l=d.width)!=null&&l.includes(".")||(g=d.width)!=null&&g.includes(",")||(p=d.width)!=null&&p.includes("，"))&&(d.width=""),((k=d.height)!=null&&k.includes(".")||(S=d.height)!=null&&S.includes(",")||(P=d.height)!=null&&P.includes("，"))&&(d.height="")),a({...d,unit:c})},b=(c,d)=>{let l;if(A.unit!==kA.inch){const[g]=c.match(/(\d|[\u0660-\u0669])+/g)||[""];l=g}else{const[g]=c.match(/(\d|[\u0660-\u0669])+(\.|,|，)?\d*/g)||[""];l=g}a({...A,[d]:l})},y=o.useMemo(()=>gp[A.unit],[A]),h=o.useMemo(()=>{const{width:c="",height:d=""}=A;let l,g;const p=fa(c),k=fa(d);return A.unit==="inch"?(l=p?parseFloat(p):void 0,g=k?parseFloat(k):void 0):(l=p?parseInt(p,10):void 0,g=k?parseInt(k,10):void 0),{width:l,height:g}},[A]),_=o.useMemo(()=>{const{width:c,height:d}=h,l=JSON.parse(JSON.stringify(y)),g=JSON.parse(JSON.stringify(l));if(c||d){if(c){let p=0,k=0;c*.5<=l.height.min?p=l.height.min:c*.5>l.height.min&&c*.5<l.height.max?p=c*.5:p=l.height.max,c*2>=l.height.max?k=l.height.max:c*2>p&&c*2<l.height.max?k=c*2:k=p,g.height={min:p,max:k}}if(d){let p=0,k=0;d*.5<=l.width.min?p=l.width.min:d*.5>l.width.min&&d*.5<l.width.max?p=d*.5:p=l.width.max,d*2>=l.width.max?k=l.width.max:d*2>p&&d*2<l.width.max?k=d*2:k=p,g.width={min:p,max:k}}}return g},[y,h]),f=o.useMemo(()=>{const{width:c,height:d}=h,l=_;if(d&&(d<l.height.min||d>l.height.max)||d===0)return e(!0),{status:"error",message:i.formatMessage({id:"height_range_should_be_within_10120"},{min:l.height.min,max:l.height.max})};if(c&&(c<l.width.min||c>l.width.max)||c===0)return e(!0),{status:"error",message:i.formatMessage({id:"width_range_should_be_within_10120"},{min:l.width.min,max:l.width.max})};if(c&&d){const g=c/d;if(g<.5||g>2){e(!0);const p=c*.5<l.height.min?l.height.min:c*.5,k=c*2>l.height.max?l.height.max:c*2;return{status:"error",message:i.formatMessage({id:"height_range_should_be_within_10120"},{min:p,max:k})}}}return e(!1),{status:"success"}},[h,i,_,e]);o.useEffect(()=>{const c=document.querySelector(`.${iA.container}`),d=c==null?void 0:c.parentElement;return Aa.addListener("keyboardWillShow",l=>{d&&setTimeout(()=>{d.style.transform=`translateY(-${l.keyboardHeight}px)`},100)}),Aa.addListener("keyboardWillHide",()=>{d&&(d.style.transform="translateY(0px)")}),()=>{Aa.removeAllListeners()}},[]);const v=o.useMemo(()=>A.unit===kA.inch?"decimal":"numeric",[A.unit]);return o.useMemo(()=>A.unit===kA.inch?"text":"number",[A.unit]),t.jsxs("section",{className:iA.controlItem,children:[t.jsx("div",{className:iA.controlTitle,children:i.formatMessage({id:"photo_size"})}),t.jsxs("div",{className:iA.sizeContainer,children:[t.jsxs("div",{className:iA.sizeBox,children:[t.jsxs("div",{className:iA.sizeItem,children:[t.jsx("div",{className:iA.sizeTitle,children:i.formatMessage({id:"width"})}),t.jsx("div",{className:iA.sizeInput,children:t.jsx("input",{value:A.width,inputMode:v,onChange:c=>b(c.target.value,"width"),type:"text",placeholder:`${_.width.min}-${_.width.max}`})})]}),t.jsxs("div",{className:iA.sizeItem,children:[t.jsx("div",{className:iA.sizeTitle,children:i.formatMessage({id:"height"})}),t.jsx("div",{className:iA.sizeInput,children:t.jsx("input",{value:A.height,inputMode:v,onChange:c=>b(c.target.value,"height"),type:"text",placeholder:`${_.height.min}-${_.height.max}`})})]}),t.jsx("div",{className:iA.sizeItem,children:t.jsxs("div",{className:iA.sizeSelect,children:[t.jsx("div",{className:N(iA.sizeSelectLabel,n&&iA.active),onClick:()=>s(c=>!c),children:t.jsx("span",{children:m})}),n&&t.jsx("div",{className:N(iA.sizeSelectBox),children:r.filter(c=>c.value!==A.unit).map(c=>t.jsx("span",{onClick:()=>{u(c.value),s(!1)},children:c.label}))})]})})]}),f.message&&t.jsx("div",{className:iA.sizeErrorMessage,children:f.message})]})]})},gp={[kA.mm]:{width:{min:10,max:80},height:{min:10,max:120}},[kA.inch]:{width:{min:.4,max:3.15},height:{min:.4,max:4.72}},[kA.px]:{width:{min:100,max:800},height:{min:100,max:1200}}},Wi=({onConfirm:A,onBeforeConfirm:a,onChange:e,info:i,type:n})=>{const s=nA(),[r,m]=o.useState(n||"medium"),[u,b]=o.useState(i||{unit:kA.mm}),y=d=>{e==null||e({type:d,sizeInfo:u}),m(d)},h=d=>{e==null||e({type:r,sizeInfo:d}),b(d)},[_,f]=o.useState(!1),v=o.useMemo(()=>!!(_||!u.width||!u.height),[_,u]),c=()=>{if(v)return;let{width:d="",height:l=""}=u;if(d=fa(d),l=fa(l),a==null||a({type:r,sizeInfo:{unit:u.unit,width:u.unit==="inch"?parseFloat(d):parseInt(d,10),height:u.unit==="inch"?parseFloat(l):parseInt(l,10)}}),d&&l){let g;u.unit!==kA.mm?u.unit===kA.px?g={width:parseInt(d,10)/10,height:parseInt(l,10)/10}:u.unit===kA.inch&&(g={width:Math.round(parseFloat(d)*25.4*100)/100,height:Math.round(parseFloat(l)*25.4*100)/100}):g={width:parseInt(d,10),height:parseInt(l,10)},g!=null&&g.width&&(g!=null&&g.height)&&A({type:r,sizeInfo:{unit:u.unit,...g}})}};return t.jsxs("div",{className:iA.container,children:[t.jsxs("div",{className:iA.controlBox,children:[t.jsx(_p,{type:r,onChange:y}),t.jsx(mp,{value:u,onChange:h,onError:d=>f(d)})]}),t.jsx("div",{className:N(iA.btn,v&&iA.disabled),onClick:c,children:s.formatMessage({id:"confirm"})})]})},pp=A=>{const a=nA(),{setSizeDisplay:e}=NA(),[i,n]=o.useState("medium"),[s,r]=o.useState(),m=h=>{const{sizeInfo:_,type:f}=h;A.onConfirm(h),A.onClose();const{width:v,height:c}=_;if(!v||!c)return;const d=[v,c];y({key:"custom",name:a.formatMessage({id:"custom_size"}),type:"custom",desc:"",size:d,sizeString:"",sizeDisplay:"",sizeValue:d.join("*"),count:[],lineBottom:0,lineTop:0,lineHeight:0,ratio:he[f].radio,link:"",previewLink:he[f].url,specialNotes:a.formatMessage({id:"generate_id_photo_desc"})}),e(Ba(h.sizeInfo)),NA.getState().setUnit(h.sizeInfo.unit)},u=({type:h,sizeInfo:_})=>{r(_),n(h)},b=()=>{var h;r(void 0),n(void 0),A.onClose(),(h=A.onBack)==null||h.call(A)},{setSizeItem:y}=NA();return t.jsxs(oa,{isOpen:A.isOpen,children:[t.jsx(se,{className:iA.appbar,backable:!0,title:a.formatMessage({id:"custom_size"}),onBack:b}),t.jsx(ie,{className:iA.modalContent,children:A.isOpen&&t.jsx(Wi,{onConfirm:m,onBeforeConfirm:h=>{var _;(_=A.onBeforeConfirm)==null||_.call(A,h),y({key:"custom",name:a.formatMessage({id:"custom_size"}),type:"custom",desc:"",size:[h.sizeInfo.width,h.sizeInfo.height],sizeString:"",sizeDisplay:"",sizeValue:[h.sizeInfo.width,h.sizeInfo.height].join("*"),count:[],lineBottom:0,lineTop:0,lineHeight:0,ratio:he.medium.radio,link:"",previewLink:he.medium.url,specialNotes:a.formatMessage({id:"generate_id_photo_desc"}),originSizeInfo:h.sizeInfo})},onChange:u,type:i,info:s})})]})},hp=fe(()=>{const A=nA(),{setSizeItem:a,setSizeDisplay:e,setUnit:i}=NA(),[n,s]=Ki(),r=({type:m,sizeInfo:u})=>{e(Ba(u));const{width:b,height:y}=u;if(!b||!y)return;const h=[b,y];a({key:"custom",name:A.formatMessage({id:"custom_size"}),type:"custom",desc:"",size:h,sizeString:"",sizeDisplay:"",sizeValue:h.join("*"),count:[],lineBottom:0,lineTop:0,lineHeight:0,ratio:he[m].radio,link:"",previewLink:he[m].url,specialNotes:A.formatMessage({id:"generate_id_photo_desc"})}),s()};return t.jsxs(t.Fragment,{children:[t.jsx(se,{backable:!0,className:iA.appbar,title:A.formatMessage({id:"custom_size"})}),t.jsxs(ie,{className:iA.content,children:[t.jsx(Wi,{onConfirm:r,onBeforeConfirm:({sizeInfo:m})=>{a({key:"custom",name:A.formatMessage({id:"custom_size"}),type:"custom",desc:"",size:[m.width,m.height],sizeString:"",sizeDisplay:"",sizeValue:[m.width,m.height].join("*"),count:[],lineBottom:0,lineTop:0,lineHeight:0,ratio:he.medium.radio,link:"",previewLink:he.medium.url,specialNotes:A.formatMessage({id:"generate_id_photo_desc"}),originSizeInfo:m}),i(m.unit)}}),n]})]})}),fp="data:image/png;base64,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",bp="_body_1x2m5_1",yp="_container_1x2m5_5",kp="_actionBar_1x2m5_15",vp="_editContainer_1x2m5_20",wp="_photoContainer_1x2m5_29",Sp="_selectContainer_1x2m5_32",Tp="_selectItem_1x2m5_41",xp="_selectItemPhoto_1x2m5_47",Cp="_active_1x2m5_58",Pp="_square_1x2m5_73",zp="_selectItemLabel_1x2m5_76",jp="_customSelectItem_1x2m5_81",Bp="_customSelectPhoto_1x2m5_86",Mp="_customSelectImage_1x2m5_107",Ip="_customSelectLabel_1x2m5_111",Ep="_nextBtn_1x2m5_116",Np="_resetBtn_1x2m5_128",Rp="_disabled_1x2m5_137",Dp="_appBar_1x2m5_140",zA={body:bp,container:yp,actionBar:kp,editContainer:vp,photoContainer:wp,selectContainer:Sp,selectItem:Tp,selectItemPhoto:xp,active:Cp,square:Pp,selectItemLabel:zp,customSelectItem:jp,customSelectPhoto:Bp,customSelectImage:Mp,customSelectLabel:Ip,nextBtn:Ep,resetBtn:Np,disabled:Rp,appBar:Dp},Za=["51x51","30x30","30x40","33x48","35x45","35x49","40x40","40x60","50x60","51x76"],Xa=-99,Lp=A=>{const a=nA(),{size:e}=A,[i,n]=o.useMemo(()=>e.split("x").map(r=>parseInt(r,10)||0),[e]),s=o.useMemo(()=>{const r=window.innerWidth/390*i,m=window.innerWidth/390*n;return{width:r,height:m}},[i,n]);return t.jsxs("div",{className:zA.selectItem,children:[t.jsx("div",{onClick:A.onClick,className:N(zA.selectItemPhoto,A.active?zA.active:void 0),children:t.jsx("div",{className:s.width===s.height?zA.square:void 0,style:{width:s.width,height:s.height}})}),t.jsxs("div",{className:zA.selectItemLabel,children:[A.size,a.formatMessage({id:"mm"})]})]},A.size)},Op=fe(()=>{const A=nA(),a=UA(),{setSizeDisplay:e}=NA(),[i,n]=o.useState(0),[s,r]=o.useState(),[m,u]=o.useState(dt),b=o.useCallback(T=>{n(T),u(dt),r(void 0)},[]),y=o.useMemo(()=>s&&i===Xa?s.slice(0,2):(Za[i]||Za[0]).split("x").map(B=>parseInt(B,10)||0),[i,s]);o.useEffect(()=>{var T,B;e(Ba({width:(T=s||y)==null?void 0:T[0],height:(B=s||y)==null?void 0:B[1],unit:(s==null?void 0:s[2])||kA.mm}))},[s,y,e]);const[h,_]=st(T=>[T.image,T.reset]),f=oe();BA(()=>{h&&(uA()?f.event("snapid_page_event",{page_id:"edit_page_view",project:"change_size"}):mA()&&(f.event("func_enter_snap_headshot"),f.event("edit_enter",{edit_enter_from:"snapid"})))},[f,h]);const v=Me(),c=o.useRef(null),d=o.useCallback(async()=>{var T;if(uA()&&f.event("snapid_page_button_clk",{clk:"edit",project:"change_size",size_value:y.join("*")}),!!c.current)try{v.loading();const B=c.current.generate("#ffffff"),w=await gt.detectSingleFace({source:ze(B),sourceType:"base64"});if(w.count!==1){if(w.count>1)throw new Error(A.formatMessage({id:"error3"}));if(w.count===0)throw new Error(A.formatMessage({id:"error2"}))}const{file:I}=await je.base64ToFile({suffix:"jpg",data:ze(B)}),{file:E}=await je.base64ToFile({suffix:"jpg",data:ze((T=h==null?void 0:h.source)!=null?T:"")});if(!I&&!E)throw new Error("Error.");const W={originPath:E,resultPath:I,from:"size",size:y.join("x")},L=new URLSearchParams(W).toString();try{await St.getState().initiateIfNeed()}catch(z){v.error(sA("network_error_info"));return}v.hide(),a.push(`/extral/pay?${L}`)}catch(B){v.error((B==null?void 0:B.message)||A.formatMessage({id:"error1"}))}},[h==null?void 0:h.source,y,a,f,A,v]),l=VA(d);BA(()=>{we()<=0&&EA.antiScreenshot({open:!0,antiBtnName:A.formatMessage({id:"ok"})});const T=EA.addListener("click",()=>{var B;!WA.getState().current&&((B=Ra.getState().current)==null?void 0:B.type)!=="loading"&&XA(()=>{v.loading(),setTimeout(()=>{l.current()},1e3)})});return()=>{EA.antiScreenshot({open:!1}),T.remove()}},[A]);const g=o.useRef(!1),p=o.useCallback(T=>{T.hasWhite?v.error(A.formatMessage({id:"whiteEdgeTips",defaultMessage:"Please provide a portrait photo with a wider range"})):g.current||(g.current=!0,v.text(A.formatMessage({id:"adjustTips"})))},[v,A]),{setUnit:k}=NA(),[S,P]=o.useState(!1),x=T=>{T.sizeInfo.width&&T.sizeInfo.height&&r([T.sizeInfo.width,T.sizeInfo.height,T.sizeInfo.unit]),k(T.sizeInfo.unit),T.type&&u(he[T.type].radio)},C=()=>{P(!0),n(Xa)};return h!=null&&h.source?t.jsxs(Ke,{children:[t.jsx(se,{className:zA.appBar,title:A.formatMessage({id:"edit_title",defaultMessage:"Edit"}),backable:!0,onBack:()=>{a.goBack()}}),t.jsxs(ie,{className:zA.body,children:[t.jsxs("div",{className:zA.container,children:[t.jsx("div",{className:zA.editContainer,children:t.jsx(Vi,{ref:c,className:zA.photoContainer,idPhotoSize:y,photo:(h==null?void 0:h.source)||"",onFocus:p,photoRatio:m,allowWhiteEdge:!0,reset:!0})}),t.jsxs("div",{className:zA.actionBar,children:[t.jsxs("div",{className:zA.selectContainer,children:[Za.map((T,B)=>t.jsx(Lp,{onClick:()=>b(B),size:T,active:B===i},B)),t.jsxs("div",{className:zA.customSelectItem,onClick:C,children:[t.jsx("div",{className:N(zA.customSelectPhoto,i===Xa&&zA.active),children:t.jsx(xA,{className:zA.customSelectImage,url:fp})}),t.jsx("p",{className:zA.customSelectLabel,children:A.formatMessage({id:"custom"})})]})]}),t.jsx("div",{className:zA.nextBtn,onClick:()=>{XA(d)},children:A.formatMessage({id:"next"})})]})]}),t.jsx(pp,{onConfirm:x,isOpen:S,onClose:()=>P(!1)})]})]}):null}),Ht=Object.assign({"./IDPhoto/color/blue.jpg":Bn,"./IDPhoto/color/cyan.jpg":Mn,"./IDPhoto/color/dark_gray.jpg":In,"./IDPhoto/color/dark_pink.jpg":En,"./IDPhoto/color/deep_blue.jpg":Nn,"./IDPhoto/color/deongaree.jpg":Rn,"./IDPhoto/color/golden.jpg":Dn,"./IDPhoto/color/goose_yellow.jpg":Ln,"./IDPhoto/color/grey.jpg":On,"./IDPhoto/color/index.tsx":no,"./IDPhoto/color/light_blue.jpg":Kn,"./IDPhoto/color/light_orange.jpg":io,"./IDPhoto/color/lotus_color.jpg":qn,"./IDPhoto/color/pale_blue.jpg":Qn,"./IDPhoto/color/pale_white.jpg":Vn,"./IDPhoto/color/pink.jpg":Jn,"./IDPhoto/color/premium_blue.jpg":Wn,"./IDPhoto/color/purple.jpg":Yn,"./IDPhoto/color/real_yellow.jpg":to,"./IDPhoto/color/rose_red.jpg":ao,"./IDPhoto/color/sand.jpg":eo,"./IDPhoto/color/sky_blue.jpg":Ao,"./IDPhoto/color/turf_green.jpg":Zn,"./IDPhoto/color/us_deep_blue.jpg":Gn,"./IDPhoto/color/us_gray_blue.jpg":Fn,"./IDPhoto/color/us_gray_white.jpg":Un,"./IDPhoto/color/us_light_blue.jpg":Hn,"./IDPhoto/color/white.jpg":Xn,"./IDPhoto/color/yellow.jpg":$n}),Gp="_content_p326r_1",Fp="_preview_p326r_7",Up="_preview__container_p326r_14",Hp="_preview__image_p326r_20",Kp="_footer_p326r_25",qp="_colors_p326r_33",Qp="_color_p326r_33",Vp="_color_active_p326r_64",Jp="_save_p326r_75",Wp="_payment_p326r_87",ge={content:Gp,preview:Fp,preview__container:Up,preview__image:Hp,footer:Kp,colors:qp,color:Qp,color_active:Vp,save:Jp,payment:Wp},Yp=fe(()=>{const A=nA(),a=UA(),[e,i,n]=ot(k=>[k.image,k.mattedImage,k.reset]);yn(()=>{n()});const s=oe();BA(()=>{e&&(uA()?s.event("snapid_page_event",{page_id:"edit_page_view",project:"change_color"}):mA()&&(s.event("func_enter_snap_headshot"),s.event("edit_enter",{edit_enter_from:"snapid"})))},[s,e]);const[r,m]=o.useState(0),u=o.useMemo(()=>i||(e?ea.convertFileSrc(e.source):""),[e,i]),[b,y]=o.useState({width:0,height:0}),h=o.useMemo(()=>({...b,backgroundImage:`url(${Ht[`./IDPhoto/color/${pA[r].name}.jpg`].default})`,backgroundRepeat:"no-repeat",backgroundSize:"cover"}),[r,b]),[_,f]=kn();o.useEffect(()=>{if(e){const k=f.width*(e.height/e.width);k>f.height?y({width:f.width*(f.height/k),height:f.height}):y({width:f.width,height:k})}},[e,f]);const[v,c]=o.useState(!1),d=o.useCallback(async()=>{const[k,S]=await Promise.all([te(Ht[`./IDPhoto/color/${pA[r].name}.jpg`].default),te(i)]),P=document.createElement("canvas"),x=P.getContext("2d");return P.width=S.naturalWidth,P.height=S.naturalHeight,x?(x.drawImage(k,0,0,k.naturalWidth,k.naturalHeight,0,0,S.naturalWidth,S.naturalHeight),x.drawImage(S,0,0,S.naturalWidth,S.naturalHeight),P.toDataURL("image/png",1)):""},[i,r]),l=o.useCallback(async()=>{var k;uA()&&s.event("snapid_page_button_clk",{clk:"edit",project:"change_color",background:pA[r].label});try{jA.loading();const S=await d(),{file:P}=await je.base64ToFile({suffix:"jpg",data:((k=S.split("base64,"))==null?void 0:k[1])||""}),x={originPath:ea.convertFileSrc((e==null?void 0:e.source)||"")||"",resultPath:P,from:"color",color:pA[r].name,colorLabel:pA[r].label},C=new URLSearchParams(x).toString();try{await St.getState().initiateIfNeed()}catch(T){jA.error(sA("network_error_info"));return}jA.hide(),a.push(`/extral/pay?${C}`)}catch(S){jA.error(sA("failed"))}},[s,r,d,a,e]),g=VA(l),p=Lo();return BA(()=>{if(e){p<=0&&EA.antiScreenshot({open:!0,antiBtnName:A.formatMessage({id:"ok"})});const k=EA.addListener("click",()=>{var S;!WA.getState().current&&((S=Ra.getState().current)==null?void 0:S.type)!=="loading"&&XA(()=>{jA.loading(),setTimeout(()=>{g.current()},1e3)})});return()=>{EA.antiScreenshot({open:!1}),k.remove()}}return()=>{}},[A,e,p]),e?t.jsxs(Ke,{children:[t.jsx(se,{clickToTop:!0,title:A.formatMessage({id:"edit_title"}),backable:!0,onBack:()=>{a.goBack()}}),t.jsx(ie,{style:{"--background":"transparent","--overflow":"hidden"},children:t.jsxs("div",{className:ge.content,children:[t.jsx("div",{className:ge.preview,children:t.jsx("div",{ref:_,className:ge.preview__container,children:t.jsx("div",{className:ge.preview__content,style:h,children:u?t.jsx("img",{className:ge.preview__image,src:u}):null})})}),t.jsxs("div",{className:ge.footer,children:[t.jsx("div",{className:ge.colors,children:pA.map((k,S)=>t.jsx("div",{className:N(ge.color,{[ge.color_active]:r===S}),style:{backgroundImage:`url('${k.bg}')`,backgroundRepeat:"no-repeat",backgroundSize:"cover"},onClick:()=>{m(S)}},S))}),t.jsx("div",{className:ge.save,onClick:()=>{XA(l)},children:A.formatMessage({id:"next"})})]})]})}),t.jsx(oa,{className:ge.payment,position:"bottom",backdropDismiss:!0,handle:!1,isOpen:v,initialBreakpoint:1,breakpoints:[1],onClose:()=>{c(!1)},children:"..."})]}):null});vn({mode:"ios",swipeBackEnabled:!0});const Zp=[{path:"/",element:t.jsx(xc,{})},{path:"/paper",element:t.jsx(Au,{}),swipeGesture:!1},{path:"/IDPhoto/adjust",element:t.jsx(Nu,{}),swipeGesture:!1},{path:"/IDPhoto/customSize",element:t.jsx(hp,{}),swipeGesture:!1},{path:"/IDPhoto/make",element:t.jsx(Mm,{}),swipeGesture:!1},{path:"/IDPhoto/pay",element:t.jsx(Sg,{}),swipeGesture:!1},{path:"/extral/pay",element:t.jsx(Lg,{}),swipeGesture:!1},{path:"/tools/resize",element:t.jsx(Op,{}),swipeGesture:!1},{path:"/tools/recolor",element:t.jsx(Yp,{}),swipeGesture:!1},{path:"/task",element:t.jsx(Qc,{}),swipeGesture:!1},{path:"/task/detail",element:t.jsx(dl,{}),swipeGesture:!1}],Xp=()=>{const[A,a,e,i]=Ko(),n=(A==null?void 0:A.language)||"en";o.useEffect(()=>{i()},[i]),o.useEffect(()=>{const b=IA.addListener("share",()=>{let h="https://snapid.ai";uA()?h="https://9dld.adj.st/p_miniapp?version=v2&appid=snapid&adj_t=1aiq2tq1":mA()&&(ka()===0?h="https://apps.apple.com/app/id998411110":h="https://play.google.com/store/apps/details?id=com.magicv.airbrush"),IA.share({link:h})}),y=IA.addListener("appStateChange",h=>{var _,f;ka()===0&&!h.isActive&&((f=(_=document.activeElement)==null?void 0:_.blur)==null||f.call(_))});return()=>{b.remove(),y.remove()}},[]);const s=o.useRef(null),r=o.useCallback(()=>{var b;return(((b=s.current)==null?void 0:b.canGoBack())||Promise.resolve(!1)).then(y=>{var h;return y&&((h=s.current)==null||h.goBack()),y})},[]);let m=null;e?m=t.jsx(wa,{fullscreen:!0,status:"loading",onLoad:i}):a&&(m=t.jsx(wa,{fullscreen:!0,status:at(a)?"offline":"error",onLoad:i}));const u=o.useMemo(()=>t.jsx(rs,{children:t.jsxs(ts,{ref:s,className:Ja.navigator,root:"/",routes:Zp,children:[t.jsx(Ys,{}),t.jsx(or,{}),t.jsx(ur,{}),t.jsx(Tr,{})]})}),[]);return t.jsx(Ws,{children:t.jsxs(wn,{locale:n,messages:et[n]||et.en,children:[t.jsx(Ro,{}),t.jsx(cs,{goBack:r,children:t.jsxs(Sn,{className:N(Ja.app,{[Ja.app_loaded]:!m},"app"),children:[u,m]})})]})})};window.lang="en",window.innerHeight/window.innerWidth<=2&&document.documentElement.setAttribute("screen","small"),/android/i.test(navigator.userAgent)&&document.documentElement.setAttribute("system","android");const Kt=document.getElementById("root");Kt&&Tn(Kt).render(t.jsx(Xp,{}));

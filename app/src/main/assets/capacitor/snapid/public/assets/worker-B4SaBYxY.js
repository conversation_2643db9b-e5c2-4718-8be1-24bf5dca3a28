(function(){"use strict";function L2(r,A){if(!(r instanceof A))throw new TypeError("Cannot call a class as a function")}var P2=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],q2=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function F2(r,A,T,B,k,w){for(var a=r.data,y=2*w+1,z=B-1,R=k-1,d=w+1,h=d*(d+1)/2,V=new E2,o=V,E,c=1;c<y;c++)o=o.next=new E2,c===d&&(E=o);o.next=V;for(var f=null,v=null,C=0,l=0,t=P2[w],n=q2[w],x=0;x<k;x++){o=V;for(var m=a[l],_=a[l+1],t2=a[l+2],a2=a[l+3],A2=0;A2<d;A2++)o.r=m,o.g=_,o.b=t2,o.a=a2,o=o.next;for(var N=0,U=0,X=0,Y=0,o2=d*m,r2=d*_,n2=d*t2,i2=d*a2,J=h*m,K=h*_,Q=h*t2,W=h*a2,H=1;H<d;H++){var Z=l+((z<H?z:H)<<2),B2=a[Z],R2=a[Z+1],k2=a[Z+2],w2=a[Z+3],$=d-H;J+=(o.r=B2)*$,K+=(o.g=R2)*$,Q+=(o.b=k2)*$,W+=(o.a=w2)*$,N+=B2,U+=R2,X+=k2,Y+=w2,o=o.next}f=V,v=E;for(var s2=0;s2<B;s2++){var e2=W*t>>n;if(a[l+3]=e2,e2!==0){var f2=255/e2;a[l]=(J*t>>n)*f2,a[l+1]=(K*t>>n)*f2,a[l+2]=(Q*t>>n)*f2}else a[l]=a[l+1]=a[l+2]=0;J-=o2,K-=r2,Q-=n2,W-=i2,o2-=f.r,r2-=f.g,n2-=f.b,i2-=f.a;var D=s2+w+1;D=C+(D<z?D:z)<<2,N+=f.r=a[D],U+=f.g=a[D+1],X+=f.b=a[D+2],Y+=f.a=a[D+3],J+=N,K+=U,Q+=X,W+=Y,f=f.next;var O=v,y2=O.r,z2=O.g,C2=O.b,V2=O.a;o2+=y2,r2+=z2,n2+=C2,i2+=V2,N-=y2,U-=z2,X-=C2,Y-=V2,v=v.next,l+=4}C+=B}for(var q=0;q<B;q++){l=q<<2;var G=a[l],L=a[l+1],P=a[l+2],M=a[l+3],l2=d*G,c2=d*L,d2=d*P,v2=d*M,b=h*G,g=h*L,I=h*P,p=h*M;o=V;for(var D2=0;D2<d;D2++)o.r=G,o.g=L,o.b=P,o.a=M,o=o.next;for(var G2=B,h2=0,x2=0,M2=0,j2=0,u=1;u<=w;u++){l=G2+q<<2;var S=d-u;b+=(o.r=G=a[l])*S,g+=(o.g=L=a[l+1])*S,I+=(o.b=P=a[l+2])*S,p+=(o.a=M=a[l+3])*S,j2+=G,h2+=L,x2+=P,M2+=M,o=o.next,u<R&&(G2+=B)}l=q,f=V,v=E;for(var T2=0;T2<k;T2++){var j=l<<2;a[j+3]=M=p*t>>n,M>0?(M=255/M,a[j]=(b*t>>n)*M,a[j+1]=(g*t>>n)*M,a[j+2]=(I*t>>n)*M):a[j]=a[j+1]=a[j+2]=0,b-=l2,g-=c2,I-=d2,p-=v2,l2-=f.r,c2-=f.g,d2-=f.b,v2-=f.a,j=q+((j=T2+d)<R?j:R)*B<<2,b+=j2+=f.r=a[j],g+=h2+=f.g=a[j+1],I+=x2+=f.b=a[j+2],p+=M2+=f.a=a[j+3],f=f.next,l2+=G=v.r,c2+=L=v.g,d2+=P=v.b,v2+=M=v.a,j2-=G,h2-=L,x2-=P,M2-=M,v=v.next,l+=B}}return r}var E2=function r(){L2(this,r),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};const i=0,s=1,e=2,H2=3;function F(){}F.prototype.mix=function(r,A,T){return r*(1-T)+A*T},F.prototype.clip=function(r){return r+=.5,r<0?r=0:r>255&&(r=255),r},F.prototype.alphaMix=function(r,A,T,B){const k=B,w=r.width*r.height*4;for(let a=0;a<w;a+=4){const y=A.data[a]/255;k.data[a+i]=this.mix(T.data[a+i],r.data[a+i],y),k.data[a+s]=this.mix(T.data[a+s],r.data[a+s],y),k.data[a+e]=this.mix(T.data[a+e],r.data[a+e],y),k.data[a+H2]=255}return k},F.prototype.run=function(r,A,T,B,k,w,a=function(){}){const y=[],z=r.width*r.height,R=z<<2;for(let t=0;t<z;t++)y[t]=A.data[t*4];const d=[];for(let t=0;t<z;t++)d[t]=y[t]>250||y[t]===0;const h=this.alphaMix(r,A,T,B),V=15,o=k;for(let t=0;t<z*4;t++)o.data[t]=h.data[t];F2(o,0,0,o.width,o.height,V);const E=[];for(let t=0,n=0;t<R;t+=4,n++)d[n]||(E[t+i]=h.data[t+i]-o.data[t+i],E[t+s]=h.data[t+s]-o.data[t+s],E[t+e]=h.data[t+e]-o.data[t+e]);const c=[];for(let t=0,n=0;t<R;t+=4,n++)d[n]||(c[t+i]=T.data[t+i]-o.data[t+i],c[t+s]=T.data[t+s]-o.data[t+s],c[t+e]=T.data[t+e]-o.data[t+e]);for(let t=0,n=0;t<R;t+=4,n++)if(!d[n]){const x=Math.sqrt(c[t+i]*c[t+i]+c[t+s]*c[t+s]+c[t+e]*c[t+e])+1e-8;c[t+i]/=x,c[t+s]/=x,c[t+e]/=x}const f=[];for(let t=0,n=0;t<R;t+=4,n++)d[n]||(f[t+i]=E[t+i]*c[t+i],f[t+s]=E[t+s]*c[t+s],f[t+e]=E[t+e]*c[t+e]);for(let t=0,n=0;t<R;t+=4,n++)if(!d[n]){const x=f[t+i]+f[t+s]+f[t+e];c[t+i]*=x,c[t+s]*=x,c[t+e]*=x}const v=[],C=[];for(let t=0,n=0;t<R;t+=4,n++)d[n]||(v[t+i]=E[t+i]-c[t+i],v[t+s]=E[t+s]-c[t+s],v[t+e]=E[t+e]-c[t+e],C[t+i]=v[t+i]*v[t+i],C[t+s]=v[t+s]*v[t+s],C[t+e]=v[t+e]*v[t+e]);for(let t=0,n=0;t<R;t+=4,n++)if(!d[n]){let x=C[t+i]+C[t+s]+C[t+e];x=x**.15+1e-8,v[t+i]/=x,v[t+s]/=x,v[t+e]/=x}for(let t=0,n=0;t<R;t+=4,n++)d[n]||(h.data[t+i]=this.clip(o.data[t+i]+c[t+i]+v[t+i]),h.data[t+s]=this.clip(o.data[t+s]+c[t+s]+v[t+s]),h.data[t+e]=this.clip(o.data[t+e]+c[t+e]+v[t+e]));const l=this.alphaMix(h,A,T,w);return a&&a(l),l},onmessage=r=>{const A=new F().run(r.data[0],r.data[1],r.data[2],r.data[3],r.data[4],r.data[5]);postMessage(A)}})();

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <array>
        <dict>
            <key>FacePart</key>
            <array>
                <dict>
                    <key>Type</key><string>Debug</string>

                    <!-- 显示xyz坐标轴 slam、arkit这些数据 -->
                    <key>ShowGyroInfo</key><string>0</string>

                    <!-- 分割的类型 -1是不需要 0 1 是身体分割 3 4是人脸分割 5是天空分割 7是头发分割-->
                    <key>SegmentType</key><string>-1</string>

                    <!-- 设备信息 -->
                    <key>DeviceInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowDeviceInfo</key><string>0</string>
                        <!-- 逻辑分辨率 -->
                        <key>ShowLogicPixel</key><string>1</string>
                        <!-- 物理分辨率 -->
                        <key>ShowPhysicPixel</key><string>1</string>
                        <!-- 摄像机前后置 -->
                        <key>ShowCameraOrientation</key><string>1</string>
                        <!-- 设备的朝向，就是横屏竖屏 -->
                        <key>ShowDeviceOrientation</key><string>1</string>
                        <!-- 总体的开关 -->
                        <key>ShowViewPixel</key><string>1</string>
                        <!-- 预览比例 -->
                        <key>ShowDevicePreviewResolution</key><string>1</string>
                        <!-- 字的颜色 -->
                        <key>DeviceFontColor</key><string>255.0,128.0,0.0,255.0</string>
                        <!-- 字的坐标 以左上角0，0右下角1，1为标准-->
                        <key>InfoPosition</key><string>0.5,0.0</string>
                    </dict>

                    <!-- 人脸信息 -->
                    <key>FaceInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowFaceInfo</key><string>1</string>
                        <!-- 显示人脸点 -->
                        <key>ShowFacePoint</key><string>1</string>
                        <!-- 显示人脸框 -->
                        <key>ShowFaceRect</key><string>1</string>
                        <!-- 显示人脸ID -->
                        <key>ShowFaceID</key><string>1</string>
                        <!-- 显示人脸索引 -->
                        <key>ShowFaceIndex</key><string>1</string>
                        <!-- 显示性别 -->
                        <key>ShowFaceGender</key><string>1</string>
                        <!-- 显示年龄 -->
                        <key>ShowFaceAge</key><string>1</string>
                        <!-- 显示人种 -->
                        <key>ShowFaceRace</key><string>1</string>
                        <!-- 人脸点类型 -->
                        <key>ShowFacePointType</key><string>118</string>
                        <!-- 人脸点的颜色 -->
                        <key>FacePointColor</key><string>255,0,0</string>
                        <!-- 人脸框的颜色 -->
                        <key>FaceRectColor</key><string>0,255,0</string>
                        <!-- 字的颜色 -->
                        <key>FaceFontColor</key><string>0.0,0.0,255.0,255.0</string>
                    </dict>

                    <key>AnimalInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowAnimalInfo</key><string>0</string>
                        <!-- 显示动物框 -->
                        <key>ShowAnimalRect</key><string>1</string>
                        <!-- 显示动物ID -->
                        <key>ShowAnimalID</key><string>1</string>
                        <!-- 显示动物索引 -->
                        <key>ShowAnimalIndex</key><string>1</string>
                        <!-- 显示动物Score -->
                        <key>ShowAnimalScore</key><string>1</string>
                        <!-- 显示动物标签 -->
                        <key>ShowAnimalLabel</key><string>1</string>
                        <!-- 字的颜色 -->
                        <key>AnimalFontColor</key><string>0.0,0.0,255.0,255.0</string>
                    </dict>



                    <key>SupportInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowSupportInfo</key><string>0</string>
                        <!-- 显示GL版本 -->
                        <key>ShowGLESVersion</key><string>1</string>
                        <!-- 是否支持MSAA -->
                        <key>ShowMSAA</key><string>1</string>
                        <!-- 是否支持物理 -->
                        <key>ShowPhysic</key><string>1</string>
                        <!-- 显示GL拓展 -->
                        <key>ShowGLEXT</key><string>1</string>
                        <!-- GL拓展对应的查询string -->
                        <key>EXTString</key><string>GL_EXT_shader_framebuffer_fetch</string>
                        <!-- 字的颜色 -->
                        <key>SupportFontColor</key><string>255.0,0.0,64.0,255.0</string>
                        <!-- 字的坐标 以左上角0，0右下角1，1为标准-->
                        <key>InfoPosition</key><string>0.0,0.0</string>
                    </dict>

                    <key>HandInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowHandInfo</key><string>1</string>
                        <!-- 显示手部框 -->
                        <key>ShowHandRect</key><string>1</string>
                        <!-- 显示手ID -->
                        <key>ShowHandID</key><string>1</string>
                        <!-- 显示手索引 -->
                        <key>ShowHandIndex</key><string>1</string>
                        <!-- 显示手势 -->
                        <key>ShowHandAction</key><string>1</string>
                        <!-- 显示手部Score -->
                        <key>ShowHandScore</key><string>1</string>
                        <!-- 显示手势Score -->
                        <key>ShowHandActionScore</key><string>1</string>
                        <!-- 显示手的坐标 -->
                        <key>ShowHandPoint</key><string>1</string>
                        <!-- 手部框的颜色 -->
                        <key>HandRectColor</key><string>255.0,0.0,0.0</string>
                        <!-- 字的颜色 -->
                        <key>HandFontColor</key><string>255.0,128.0,255.0,255.0</string>
                    </dict>

                    <!-- 骨骼信息 -->
                    <key>SkeletonInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowSkeletonInfo</key><string>0</string>
                        <!-- 画骨骼线 -->
                        <key>ShowSkeletonLine</key><string>1</string>
                        <!-- 显示对应骨骼的置信度 -->
                        <key>JointIndex</key><string>0,1,2,3,8,9,10,11,12,13</string>
                    </dict>


                    <!-- 耳朵信息 -->    
                    <key>EarInfo</key>
                    <dict>
                        <!-- 总体的开关 -->
                        <key>ShowEarInfo</key><string>1</string>
                        <!-- 显示耳朵点 -->
                        <key>ShowEarPoint</key><string>1</string>
                        <!-- 耳朵点的颜色 -->
                        <key>EarPointColor</key><string>0,255,0</string>
                    </dict>


                    <!-- 绘字的配置属性 -->
                    <key>FontManager</key>
                    <dict>
                        <key>FontLibrary</key>
                        <dict>
                            <key>Path</key>
                            <string>simple.ttf</string>
                            <key>Size</key>
                            <integer>64</integer>
                        </dict>
                        <key>SectionFormat</key>
                        <dict>
                            <key>TextDirection</key>
                            <string>LR</string>
                            <key>LineSpacing</key>
                            <string>TOP_TOP</string>
                            <key>LineSpacingValue</key>
                            <string>1.5</string>
                            <key>LineSpacingValueUnit</key>
                            <string>CHAR</string>
                        </dict>
                        <key>FontFormat</key>
                        <dict>
                            <key>Size</key>
                            <string>20</string>
                            <key>SizeUnit</key>
                            <string>PIXEL</string>
                            <key>Color</key>
                            <string>255.0,255.0,255.0,255.0</string>
                        </dict>
                        <key>FontLayout</key>
                        <dict>
                            <key>IsHoriCenter</key>
                            <integer>0</integer>
                            <key>IsVertCenter</key>
                            <integer>0</integer>
                            <key>MaxLineNumber</key>
                            <integer>1</integer>
                            <key>IsSelfLineNumber</key>
                            <integer>0</integer>
                            <key>IsSelfFontSize</key>
                            <integer>0</integer>
                            <key>HoriMargin</key>
                            <string>0.0</string>
                            <key>VertMargin</key>
                            <string>0.0</string>
                        </dict>
                    </dict>
                </dict>
            </array>
        </dict>
    </array>
</plist>

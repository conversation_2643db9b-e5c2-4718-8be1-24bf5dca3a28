precision highp float;

uniform float textureWidth;
uniform float textureHeight;
#define PI 3.1415926535
//uniform sampler2D ourTexture;

varying vec2 texcoordOut;      // size of texture (screen)
uniform float startPoint[2];
uniform float endPoint[2];
uniform vec3 colors[10];
uniform float locations[10];
uniform int type;// 0-纯色 1-线性渐变 2-圆形渐变 3-扇形渐变
uniform int count;
uniform float alpha;

// 线性渐变。
vec3 linearGradientColor(float sx, float sy, float ex, float ey) {
    vec3 color;
    vec2 st = texcoordOut.xy * vec2(textureWidth, textureHeight);
    sx *= textureWidth;
    sy *= textureHeight;
    ex *= textureWidth;
    ey *= textureHeight;
    float pk = 0.0;
    if(abs(ey - sy) < 0.000001 ){
        float dx = (ex - sx);
        float dxloc[10];
        for(int i =0; i < 10; i++){
            if (i >= count) {
                break;
            }
            dxloc[i] = dx * locations[i];
        }
        
        for(int j =0; j < 10; j++) {
            if (j >= count - 1) {
                break;
            }
            if ((st.x - sx - dxloc[j]) * (st.x - sx - dxloc[j+1]) <= 0.0)
            {
                float diff0 = dxloc[j] - (st.x - sx);
                float diff1 = dxloc[j] - dxloc[j+1];
                color = mix(colors[j], colors[j+1], diff0 / diff1);
                break;
            }
        }
        
        // process point out of defined terminal points
        if((st.x > sx && sx > ex) || (st.x < sx && sx < ex)){
            color = colors[0];
        } else if((st.x > ex && sx < ex) || (st.x < ex && sx > ex)){
            color = colors[count-1];
        }
        
    } else{
        if(abs(ex - sx) > 0.000001){
            float k = (ey - sy)/(ex - sx);
            pk = -1.0/k;
        } else if(abs(ex - sx) < 0.000001){
            pk = 0.0;
        }
        
        float dx = (ex - sx);
        float dy = (ey - sy);
        float dxloc[10];
        float dyloc[10];
        for(int i = 0; i < 10; i++) {
            if (i >= count) {
                break;
            }
            dxloc[i] = dx * locations[i];
            dyloc[i] = dy * locations[i];
        }
        
        for(int j = 0; j < 10; j++){
            if (j >= count - 1) {
                break;
            }
            if ((st.y - pk * (st.x - sx - dxloc[j]) - sy - dyloc[j]) * (st.y - pk * (st.x - sx - dxloc[j+1]) - sy - dyloc[j+1]) <= 0.0)
            {
                float diff0 = pk * ((st.x - sx) - dxloc[j]) + (dyloc[j] - (st.y - sy));
                float diff1 = pk * (dxloc[j+1] - dxloc[j]) + (dyloc[j] - dyloc[j+1]);
                color = mix(colors[j], colors[j+1], diff0 / diff1);
                break;
            }
        }
        
        // process points out of start line and end line
        if(sx <= ex){
            if(((st.y - pk*(st.x - sx -dxloc[0])-sy-dyloc[0]) < 0.0 && pk <= 0.0) ||
               ((st.y - pk*(st.x - sx -dxloc[0])-sy-dyloc[0]) > 0.0 && pk > 0.0)){
                color = colors[0];
            } else if(((st.y - pk*(st.x - sx -dxloc[count-1])-sy-dyloc[count-1]) < 0.0 && pk >0.0)||
                ((st.y - pk*(st.x - sx -dxloc[count-1])-sy-dyloc[count-1]) > 0.0 && pk <= 0.0)){
                color = colors[count-1];
            }
        } else{
            if(((st.y - pk*(st.x - sx -dxloc[0])-sy-dyloc[0]) < 0.0 && pk > 0.0) ||
               ((st.y - pk*(st.x - sx -dxloc[0])-sy-dyloc[0]) > 0.0 && pk < 0.0)){
                color = colors[0];
            } else if(((st.y - pk*(st.x - sx -dxloc[count-1])-sy-dyloc[count-1]) < 0.0 && pk < 0.0) ||
                      ((st.y - pk*(st.x - sx -dxloc[count-1])-sy-dyloc[count-1]) > 0.0 && pk > 0.0)){
                color = colors[count-1];
            }
        }

    }
    return color;
}

// 中心渐变
vec3 centerGradientColor(float sx, float sy, float ex, float ey) {
    vec2 st = texcoordOut;
    vec3 color;
    float widthRatio = textureWidth / textureHeight;
    vec2 centerPoint = vec2(startPoint[0] * widthRatio, startPoint[1]);
    vec2 edgePoint = vec2(endPoint[0] * widthRatio, endPoint[1]);
    vec2 currentPoint = vec2(st.x * widthRatio, st.y);
    float r = distance(centerPoint, edgePoint);
    float dist = distance(centerPoint, currentPoint);
    for (int i = 0; i < 10; i++) {
        if (i >= count - 1) {
            if (dist/r > locations[i]) {
                color = colors[i];
            } else {
                color = colors[0];
            }
            break;
        }
        if (dist/r >= locations[i] && dist/r <= locations[i+1]) {
            float diff0 = dist/r - locations[i];
            float diff1 = locations[i+1] - locations[i];
            color = mix(colors[i], colors[i+1], diff0/diff1);
            break;
        }
    }
    return color;
}

// 伞型渐变
vec3 fanGradientColor(float sx, float sy, float ex, float ey) {
    vec3 color;
    vec2 st = texcoordOut.xy;
    st.y = 1.0 - st.y;
    sy = 1.0 - sy;
    ey = 1.0 - ey;
    st.y = st.y * textureHeight / textureWidth;
    sy = sy * textureHeight / textureWidth;
    ey = ey * textureHeight / textureWidth;
    vec3 v0 = vec3(ex-sx, ey-sy, 0.0);
    vec3 v1 = vec3(st.x-sx, st.y-sy, 0.0);
    
    //calculate cosine value: a*b/(|a|*|b|)
    float dotp = dot(v0, v1);
    float cosval = dotp/(length(v0) * length(v1));
    
    //calculate sine value: |axb|/(|a|*|b|)
    vec3 crossval = cross(v0, v1);
    float sinval = length(crossval)/(length(v0) * length(v1));
    
    //alteration if at the other side of the line
    if(((ey - sy)*(st.x - sx) -(ex - sx)*(st.y-sy)) < 0.0){
        sinval = -sinval;
    }
    
    //calulate arctangent value and convert from -PI:PI to 0:2*PI
    float angle = atan(sinval, cosval);
    if(angle < 0.0){
        angle = angle + 2.0*PI;
    }
    float anglenorm = angle/(PI*2.0);
    
    //plot figure
    for(int i =0; i < 10; i++){
        if (i >= count - 1) {
            break;
        }
        if(anglenorm >= locations[i] && anglenorm <= locations[i+1]){
            float mixValue = (anglenorm - locations[i])/(locations[i+1] - locations[i]);
            color = mix(colors[i], colors[i+1], mixValue);
            break;
        }
    }
    return color;
}

void main() {
    if (type == 0) {
        gl_FragColor = vec4(colors[0].rgb, alpha);
        return;
    }
    if(abs(startPoint[0] - endPoint[0]) < 0.000001 && abs(startPoint[1] - endPoint[1]) < 0.000001){
        return;
    }
    if(type == 1){
        gl_FragColor = vec4(linearGradientColor(startPoint[0],startPoint[1],endPoint[0],endPoint[1]), alpha);
    } else if(type ==2){
        gl_FragColor = vec4(centerGradientColor(startPoint[0],startPoint[1],endPoint[0],endPoint[1]), alpha);
    } else if(type ==3){
        gl_FragColor = vec4(fanGradientColor(startPoint[0],startPoint[1],endPoint[0],endPoint[1]), alpha);
    }
}

precision mediump float;

uniform float textureWidth;
uniform float textureHeight;
uniform float backWidth;
uniform float backHeight;

uniform sampler2D inputImageTexture0;
uniform sampler2D inputImageTexture1;

// 混合模式枚举
uniform int blendMode;
// 纹理alpha
uniform float textureAlpha;
// 0-显示原图 1-显示纹理
uniform int type;

varying vec2 texcoordOut;      // size of texture (screen)

// 取小数，不知道为什么没有实现，自己实现一个
float frac(float v) {
    return v - floor(v);
}

// floor平铺tile之后，floor上纹理坐标对应的tile上纹理坐标
// 这边场景floor是target，tile是detail
vec2 texcoordTiled2D(float floorWidth, float floorHeight, vec2 floorTexcoord, float tileWidth, float tileHeight) {
    float x = frac(floorWidth / tileWidth * floorTexcoord.x);
    float y = frac(floorHeight / tileHeight * floorTexcoord.y);
    return vec2(x, y);
}


// 正片叠底 Multiply
vec3 blendMultiply(vec3 base, vec3 blend) {
    return base * blend;
}

// 滤色 Screen
vec3 blendScreen(vec3 base, vec3 blend) {
    return vec3((1.0 - ((1.0 - base.r) * (1.0 - blend.r))), (1.0 - ((1.0 - base.g) * (1.0 - blend.g))), (1.0 - ((1.0 - base.b) * (1.0 - blend.b))));
}

// 叠加 Overlay
vec3 blendOverlay(vec3 base, vec3 blend) {
    return vec3(((base.r < 0.5) ? (2.0 * base.r * blend.r) : (1.0 - 2.0 * (1.0 - base.r) * (1.0 - blend.r))), 
                ((base.g < 0.5) ? (2.0 * base.g * blend.g) : (1.0 - 2.0 * (1.0 - base.g) * (1.0 - blend.g))), 
                ((base.b < 0.5) ? (2.0 * base.b * blend.b) : (1.0 - 2.0 * (1.0 - base.b) * (1.0 - blend.b))));
}

// 柔光 Soft Light
vec3 blendSoftLight(vec3 base, vec3 blend) {
    return vec3(((blend.r < 0.5) ? (2.0 * base.r * blend.r + base.r * base.r * (1.0 - 2.0 * blend.r)) : (sqrt(base.r) * (2.0 * blend.r - 1.0) + 2.0 * base.r * (1.0 - blend.r))), 
                ((blend.g < 0.5) ? (2.0 * base.g * blend.g + base.g * base.g * (1.0 - 2.0 * blend.g)) : (sqrt(base.g) * (2.0 * blend.g - 1.0) + 2.0 * base.g * (1.0 - blend.g))), 
                ((blend.b < 0.5) ? (2.0 * base.b * blend.b + base.b * base.b * (1.0 - 2.0 * blend.b)) : (sqrt(base.b) * (2.0 * blend.b - 1.0) + 2.0 * base.b * (1.0 - blend.b))));
}

// 根据混合枚举混合颜色，默认颜色都是预乘过的。
vec4 blendColor(vec4 base, vec4 blend, int mode) {
    vec3 result = vec3(blend.rgb);
    if (mode == 0) {
        // 正常模式
        result = blend.rgb;
    } else if (mode == 1) {
        // 正片叠底
        result = blendMultiply(blend.rgb,base.rgb);
    }  else if (mode == 2) {
        // 滤色。
        result = blendScreen(blend.rgb,base.rgb);
    } else if (mode == 3) {
        // 叠加。
        result = blendOverlay(blend.rgb,base.rgb);
    } else if (mode == 4) {
        // 柔光。
        result = blendSoftLight(blend.rgb,base.rgb);
    }
    return mix(base, vec4(result.rgb, 1.0), blend.a * textureAlpha);
}

void main() {
    if (type == 0) {
        gl_FragColor = texture2D(inputImageTexture0, texcoordOut);
        return;
    }
    vec4 textureColor = texture2D(inputImageTexture1, texcoordTiled2D(textureWidth, textureHeight, texcoordOut, backWidth, backHeight));
    if (textureColor.a != 0.0) {
        textureColor.rgb = textureColor.rgb / textureColor.a;
    }
    vec4 colorColor = texture2D(inputImageTexture0, texcoordOut);
    gl_FragColor = blendColor(colorColor, textureColor, blendMode);
}

        

        

        

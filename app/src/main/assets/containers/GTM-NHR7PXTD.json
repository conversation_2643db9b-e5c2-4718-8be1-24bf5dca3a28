{"fingerprint": "Mw$0", "resource": {"version": "3", "macros": [{"function": "__e", "instance_name": "_event"}, {"function": "__e", "instance_name": "Event Name"}, {"function": "__funcm", "instance_name": "event_timestamp", "vtp_classPath": "com.commsource.statistics.gtm.GetEventTimestamp"}, {"function": "__fup", "instance_name": "gid", "vtp_setDefaultValue": false, "vtp_key": "gid"}, {"function": "__fup", "instance_name": "hwgid", "vtp_setDefaultValue": false, "vtp_key": "hwgid"}, {"function": "__fup", "instance_name": "gtm_user_pseudo_id", "vtp_setDefaultValue": false, "vtp_key": "gtm_user_pseudo_id"}, {"function": "__md", "instance_name": "gtm_session_id", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "gtm_session_id"}, {"function": "__ai", "instance_name": "App ID"}, {"function": "__p", "instance_name": "Platform"}, {"function": "__dn", "instance_name": "Device Name"}, {"function": "__l", "instance_name": "Language"}, {"function": "__ov", "instance_name": "OS Version"}, {"function": "__cid", "instance_name": "Container ID"}, {"function": "__ctv", "instance_name": "Container Version"}, {"function": "__funcm", "instance_name": "app_project_id", "vtp_classPath": "com.commsource.statistics.gtm.GetAppProjectId"}, {"function": "__an", "instance_name": "App Name"}, {"function": "__av", "instance_name": "App Version Code"}, {"function": "__avn", "instance_name": "App Version Name"}], "tags": [{"function": "__img", "once_per_event": true, "vtp_useCacheBuster": false, "vtp_url": ["template", "https://data.pixocial.com/collect/gtm?event_name=", ["escape", ["macro", 1], 12], "&event_timestamp=", ["escape", ["macro", 2], 12], "&gid=", ["escape", ["macro", 3], 12], "&hwgid=", ["escape", ["macro", 4], 12], "&gtm_user_pseudo_id=", ["escape", ["macro", 5], 12], "&gtm_session_id=", ["escape", ["macro", 6], 12], "&app_id=", ["escape", ["macro", 7], 12], "&platform=", ["escape", ["macro", 8], 12], "&device_name=", ["escape", ["macro", 9], 12], "&language=", ["escape", ["macro", 10], 12], "&os_version=", ["escape", ["macro", 11], 12], "&container_id=", ["escape", ["macro", 12], 12], "&container_versoin=", ["escape", ["macro", 13], 12], "&app_project_id=", ["escape", ["macro", 14], 12]], "tag_id": 9}], "predicates": [{"function": "_re", "arg0": ["macro", 0], "arg1": ".*"}], "rules": [[["if", 0], ["add", 0]]]}, "runtime": [[50, "__ai_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationId", [7]]]], [50, "__ai", [46, "data"], [36, ["__ai_main", [15, "data"]]]], [50, "__an_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationName", [7]]]], [50, "__an", [46, "data"], [36, ["__an_main", [15, "data"]]]], [50, "__av_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationVersion", [7]]]], [50, "__av", [46, "data"], [36, ["__av_main", [15, "data"]]]], [50, "__avn_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationVersionName", [7]]]], [50, "__avn", [46, "data"], [36, ["__avn_main", [15, "data"]]]], [50, "__cid_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "containerId", [7]]]], [50, "__cid", [46, "data"], [36, ["__cid_main", [15, "data"]]]], [50, "__ctv_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "containerVersion", [7]]]], [50, "__ctv", [46, "data"], [36, ["__ctv_main", [15, "data"]]]], [50, "__dn_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "deviceName", [7]]]], [50, "__dn", [46, "data"], [36, ["__dn_main", [15, "data"]]]], [50, "__e_main", [46], [41, "a", "b"], [3, "a", [2, [17, [15, "gtmUtils"], "mobile"], "event", [7]]], [22, [1, [1, [15, "a"], [18, [17, [15, "a"], "length"], 0]], [12, [16, [15, "a"], 0], "_"]], [46, [3, "b", [8, "_f", "first_open", "_v", "first_visit", "_iap", "in_app_purchase", "_e", "user_engagement", "_s", "session_start", "_ssr", "session_start_with_rollout", "_au", "app_update", "_ui", "app_remove", "_ab", "app_background", "_ou", "os_update", "_cd", "app_clear_data", "_ae", "app_exception", "_nf", "notification_foreground", "_nr", "notification_receive", "_no", "notification_open", "_nd", "notification_dismiss", "_cmp", "firebase_campaign", "_cmpx", "invalid_campaign", "_vs", "screen_view", "_ar", "ad_reward", "_asr", "app_store_refund", "_assc", "app_store_subscription_convert", "_assr", "app_store_subscription_renew", "_asse", "app_store_subscription_cancel"]], [3, "a", [30, [16, [15, "b"], [15, "a"]], [15, "a"]]]]], [36, [15, "a"]]], [50, "__e", [46, "data"], [36, ["__e_main", [15, "data"]]]], [50, "__funcm_main", [46, "a"], [36, [2, [17, [15, "gtmUtils"], "mobile"], "customMacro", [7, [17, [15, "a"], "classPath"], [30, [2, [17, [15, "gtmUtils"], "common"], "tableToMap", [7, [17, [15, "a"], "functionArgument"], "key", "value"]], [8]]]]]], [50, "__funcm", [46, "data"], [36, ["__funcm_main", [15, "data"]]]], [50, "__fup_main", [46, "a"], [41, "b"], [3, "b", [2, [17, [15, "gtmUtils"], "mobile"], "userProperty", [7, [17, [15, "a"], "key"]]]], [22, [20, [15, "b"], [45]], [46, [22, [21, [17, [15, "a"], "defaultValue"], [44]], [46, [3, "b", [17, [15, "a"], "defaultValue"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", [0, [0, "No user property available for \"", [17, [15, "a"], "key"]], "\" and no default value was defined. Returning \"undefined\"."]]], [36]]]]], [36, [15, "b"]]], [50, "__fup", [46, "data"], [36, ["__fup_main", [15, "data"]]]], [50, "__img_main", [46, "a"], [41, "b", "c", "d", "e", "f"], [22, [29, [40, [17, [15, "a"], "url"]], "string"], [46, [36]]], [3, "b", [17, [15, "a"], "url"]], [3, "c", [17, [15, "a"], "useCacheBuster"]], [22, [15, "c"], [46, [3, "d", [17, [15, "a"], "cacheBusterQueryParam"]], [22, [28, [15, "d"]], [46, [3, "d", "gtmcb"]]], [3, "e", [2, [15, "b"], "char<PERSON>t", [7, [37, [17, [15, "b"], "length"], 1]]]], [3, "f", [39, [19, [2, [15, "b"], "indexOf", [7, "?"]], 0], [39, [30, [12, [15, "e"], "?"], [12, [15, "e"], "&"]], "", "&"], "?"]], [3, "b", [0, [15, "b"], [0, [0, [0, [15, "f"], [15, "d"]], "="], [17, [15, "a"], "randomNumber"]]]]]], [2, [17, [15, "gtmUtils"], "mobile"], "arbitraryPixel", [7, [15, "b"], [44], [44]]]], [50, "__img", [46, "data"], [36, ["__img_main", [15, "data"]]]], [50, "__l_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "language", [7]]]], [50, "__l", [46, "data"], [36, ["__l_main", [15, "data"]]]], [50, "__md_main", [46, "a"], [41, "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l"], [22, [12, [17, [15, "a"], "eventType"], "CUSTOM"], [46, [3, "b", [16, [15, "a"], "key"]]], [46, [22, [12, [17, [15, "a"], "eventType"], "SUGGESTED"], [46, [47, "c", [15, "a"], [46, [3, "d", [2, [15, "c"], "lastIndexOf", [7, "Param"]]], [22, [1, [29, [15, "d"], [27, 1]], [12, [15, "d"], [37, [17, [15, "c"], "length"], 5]]], [46, [22, [20, [15, "b"], [44]], [46, [3, "b", [16, [15, "a"], [15, "c"]]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", [0, [0, [0, "Ignoring unexpected additional parameter ", "key in the data (key = \""], [15, "c"]], "\")."]]]]]]]]]], [46, [22, [20, [17, [15, "a"], "eventType"], [44]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", "Missing expected eventType param"]], [3, "b", [16, [15, "a"], "key"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", [0, "Unexpected eventType param value: ", [17, [15, "a"], "eventType"]]]], [36]]]]]]], [22, [20, [15, "b"], [44]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", "No parameter key specified in the data."]], [36]]], [3, "e", [30, [2, [17, [15, "gtmUtils"], "mobile"], "eventParameters", [7]], [8]]], [22, [12, [2, [15, "b"], "indexOf", [7, "."]], [27, 1]], [46, [3, "f", [16, [15, "e"], [15, "b"]]], [22, [1, [1, [20, [15, "f"], [44]], [12, [17, [15, "a"], "eventType"], "SUGGESTED"]], [17, [15, "a"], "checkEcommerceValue"]], [46, [3, "g", [7, "items", "promotions"]], [47, "h", [15, "g"], [46, [3, "i", [16, [15, "g"], [15, "h"]]], [22, [21, [16, [15, "e"], [15, "i"]], [44]], [46, [3, "j", [16, [15, "e"], [15, "i"]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "j"]]], [46, [22, [21, [16, [16, [15, "j"], 0], [15, "b"]], [44]], [46, [36, [16, [16, [15, "j"], 0], [15, "b"]]]]]], [46, [22, [21, [16, [15, "j"], [15, "b"]], [44]], [46, [36, [16, [15, "j"], [15, "b"]]]]]]]]]]]]]], [46, [3, "k", [2, [17, [15, "gtmUtils"], "common"], "split", [7, [15, "b"], "."]]], [3, "f", [15, "e"]], [47, "l", [15, "k"], [46, [3, "f", [16, [15, "f"], [16, [15, "k"], [15, "l"]]]], [22, [20, [15, "f"], [44]], [46, [4]]]]]]], [22, [21, [15, "f"], [44]], [46, [36, [15, "f"]]], [46, [22, [21, [17, [15, "a"], "defaultValue"], [44]], [46, [36, [17, [15, "a"], "defaultValue"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", [0, [0, "Event does not have parameter \"", [15, "b"]], "\" and no default value was defined. Returning \"undefined\"."]]]]]]]], [50, "__md", [46, "data"], [36, ["__md_main", [15, "data"]]]], [50, "__ov_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "osVersion", [7]]]], [50, "__ov", [46, "data"], [36, ["__ov_main", [15, "data"]]]], [50, "__p_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "platform", [7]]]], [50, "__p", [46, "data"], [36, ["__p_main", [15, "data"]]]], [50, "main", [46, "gtmUtils"], [43, [17, [15, "gtmUtils"], "common"], "tableToMap", [15, "tableToMap"]], [43, [17, [15, "gtmUtils"], "common"], "stringify", [15, "stringify"]], [43, [17, [15, "gtmUtils"], "common"], "copy", [15, "copy"]], [43, [17, [15, "gtmUtils"], "common"], "split", [15, "split"]]], [50, "tableToMap", [46, "tableObj", "keyColumnName", "valueColumnName"], [41, "result", "hasResult", "i"], [3, "result", [8]], [3, "hasResult", false], [3, "i", 0], [42, [1, [15, "tableObj"], [23, [15, "i"], [17, [15, "tableObj"], "length"]]], [33, [15, "i"], [3, "i", [0, [15, "i"], 1]]], false, [46, [22, [1, [1, [16, [15, "tableObj"], [15, "i"]], [2, [16, [15, "tableObj"], [15, "i"]], "hasOwnProperty", [7, [15, "keyColumnName"]]]], [2, [16, [15, "tableObj"], [15, "i"]], "hasOwnProperty", [7, [15, "valueColumnName"]]]], [46, [43, [15, "result"], [16, [16, [15, "tableObj"], [15, "i"]], [15, "keyColumnName"]], [16, [16, [15, "tableObj"], [15, "i"]], [15, "valueColumnName"]]], [3, "hasResult", true]]]]], [36, [39, [15, "hasResult"], [15, "result"], [45]]]], [50, "stringify", [46, "value"], [41, "result", "i", "item", "key"], [22, [20, [15, "value"], [45]], [46, [36, "null"]]], [22, [20, [15, "value"], [44]], [46, [36, [44]]]], [22, [30, [12, [40, [15, "value"]], "number"], [12, [40, [15, "value"]], "boolean"]], [46, [36, [2, [15, "value"], "toString", [7]]]]], [22, [12, [40, [15, "value"]], "string"], [46, [36, [0, [0, "\"", [2, ["split", [15, "value"], "\""], "join", [7, "\\\""]]], "\""]]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "value"]]], [46, [3, "result", [7]], [3, "i", 0], [42, [23, [15, "i"], [17, [15, "value"], "length"]], [33, [15, "i"], [3, "i", [0, [15, "i"], 1]]], false, [46, [3, "item", ["stringify", [16, [15, "value"], [15, "i"]]]], [22, [12, [15, "item"], [44]], [46, [2, [15, "result"], "push", [7, "null"]]], [46, [2, [15, "result"], "push", [7, [15, "item"]]]]]]], [36, [0, [0, "[", [2, [15, "result"], "join", [7, ","]]], "]"]]]], [22, [12, [40, [15, "value"]], "object"], [46, [3, "result", [7]], [47, "key", [15, "value"], [46, [3, "item", ["stringify", [16, [15, "value"], [15, "key"]]]], [22, [29, [15, "item"], [44]], [46, [2, [15, "result"], "push", [7, [0, [0, [0, "\"", [15, "key"]], "\":"], [15, "item"]]]]]]]], [36, [0, [0, "{", [2, [15, "result"], "join", [7, ","]]], "}"]]]], [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", "Attempting to stringify unknown type!"]], [36, [44]]], [50, "split", [46, "str", "delimiter"], [41, "result", "len", "i", "index"], [3, "result", [7]], [22, [20, [15, "delimiter"], ""], [46, [3, "len", [17, [15, "str"], "length"]], [3, "i", 0], [42, [23, [15, "i"], [15, "len"]], [33, [15, "i"], [3, "i", [0, [15, "i"], 1]]], false, [46, [2, [15, "result"], "push", [7, [16, [15, "str"], [15, "i"]]]]]], [36, [15, "result"]]]], [42, [1, [15, "str"], [19, [2, [15, "str"], "indexOf", [7, [15, "delimiter"]]], 0]], [46], false, [46, [3, "index", [2, [15, "str"], "indexOf", [7, [15, "delimiter"]]]], [22, [12, [15, "index"], 0], [46, [2, [15, "result"], "push", [7, ""]]], [46, [2, [15, "result"], "push", [7, [2, [15, "str"], "substring", [7, 0, [15, "index"]]]]]]], [3, "str", [2, [15, "str"], "substring", [7, [0, [15, "index"], [17, [15, "delimiter"], "length"]]]]]]], [2, [15, "result"], "push", [7, [15, "str"]]], [36, [15, "result"]]], [50, "copy", [46, "from", "to"], [41, "property", "fromProperty"], [3, "to", [30, [15, "to"], [39, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "from"]]], [7], [8]]]], [47, "property", [15, "from"], [46, [3, "fromProperty", [16, [15, "from"], [15, "property"]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "fromProperty"]]], [46, [22, [28, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [16, [15, "to"], [15, "property"]]]]], [46, [43, [15, "to"], [15, "property"], [7]]]], [43, [15, "to"], [15, "property"], ["copy", [15, "fromProperty"], [16, [15, "to"], [15, "property"]]]]], [46, [22, [1, [29, [15, "fromProperty"], [45]], [12, [40, [15, "fromProperty"]], "object"]], [46, [22, [29, [40, [16, [15, "to"], [15, "property"]]], "object"], [46, [43, [15, "to"], [15, "property"], [8]]]], [43, [15, "to"], [15, "property"], ["copy", [15, "fromProperty"], [16, [15, "to"], [15, "property"]]]]], [46, [43, [15, "to"], [15, "property"], [15, "fromProperty"]]]]]]]], [36, [15, "to"]]]]}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ID</key>
	<integer>1001</integer>
	<key>FilterPart</key>
	<array>
		<dict>
			<key>Type</key>
			<string>AnattaBeauty</string>
			<key>Filters</key>
			<array>
				<dict>
					<key>Name</key>
					<string>SourceInput</string>
				</dict>
				<dict>
					<key>Name</key>
					<string>SkinSmooth</string>
					<key>Type</key>
					<string>Normal</string>
					<key>IsNeedFaceAndNeckMask</key>
					<string>1</string>
					<key>Alpha</key>
					<string>0.8</string>
					<key>Switch</key>
                    <string>1</string>
					<key>FromSource</key>
					<array>
						<string>SourceInput</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>Sharpen</string>
					<key>Type</key>
					<string>MySharpenSkinMask</string>
					<key>Alpha</key>
					<string>0.2</string>
					<key>ExceptFaceSkin</key>
					<string>0</string>
					<key>SharpenAdaptType</key>
					<string>2</string>
					<key>Switch</key>
                    <string>1</string>
					<key>FromSource</key>
					<array>
						<string>SkinSmooth</string>
						<string>SourceInput</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>FaceColor</string>
					<key>Type</key>
					<string>SingleLookup</string>
					<key>Size</key>
					<string>64</string>
					<key>AutoContrastType</key>
					<string>2</string>
					<key>AutoContrastSwitch</key>
					<string>1</string>
					<key>AutoContrastAlpha</key>
					<string>1</string>
					<key>DarkLookupSwitch</key>
                    <string>1</string>
					<key>NeedSkinMaskMix</key>
					<string>0</string>
					<key>LutPath</key>
					<string>Anatta/lookup_table_512_smooth.png</string>
					<key>Alpha</key>
                    <string>0.5</string>
					<key>FromSource</key>
					<array>
						<string>Sharpen</string>
					</array>
				</dict>
				<dict>
					<key>Name</key>
					<string>ResultOutput</string>
					<key>FromSource</key>
					<array>
						<string>FaceColor</string>
					</array>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.commsource.beautyplus"
    android:installLocation="auto">

    <uses-feature android:glEsVersion="0x00020000" /> <!-- To handle the reselection within the app on Android 14 (API level 34) -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" /> <!-- Devices running Android 13 (API level 33) or higher -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Devices running Android 12L (API level 32) or lower -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission
        android:name="${PERMISSION_SYSTEM_ALERT_WINDOW}"
        tools:node="merge" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <permission
        android:name="com.commsource.beautyplus${suffix}.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.commsource.beautyplus${suffix}.permission.C2D_MESSAGE" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- In-app Billing -->
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <queries>

        <!-- 手机管家 -->
        <package android:name="com.meizu.media.gallery" />
        <package android:name="com.google.android.webview" />
        <package android:name="com.qihoo360.mobilesafe" />
        <package android:name="com.qihoo.antivirus" />
        <package android:name="com.tencent.qqpimsecure" />
        <package android:name="com.lbe.security" />
        <package android:name="com.lenovo.safecenter" />
        <package android:name="com.kingroot.master" />
        <package android:name="com.yulong.android.security" />
        <package android:name="com.yulong.android.seccenter" />
        <package android:name="com.iqoo.secure" />
        <package android:name="com.zte.heartyservice" />
        <package android:name="com.mediatek.security" />
        <package android:name="com.coloros.safecenter" />
        <package android:name="com.ss.android.ugc.trill" />

        <!-- 分享相关 -->
        <package android:name="com.facebook.katana" />
        <package android:name="com.twitter.android" />
        <package android:name="com.instagram.android" />
        <package android:name="com.whatsapp" />
        <package android:name="jp.naver.line.android" />
        <package android:name="com.tencent.mm" />
        <package android:name="com.kakao.talk" />
        <package android:name="com.sina.weibo" />
        <package android:name="com.projectgoth" />
        <package android:name="tv.cchan.harajuku" />

        <!-- 矩阵互推 -->
        <package android:name="com.meitu.makeup" />

        <!-- 谷歌相关 -->
        <package android:name="com.google.ar.core" />
        <package android:name="com.google.android.gms" />
        <package android:name="com.google.android.webview" />

        <!-- tiktok分享 -->
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />

        <!-- facebook -->
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />

        <!-- 吊起邮件客户端 -->
        <intent>
            <action android:name="android.intent.action.SENDTO" />

            <data android:scheme="mailto" />
        </intent>

        <!-- 吊起浏览器 -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
    </queries>

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:resizeable="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <application
        android:name=".BeautyPlusApplication"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="${app_name}"
        android:largeHeap="true"
        android:networkSecurityConfig="${NET_CONFIG}"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/theme"
        tools:ignore="LockedOrientationActivity"
        tools:replace="android:label,allowBackup,fullBackupContent,networkSecurityConfig,supportsRtl">
        <!-- https://developers.google.com/admob/android/optimize-initialization?hl=zh-cn -->
<!--        <meta-data-->
<!--            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"-->
<!--            android:value="true" />-->

        <meta-data android:name='com.facebook.sdk.AutoLogAppEventsEnabled'
            android:value='false'/>

        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBvzvKJJq0yfHQ-FyUgAltRqntv1nU_a0U" /> <!-- 分享页面适配方案 -->
        <meta-data
            android:name="design_width"
            android:value="780" />
        <meta-data
            android:name="design_height"
            android:value="1688" /> <!-- 华为刘海屏区域使用 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" /> <!-- Admob -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_app_id" /> <!-- DFP -->
        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" /> <!-- 性能监控SDK不自动初始化 -->
        <meta-data
            android:name="APP_CIA_AUTO_INIT"
            android:value="false" />

        <activity
            android:name=".start.StartUpAdvertActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="${start_up_theme}">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:icon="@mipmap/ic_launcher">
                <action android:name="android.intent.action.EDIT" />
                <action android:name="android.intent.action.SEND" />
                <action android:name="com.commsource.beautyplus.intent.action.EDIT" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="image/*" />
            </intent-filter>
            <intent-filter android:icon="@mipmap/ic_launcher">
                <action android:name="android.intent.action.EDIT" />
                <action android:name="android.intent.action.SEND" />
                <action android:name="com.commsource.beautyplus.intent.action.VIDEO_EDIT" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="video/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.media.action.IMAGE_CAPTURE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.media.action.STILL_IMAGE_CAMERA" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".setting.test.QuadrangleAdjustDemoActivity" />
        <activity
            android:name=".web.WebActivity"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".setting.language.LanguageActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".web.CropPhotoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.test.TestFuncActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".setting.test.DeeplinkTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".setting.test.TestCIAActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.test.AdTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.test.SubscribeConfigActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustUnspecified|stateHidden" /> <!-- 设置页面 -->
        <activity
            android:name=".setting.SettingActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <!--
        android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
                    适配的折叠屏的配置，保证Activity在折叠屏手机切换的时候，不销毁Activity,别的地方需要用到，需要页面适配
        -->
        <activity
            android:name="com.commsource.homev3.HomeV3Activity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" /> <!-- 关于我 -->
        <activity
            android:name="com.commsource.homev3.work.WorkActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.about.AboutActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" /> <!-- 帮助页面 -->
        <activity
            android:name="com.commsource.help.HelpActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.help.TicketActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.help.HelpArticleActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.help.HelpCenterWebActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.ccpa.CCPAActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" /> <!-- 拼图页面 -->
        <activity
            android:name="com.commsource.puzzle.patchedworld.frame.PuzzleActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.camera.xcamera.CameraNewActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/fullScreenAppCompat"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.camera.common.CameraContainerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/noAnimation"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.camera.xcamera.stamp_model.StampCameraActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/noAnimation"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.camera.xcamera.idphoto.BaseCapacitorActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.camera.xcamera.idphoto.IDPhotoCameraActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/fullScreenAppCompat"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".web.CrashFeedBackActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <receiver
            android:name="com.commsource.push.NotificationBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.commsource.beautyplus.ClickNotification" />
                <action android:name="com.commsource.beautyplus.PushNotification" />
                <action android:name="com.commsource.beautyplus.TenDaysPushNotification" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/push" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/color_ff6e8d" />
        <meta-data
            android:name="applovin.sdk.key"
            android:value="GkEdZcCupQJQsSE36JC0Bem7WIVoFfdklWeDTMbB0OX1w_6v6E97d8FQTJhm6zU7cbTLPIpffequPC1P3WWdKw" />

        <service
            android:name=".fcm.BPFirebaseMessagingService"
            android:exported="false"
            android:process=":light">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.amazonaws.mobileconnectors.s3.transferutility.TransferService"
            android:enabled="true" />
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:exported="false"
            tools:node="remove">
            <intent-filter android:priority="-500">
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND"
            android:process=":light"
            tools:node="replace">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver> <!-- 处理Push scheme的服务 -->
        <activity
            android:name=".PushSchemeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/translucent_no_full_screen_orientation">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="beautyplus" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="beautyplus.go.link"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:host="rizwue.allapp.link" />
                <data android:scheme="rizwue" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ProcessFBPushActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/translucent_no_full_screen_orientation" />

        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/fb_client_token" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" /> <!-- This meta-data tag is required to use Google Play Services. -->
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.kakao.sdk.AppKey"
            android:value="@string/kakao_app_key" /> <!-- <meta-data -->
        <!-- android:name="AF_PRE_INSTALL_NAME" -->
        <!-- android:value="${CHANNEL_VALUE}" /> -->
        <!-- Include the AdActivity configChanges and theme. -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:theme="@style/googleAdActivity"
            tools:replace="android:theme" />
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:theme" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="${facebook_id}"
            android:exported="true" /> <!-- API 24 文件分享 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.commsource.beautyplus${suffix}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name=".setting.camerasetting.ImageQualitySettingActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:label="@string/setting"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name=".setting.country.ChooseCountryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/setting"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" /> <!-- 分类版本 高级美颜 -->
        <!-- 高级美颜 - 帮助视频 - 透明主题 -->
        <activity
            android:name="com.commsource.beautymain.activity.BeautyHelpActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/settingtheme" />

        <receiver
            android:name="com.meitu.webview.download.DownloadCompletedReceiver"
            android:exported="false"
            tools:node="merge" />

        <activity
            android:name="im.dino.dbinspector.activities.DbInspectorActivity"
            android:exported="true"
            tools:node="merge" />

        <receiver
            android:name=".ChangeLanguageBroadcastReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.LOCALE_CHANGED" />
            </intent-filter>
        </receiver>

        <activity android:name="com.commsource.camera.BaseCameraConfirmActivity" /> <!-- 特效中心 -->
        <activity
            android:name=".setting.abtest.ABTestSettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.commsource.advertisiting.ImageShareAdvertActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.meitu.hwbusinesskit.core.web.AdBrowserActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize" /> <!-- 因GDPR协议，Firebase禁用自动初始化FCM和Analytics -->
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="true" />

        <activity
            android:name="com.commsource.billing.activity.ProActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.billing.credit.CreditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.billing.credit.detail.CreditDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".location.LocationSearchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.aieditor.AiEditorActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.aieditor.AiEditorProcessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.airepair.imagequality.ImageLandingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.airepair.imagequality.image.ImageResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.album.AlbumActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme"
            tools:replace="android:theme" />
        <activity
            android:name="com.commsource.studio.ImageStudioActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.videostudio.VideoStudioActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.ad.InternalRewardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.store.filter.FilterStoreActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" /> <!-- 重写配置，不支持横屏 -->
        <activity
            android:name="com.commsource.store.doodle.DoodleStoreActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".onboarding.NewOnBoardingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.studio.sticker.CustomStickerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.studio.formula.FormulaStoreActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.test.DialogTestActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.test.ThumbnailTestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.meitu.http.HttpCacheActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.duffle.sticker.StudioStiStoreActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" /> <!-- 配方页面中间页 -->
        <activity
            android:name="com.commsource.duffle.formula.FormulaMidActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|smallestScreenSize|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Transparent" />
        <activity
            android:name="com.commsource.airepair.VideoAiRepairGuideActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.airepair.VideoAiRepairActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.homev2.topic.TopicActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.duffle.formula.detail.FormulaDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Transparent"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="com.commsource.airepair.easytimeline.VideoCropActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.commsource.batch.BatchEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name="com.commsource.ad.adtaskcenter.AdTaskCenterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/MD_Theme" />
        <activity
            android:name="com.commsource.framecut.VideoFrameCutActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.commsource.mockup.MockupActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name="com.commsource.mockup.RetroConfirmActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.commsource.airepair.imagequality.video.crop.VideoCropActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.commsource.airepair.imagequality.video.process.VideoProcessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.commsource.airepair.imagequality.video.preview.VideoFramePreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <service android:name=".setting.test.TestFuncFloatingWinService" />
        <service android:name=".setting.glleakplugin.FloatingWindowService" />
    </application>

</manifest>